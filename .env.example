# API configuration
API_KEY=your_secret_api_key_here

# App configuration
DEBUG=false
LOG_LEVEL=INFO
# API Base URL (optional, defaults to same origin)
VITE_API_BASE_URL=http://localhost:8000

# Enable debug logging
VITE_DEBUG=true

# S3 configuration
S3_ACCESS_KEY=your_s3_access_key_here
S3_SECRET_KEY=your_s3_secret_key_here
S3_BUCKET_NAME=your_s3_bucket_name_here
S3_REGION=your_s3_region_here
S3_ENDPOINT_URL=your_s3_endpoint_here

# Database Configuration (PostgreSQL)
DATABASE_URL=postgresql+asyncpg://postgres:your_password@postgres:5432/ouinhi
POSTGRES_DB=ouinhi
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_postgres_password_here

# Redis configuration
REDIS_PASSWORD=your_secure_redis_password_here
REDIS_URL=redis://:your_secure_redis_password_here@redis:6379/0

ADMIN_USERNAME=your_admin_username
ADMIN_PASSWORD=your_secure_admin_password
JWT_SECRET_KEY=

# Custom auth credentials  
DEFAULT_USERNAME=
DEFAULT_PASSWORD=

# Kokoro TTS configuration
KOKORO_API_URL=http://kokoro-tts:8880/v1/audio/speech
KOKORO_TIMEOUT=120
KOKORO_MAX_TEXT_LENGTH=1000

# OpenAI API Configuration
# Required: Get your API key from https://platform.openai.com/account/api-keys

OPENAI_API_KEY=dummy_key_for_ollama
OPENAI_BASE_URL=http://localhost:11434/v1
OPENAI_MODEL=llama3.1:8b

Together AI (Cloud)

OPENAI_API_KEY=your_together_api_key
OPENAI_BASE_URL=https://api.together.xyz/v1
OPENAI_MODEL=meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo

OpenRouter (Multiple Providers)

OPENAI_API_KEY=sk-or-v1-your_openrouter_key
OPENAI_BASE_URL=https://openrouter.ai/api/v1
OPENAI_MODEL=meta-llama/llama-3.1-8b-instruct:free

# Pexels API Configuration  
# Required: Get your API key from https://www.pexels.com/api/
PEXELS_KEY=your_pexels_api_key_here

# Groq API Configuration (Optional)
# Optional: If provided (>30 characters), will use Groq instead of OpenAI
# Get your API key from https://console.groq.com/keys
GROQ_API_KEY=your_groq_key                    # Your Groq API key
GROQ_MODEL=mixtral-8x7b-32768    

# Application Configuration
# Default video server for background videos (pexel is currently supported)
VIDEO_SERVER=pexel

# MoviePy and ImageMagick Configuration
# Path to ImageMagick binary (auto-detected in Docker)
IMAGEMAGICK_BINARY=/usr/bin/convert

# Temporary directory for MoviePy operations
MOVIEPY_TEMP_DIR=/tmp/moviepy

# Logging Configuration
# Directory paths for storing API response logs
DIRECTORY_LOG_GPT=.logs/gpt_logs
DIRECTORY_LOG_PEXEL=.logs/pexel_logs

GOOGLE_SEARCH_API_KEY=
GOOGLE_SEARCH_ENGINE_ID=

PERPLEXITY_API_KEY=your_perplexity_api_key_here
PERPLEXITY_MODEL=sonar

PIXABAY_API_KEY=your_pixabay_api_key_here

# External TTS API Configuration (Optional - for compatibility)
# Note: Ouinhi primarily uses internal Kokoro TTS service
TTS_API_KEY=your_external_tts_api_key_here
TTS_API_URL=https://your-tts-service.com/api

# Tesseract (for OCR)
TESSERACT_PATH=/usr/bin/tesseract

# Set once, use everywhere
TOGETHER_API_KEY=your_together_api_key
TOGETHER_DEFAULT_MODEL=black-forest-labs/FLUX.1-schnell
TOGETHER_DEFAULT_WIDTH=576
TOGETHER_DEFAULT_HEIGHT=1024
TOGETHER_DEFAULT_STEPS=4

# Rate limiting configuration
TOGETHER_MAX_RPS=2              # Max requests per second
TOGETHER_MAX_CONCURRENT=3       # Max concurrent requests  
TOGETHER_RETRY_ATTEMPTS=3       # Retry attempts on failure
TOGETHER_BASE_DELAY=1.0         # Base delay between retries

# Short Video Creation & MCP Configuration
# MCP server for AI agent integration (true/false)
MCP_ENABLED=true
# Frontend React UI for manual video creation (true/false)
FRONTEND_ENABLED=true
# Default TTS voice for short video creation (kokoro voices: af_bella, af_nicole, etc.)
DEFAULT_SHORT_VIDEO_VOICE=af_bella
# Default video resolution for short videos (portrait format recommended)
DEFAULT_SHORT_VIDEO_RESOLUTION=1080x1920
# Default video frame rate for short videos
DEFAULT_SHORT_VIDEO_FPS=30
# Optional: Custom segment duration for background videos (2.0-8.0 seconds)
DEFAULT_SEGMENT_DURATION=3.0

# Web Frontend Configuration
# Base URL for the frontend (used in development)
FRONTEND_BASE_URL=http://localhost:8000
# API endpoint for MCP connections (used by frontend)
MCP_ENDPOINT=http://localhost:8000/mcp/sse

# POSTIZ
POSTIZ_API_KEY=
POSTIZ_API_URL=
POSTIZ_MCP_URL=

