#!/usr/bin/env python3
"""
Debug script to test Pexels API directly and see what's happening.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_pexels_api():
    """Test Pexels API directly to debug the issue."""
    
    # Get API key
    api_key = os.getenv('PEXELS_API_KEY')
    if not api_key:
        print("❌ PEXELS_API_KEY not found in environment variables")
        return False
    
    print(f"✅ PEXELS_API_KEY found (length: {len(api_key)})")
    
    # Test API endpoint
    base_url = "https://api.pexels.com/v1"
    headers = {
        "Authorization": api_key,
        "User-Agent": "Media-API/1.0"
    }
    
    # Test queries that should definitely return results
    test_queries = [
        "city skyline sunset",
        "nature",
        "landscape", 
        "sunset",
        "city",
        "sky"
    ]
    
    for query in test_queries:
        print(f"\n🔍 Testing query: '{query}'")
        
        url = f"{base_url}/search"
        params = {
            "query": query,
            "orientation": "landscape",
            "per_page": 5
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=30)
            print(f"   Status code: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                photos = data.get('photos', [])
                total_results = data.get('total_results', 0)
                
                print(f"   ✅ Success: {len(photos)} photos returned, {total_results} total available")
                
                if photos:
                    # Show details of first photo
                    first_photo = photos[0]
                    print(f"   📸 First photo: {first_photo.get('width')}x{first_photo.get('height')}")
                    print(f"   🔗 URL: {first_photo.get('url', 'N/A')}")
                    print(f"   📥 Download URL: {first_photo.get('src', {}).get('original', 'N/A')}")
                else:
                    print(f"   ⚠️  No photos returned for query '{query}'")
                    
            elif response.status_code == 401:
                print(f"   ❌ Authentication failed - check API key")
                print(f"   Response: {response.text}")
                return False
            elif response.status_code == 429:
                print(f"   ⚠️  Rate limited")
                print(f"   Response: {response.text}")
            else:
                print(f"   ❌ Error: {response.status_code}")
                print(f"   Response: {response.text}")
                
        except requests.RequestException as e:
            print(f"   ❌ Request failed: {e}")
            
    return True

def test_pexels_service():
    """Test the actual Pexels service implementation."""
    print("\n" + "="*50)
    print("Testing Pexels Service Implementation")
    print("="*50)
    
    # Add the app directory to Python path
    sys.path.insert(0, '/app')
    
    try:
        from app.services.ai.pexels_image_service import pexels_image_service
        
        print(f"✅ Pexels service imported successfully")
        print(f"✅ Service available: {pexels_image_service.is_available()}")
        print(f"✅ API key configured: {bool(pexels_image_service.api_key)}")
        
        if pexels_image_service.api_key:
            print(f"✅ API key length: {len(pexels_image_service.api_key)}")
        
        # Test search
        import asyncio
        
        async def test_search():
            params = {
                "query": "city skyline sunset",
                "per_page": 5,
                "orientation": "landscape",
                "quality": "high"
            }
            
            try:
                result = await pexels_image_service.search_images(params)
                images = result.get('images', [])
                total_results = result.get('total_results', 0)
                
                print(f"✅ Search successful: {len(images)} images returned, {total_results} total")
                
                if images:
                    first_image = images[0]
                    print(f"📸 First image: {first_image.get('width')}x{first_image.get('height')}")
                    print(f"🔗 Download URL: {first_image.get('download_url', 'N/A')}")
                else:
                    print("⚠️  No images returned from service")
                    
            except Exception as e:
                print(f"❌ Search failed: {e}")
                import traceback
                traceback.print_exc()
        
        asyncio.run(test_search())
        
    except ImportError as e:
        print(f"❌ Failed to import Pexels service: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing Pexels service: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 Pexels API Debug Tool")
    print("="*50)
    
    # Test direct API
    print("Testing Pexels API directly...")
    api_success = test_pexels_api()
    
    if api_success:
        # Test service implementation
        test_pexels_service()
    else:
        print("\n❌ API test failed, skipping service test")
