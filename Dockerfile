# Frontend build stage  
FROM node:18-alpine as frontend-builder
WORKDIR /frontend

# Copy package files and install dependencies
COPY frontend/package.json ./
RUN npm install --legacy-peer-deps

# Copy source and build
COPY frontend/ ./
RUN npm run build

# Base stage with dependencies
FROM python:3.12-slim as base

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ffmpeg tesseract-ocr tesseract-ocr-eng build-essential \
    wget git fontconfig curl ca-certificates && \
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies first (cached if unchanged)
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Create font directory and copy fonts (cached - done before app code)
RUN mkdir -p /usr/share/fonts/truetype/custom
COPY fonts/*.ttf /usr/share/fonts/truetype/custom/
RUN fc-cache -f -v

# Create temp directories (cached - done before app code)
RUN mkdir -p /app/temp/output

# Development stage
FROM base as development
# Install development dependencies
RUN pip install --no-cache-dir uvicorn[standard] watchdog

# Copy application code
COPY app/ ./app/
COPY *.py ./

# Copy scripts and make them executable
COPY scripts/ ./scripts/
RUN chmod +x ./scripts/*.sh

# Copy static files (music, voices, etc.)
COPY app/static/ ./static/
# Create backup copy for volume initialization
COPY app/static/ ./static_backup/

# Copy frontend source for development builds
COPY frontend/ ./frontend/

# Build frontend for development (with hot reload support)
WORKDIR /app/frontend
RUN npm install
RUN npm run build

WORKDIR /app

# Use reload for development
CMD ["sh", "-c", "./scripts/init-music.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app/app"]

# Production stage
FROM base as production
# Copy application code in specific order to maximize caching
COPY app/ ./app/
COPY *.py ./

# Copy scripts and make them executable
COPY scripts/ ./scripts/
RUN chmod +x ./scripts/*.sh

# Copy static files (music, voices, etc.)
COPY app/static/ ./static/
# Create backup copy for volume initialization
COPY app/static/ ./static_backup/

# Copy built frontend from the frontend-builder stage
COPY --from=frontend-builder /frontend/dist ./frontend/dist

# Expose the port the app runs on
EXPOSE 8000
# Command to run the application
CMD ["sh", "-c", "./scripts/init-music.sh && uvicorn app.main:app --host 0.0.0.0 --port 8000"] 