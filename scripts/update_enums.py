#!/usr/bin/env python3
"""
Database enum update script to add missing FOOTAGE_TO_VIDEO enum values.
"""
import asyncio
import os
import sys
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.database import database_service
from sqlalchemy import text
from loguru import logger

async def update_enums():
    """Update database enums to include FOOTAGE_TO_VIDEO values."""
    try:
        await database_service.initialize()
        
        async with database_service.engine.begin() as conn:
            # Check current enum values
            logger.info("Checking current enum values...")
            
            # Check mediacategory enum
            result = await conn.execute(text("""
                SELECT enumlabel FROM pg_enum 
                WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'mediacategory')
                ORDER BY enumlabel;
            """))
            media_categories = [row[0] for row in result]
            logger.info(f"Current mediacategory values: {media_categories}")
            
            # Check videotype enum  
            result = await conn.execute(text("""
                SELECT enumlabel FROM pg_enum 
                WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'videotype')
                ORDER BY enumlabel;
            """))
            video_types = [row[0] for row in result]
            logger.info(f"Current videotype values: {video_types}")
            
            # Add FOOTAGE_TO_VIDEO to mediacategory if missing
            if 'footage_to_video' not in media_categories:
                logger.info("Adding 'footage_to_video' to mediacategory enum...")
                await conn.execute(text("""
                    ALTER TYPE mediacategory ADD VALUE 'footage_to_video';
                """))
                logger.info("✅ Added 'footage_to_video' to mediacategory")
            else:
                logger.info("'footage_to_video' already exists in mediacategory")
            
            # Add FOOTAGE_TO_VIDEO to videotype if missing
            if 'footage_to_video' not in video_types:
                logger.info("Adding 'footage_to_video' to videotype enum...")
                await conn.execute(text("""
                    ALTER TYPE videotype ADD VALUE 'footage_to_video';
                """))
                logger.info("✅ Added 'footage_to_video' to videotype")
            else:
                logger.info("'footage_to_video' already exists in videotype")
                
            await conn.commit()
            logger.info("✅ Database enum update completed successfully")
            
    except Exception as e:
        logger.error(f"❌ Failed to update database enums: {e}")
        raise
    finally:
        await database_service.close()

if __name__ == "__main__":
    asyncio.run(update_enums())