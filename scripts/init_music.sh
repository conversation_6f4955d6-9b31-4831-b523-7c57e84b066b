#!/bin/bash
# Initialize music files in the persistent volume

MUSIC_DIR="/app/static/music"
BACKUP_MUSIC_DIR="/app/static_backup/music"

# Check if music directory is empty (indicating first run or volume was reset)
if [ ! "$(ls -A $MUSIC_DIR 2>/dev/null)" ]; then
    echo "Music directory is empty, copying music files from backup..."
    
    # Create the music directory if it doesn't exist
    mkdir -p "$MUSIC_DIR"
    
    # Copy music files from backup location to persistent volume
    if [ -d "$BACKUP_MUSIC_DIR" ]; then
        cp -r "$BACKUP_MUSIC_DIR/"* "$MUSIC_DIR/"
        echo "Music files copied successfully"
        echo "Music files available: $(ls -la $MUSIC_DIR | wc -l) files"
    else
        echo "Warning: Backup music directory not found at $BACKUP_MUSIC_DIR"
    fi
else
    echo "Music directory already populated with $(ls -1 $MUSIC_DIR | wc -l) files"
fi