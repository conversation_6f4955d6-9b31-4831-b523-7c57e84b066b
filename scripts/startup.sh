#!/bin/bash
set -e

echo "🚀 Ouinhi API Startup Script"
echo "================================"

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
timeout=30
counter=0

while ! pg_isready -h postgres -p 5432 -U postgres >/dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -gt $timeout ]; then
        echo "❌ PostgreSQL connection timeout after ${timeout}s"
        exit 1
    fi
done

echo "✅ PostgreSQL is ready!"

# Wait for Redis to be ready
echo "⏳ Waiting for Redis to be ready..."
counter=0

while ! redis-cli -h redis -p 6379 -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; do
    sleep 1
    counter=$((counter + 1))
    if [ $counter -gt $timeout ]; then
        echo "❌ Redis connection timeout after ${timeout}s"
        exit 1
    fi
done

echo "✅ Redis is ready!"

# Run database initialization
echo "🔧 Running database initialization..."
cd /app
python scripts/init_database.py

# Start the main application
echo "🚀 Starting Ouinhi API server..."
if [ "${DEBUG:-false}" = "true" ]; then
    echo "🐛 Debug mode enabled - starting with reload"
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
else
    echo "🏃 Production mode - starting optimized server"
    exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
fi