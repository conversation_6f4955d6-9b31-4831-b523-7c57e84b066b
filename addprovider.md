Adding More AI Video Providers:

  To add another AI video provider (let's say "RunwayML" or "Pika Labs"), you would:

  1. Add to constants in contentCreation.ts:
  export const AI_VIDEO_PROVIDERS = ['ltx_video', 'wavespeed', 'runway'] as const;

  2. Add backend support in footage_to_video_pipeline.py:
  async def _generate_ai_background_video(self, ...):
      if ai_video_provider == "runway":
          return await self._generate_runway_video(...)
      elif ai_video_provider == "wavespeed":
          return await self._generate_wavespeed_video(...)
      else:  # Default to LTX-Video
          return await self._generate_ltx_video(...)

  3. Update UI labels in BackgroundVideoSettings.tsx:
  {provider === 'runway' ? 'RunwayML Gen-3' :
   provider === 'ltx_video' ? 'LTX-Video' : 'WaveSpeed AI'}
