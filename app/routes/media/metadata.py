from fastapi import APIRouter, HTTPException, Depends, status
from pydantic import BaseModel, HttpUrl
from typing import Any, Dict
import logging

from app.services.job_queue import job_queue
from app.services.media.metadata import metadata_service
from app.models import JobType, JobStatusResponse, JobStatus, JobResponse
from app.utils.auth import get_api_key
import uuid

router = APIRouter()
logger = logging.getLogger(__name__)

class MetadataRequest(BaseModel):
    media_url: HttpUrl

class MetadataResponse(BaseModel):
    filesize: int
    filesize_mb: float
    duration: float | None = None
    duration_formatted: str | None = None
    format: str | None = None
    overall_bitrate: int | None = None
    overall_bitrate_mbps: float | None = None
    has_video: bool
    has_audio: bool
    
    # Video properties (if present)
    video_codec: str | None = None
    video_codec_long: str | None = None
    width: int | None = None
    height: int | None = None
    resolution: str | None = None
    fps: float | None = None
    video_bitrate: int | None = None
    video_bitrate_mbps: float | None = None
    pixel_format: str | None = None
    
    # Audio properties (if present)
    audio_codec: str | None = None
    audio_codec_long: str | None = None
    audio_channels: int | None = None
    audio_sample_rate: int | None = None
    audio_sample_rate_khz: float | None = None
    audio_bitrate: int | None = None
    audio_bitrate_kbps: float | None = None

@router.post("/metadata", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def extract_metadata(
    request: MetadataRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Extract metadata from a media file including video/audio properties.
    
    This endpoint analyzes a media file and returns comprehensive metadata including:
    - File size and format information
    - Duration and overall bitrate
    - Video properties (codec, resolution, frame rate, bitrate)
    - Audio properties (codec, channels, sample rate, bitrate)
    """
    job_id = str(uuid.uuid4())
    
    try:
        async def process_wrapper(_job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
            media_url = data["media_url"]
            metadata = await metadata_service.get_metadata(media_url, _job_id)
            return metadata
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.METADATA_EXTRACTION,
            process_func=process_wrapper,
            data={"media_url": str(request.media_url)}
        )
        
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        logger.error(f"Failed to create metadata extraction job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to process request: {str(e)}")

@router.get("/metadata/{job_id}", response_model=JobStatusResponse)
async def get_metadata_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status and result of a metadata extraction job"""
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )