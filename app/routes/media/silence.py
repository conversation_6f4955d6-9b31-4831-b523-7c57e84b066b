"""
Routes for silence/speech detection in media files.
"""
import logging
import uuid
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status, Depends
from pydantic import BaseModel

from app.utils.auth import get_api_key
from app.services.job_queue import job_queue, JobType
from app.services.media.silence_service import silence_service
from app.models import (
    JobResponse, JobStatusResponse, 
    MediaSilenceRequest, MediaSilenceResult,
    MediaAnalyzeRequest, MediaAnalyzeResult
)

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/silence", tags=["media"])

@router.post("/", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_silence_detection_job(
    request: MediaSilenceRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a job for silence/speech detection in media files.
    
    This endpoint provides advanced silence detection with Voice Activity Detection (VAD)
    support for better speech/silence boundary detection. It can detect either:
    
    - **Speech segments** (when use_advanced_vad=True): Returns time ranges where speech is detected
    - **Silence intervals** (when use_advanced_vad=False): Returns time ranges where silence is detected
    
    The advanced VAD method uses machine learning-based audio analysis for more accurate
    speech detection, while the legacy method uses FFmpeg's built-in silence detection.
    
    Args:
        request: The request containing media URL and detection parameters
        api_key: API key for authentication
        
    Returns:
        A JobResponse object with the job ID and initial status
        
    Raises:
        HTTPException: If the job cannot be created
    """
    # Validate input
    if not request.media_url:
        raise HTTPException(status_code=400, detail="No media URL provided")
    
    # Create job data
    job_data = {
        "media_url": str(request.media_url),
        "start": request.start,
        "end": request.end,
        "noise": request.noise,
        "duration": request.duration,
        "mono": request.mono,
        "volume_threshold": request.volume_threshold,
        "use_advanced_vad": request.use_advanced_vad,
        "min_speech_duration": request.min_speech_duration,
        "speech_padding_ms": request.speech_padding_ms,
        "silence_padding_ms": request.silence_padding_ms
    }
    
    # Create a job
    job_id = str(uuid.uuid4())
    
    try:
        # Create wrapper function to match job queue signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await silence_service.process_silence_job(_job_id, data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_SILENCE_DETECTION,
            process_func=process_wrapper,
            data=job_data
        )
        
        logger.info(f"Created silence detection job: {job_id}")
        
        # Return the response with the job_id and status
        return JobResponse(
            job_id=job_id
        )
    except Exception as e:
        logger.error(f"Failed to create silence detection job: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create silence detection job: {str(e)}"
        )

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_silence_job_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get the status of a silence detection job.
    
    Args:
        job_id: The ID of the job to check
        api_key: API key for authentication
        
    Returns:
        A JobStatusResponse object with the job status and result if available
        
    Raises:
        HTTPException: If the job is not found
    """
    # Get job status
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(
            status_code=404,
            detail=f"Job not found: {job_id}"
        )
    
    # Create response
    response = JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )
    
    return response

@router.post("/analyze", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_audio_analysis_job(
    request: MediaAnalyzeRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a job to analyze audio characteristics and recommend optimal processing parameters.
    
    This endpoint analyzes audio files to provide insights about:
    - Audio quality and dynamic range
    - Noise floor and speech levels
    - Recommended volume thresholds for optimal speech detection
    - Audio characteristics like spectral centroid and zero-crossing rate
    
    The analysis helps determine the best parameters for silence detection and other
    audio processing tasks.
    
    Args:
        request: The request containing media URL for analysis
        api_key: API key for authentication
        
    Returns:
        A JobResponse object with the job ID and initial status
        
    Raises:
        HTTPException: If the job cannot be created
    """
    # Validate input
    if not request.media_url:
        raise HTTPException(status_code=400, detail="No media URL provided")
    
    # Create job data
    job_data = {
        "media_url": str(request.media_url)
    }
    
    # Create a job
    job_id = str(uuid.uuid4())
    
    try:
        # Create wrapper function to match job queue signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await silence_service.process_analyze_job(_job_id, data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_AUDIO_ANALYSIS,
            process_func=process_wrapper,
            data=job_data
        )
        
        logger.info(f"Created audio analysis job: {job_id}")
        
        # Return the response with the job_id and status
        return JobResponse(
            job_id=job_id
        )
    except Exception as e:
        logger.error(f"Failed to create audio analysis job: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create audio analysis job: {str(e)}"
        )

@router.get("/analyze/{job_id}", response_model=JobStatusResponse)
async def get_analysis_job_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get the status of an audio analysis job.
    
    Args:
        job_id: The ID of the job to check
        api_key: API key for authentication
        
    Returns:
        A JobStatusResponse object with the job status and result if available
        
    Raises:
        HTTPException: If the job is not found
    """
    # Get job status
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(
            status_code=404,
            detail=f"Job not found: {job_id}"
        )
    
    # Create response
    response = JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )
    
    return response