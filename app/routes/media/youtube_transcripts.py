"""
Routes for YouTube transcript generation.
"""
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from app.models import YouTubeTranscriptRequest, JobResponse, JobStatusResponse
from app.services.media.youtube_transcript_service import youtube_transcript_service
from app.services.job_queue import job_queue, JobType
from app.utils.auth import get_api_key
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["media"])


@router.post("/youtube-transcripts", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def generate_youtube_transcript(
    request: YouTubeTranscriptRequest,
    _: str = Depends(get_api_key),  # API key validation (not used in function)
):
    """
    Generate a transcript for a YouTube video with optional translation.
    
    This endpoint extracts transcripts from YouTube videos using the YouTube Transcript API.
    Supports multiple languages, translation, and various output formats.
    
    Args:
        request: YouTube transcript request containing video URL and options
        
    Returns:
        JobResponse with job_id for tracking the transcript generation progress
        
    Raises:
        HTTPException: If parameters are invalid or transcript generation fails
    """
    try:
        # Validate YouTube URL
        video_url = str(request.video_url)
        if "youtube.com" not in video_url and "youtu.be" not in video_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid YouTube URL. Please provide a valid YouTube video URL."
            )
        
        # Validate format
        valid_formats = ["text", "srt", "vtt", "json"]
        if request.format and request.format not in valid_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid format. Supported formats: {', '.join(valid_formats)}"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "video_url": video_url,
            "languages": request.languages,
            "translate_to": request.translate_to,
            "format": request.format or "text"
        }
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await youtube_transcript_service.process_transcript_generation(data)
        
        # Queue the job using consistent pattern
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.YOUTUBE_TRANSCRIPT,
            process_func=process_wrapper,
            data=job_data
        )
        
        logger.info(f"Created YouTube transcript job {job_id} for video: {video_url}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating YouTube transcript job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create YouTube transcript job"
        )


@router.get("/youtube-transcripts/{job_id}", response_model=JobStatusResponse)
async def get_transcript_status(job_id: str):
    """
    Get the status of a YouTube transcript generation job.
    
    Args:
        job_id: The ID of the transcript job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
