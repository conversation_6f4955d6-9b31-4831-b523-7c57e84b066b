"""
Routes for downloading media.
"""
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, status
from app.models import MediaDownloadRequest, JobResponse, JobStatusResponse
from app.services.media.download_service import download_service
from app.services.job_queue import job_queue, JobType
from app.utils.auth import get_api_key
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["media"])


@router.post("/download", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def download_media(
    request: MediaDownloadRequest,
    _: str = Depends(get_api_key),  # API key validation (not used in function)
):
    """
    Download media from a URL using yt-dlp with comprehensive format support.
    
    This endpoint supports downloading from various platforms including YouTube, 
    Vimeo, Twitter, and many others. Supports authenticated downloads with cookies
    and custom filename options.
    
    Args:
        request: Download request containing URL, format, filename, and optional cookies
        
    Returns:
        JobResponse with job_id for tracking the download progress
        
    Raises:
        HTTPException: If parameters are invalid or download fails
    """
    try:
        # Validate URL
        if not request.url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="URL is required"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "url": str(request.url),
            "format": request.format,
            "file_name": request.file_name,
            "cookies_url": request.cookies_url
        }
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await download_service.process_media_download(job_id, data)
        
        # Queue the job using consistent pattern
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_DOWNLOAD,
            process_func=process_wrapper,
            data=job_data
        )
        
        logger.info(f"Created media download job {job_id} for URL: {request.url}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating media download job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create media download job"
        )


@router.get("/download/{job_id}", response_model=JobStatusResponse)
async def get_download_status(job_id: str):
    """
    Get the status of a media download job.
    
    Args:
        job_id: The ID of the download job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
