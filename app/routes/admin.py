"""
Admin dashboard routes for API key management.
"""
import os
import time
import hmac
import base64
import hashlib
import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from fastapi import APIRouter, Request, HTTPException, Response, Depends
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.utils.auth import get_api_key
from app.services.redis_service import redis_service

router = APIRouter()
security = HTTPBearer(auto_error=False)

# Admin credentials from environment
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME', 'admin').lower()
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123')
JWT_SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'fallback-secret-key')

def generate_hmac_token(username: str, api_key: str) -> str:
    """Generate a secure HMAC-based token for admin sessions"""
    secret = JWT_SECRET_KEY.encode()
    expiry = int(time.time()) + (24 * 60 * 60)  # 24 hours
    
    # Create payload: username|api_key|expiry
    payload = f"{username}|{api_key}|{expiry}"
    
    # Create HMAC signature
    signature = hmac.new(secret, payload.encode(), hashlib.sha256).hexdigest()
    
    # Combine payload and signature
    token_data = f"{payload}|{signature}"
    
    # Base64 encode for safe transport
    return base64.b64encode(token_data.encode()).decode()

def verify_hmac_token(token: str) -> Optional[dict]:
    """Verify HMAC-based token"""
    try:
        # Decode from base64
        token_data = base64.b64decode(token.encode()).decode()
        
        # Split token parts
        parts = token_data.split('|')
        if len(parts) != 4:
            return None
            
        username, api_key, expiry_str, signature = parts
        
        # Check expiry
        if int(time.time()) > int(expiry_str):
            return None
        
        # Verify signature
        secret = JWT_SECRET_KEY.encode()
        payload = f"{username}|{api_key}|{expiry_str}"
        expected_signature = hmac.new(secret, payload.encode(), hashlib.sha256).hexdigest()
        
        if not hmac.compare_digest(signature, expected_signature):
            return None
        
        # Return payload
        return {
            'username': username,
            'api_key': api_key,
            'exp': int(expiry_str)
        }
        
    except Exception:
        return None

@router.get("/admin", response_class=HTMLResponse, tags=["admin"])
async def admin_login():
    """Redirect to React admin dashboard"""
    
    redirect_html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <i class="fas fa-spinner fa-spin text-6xl text-blue-600 mb-4"></i>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
                Redirecting to Admin Dashboard
            </h2>
            <p class="text-gray-600 mb-4">
                Admin functionality has been moved to the main dashboard
            </p>
            <p class="text-sm text-gray-500">
                If you're not redirected automatically, <a href="/dashboard" class="text-blue-600 hover:underline">click here</a>
            </p>
        </div>
    </div>

    <script>
        // Redirect to the React dashboard
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 2000);
    </script>
</body>
</html>
    """
    
    return HTMLResponse(content=redirect_html)

@router.post("/admin/login", tags=["admin"])
async def admin_login_api(request: Request):
    """Username/password login endpoint"""
    try:
        data = await request.json()
        username = data.get('username', '').strip().lower()
        password = data.get('password', '')
        
        if username == ADMIN_USERNAME and password == ADMIN_PASSWORD:
            # Use the main API key for admin authentication
            api_key = os.getenv('API_KEY', 'admin-fallback-key')
            
            # Generate secure token
            token = generate_hmac_token(ADMIN_USERNAME, api_key)
            
            response_data = {
                "status": "success",
                "message": "Login successful"
            }
            
            response = JSONResponse(content=response_data)
            
            # Set secure cookie
            is_production = os.getenv('DEBUG', 'false').lower() != 'true'
            response.set_cookie(
                'admin_token',
                token,
                max_age=24*60*60,  # 24 hours
                httponly=True,
                secure=is_production,
                samesite='strict'
            )
            
            return response
        else:
            raise HTTPException(status_code=401, detail="Invalid username or password")
            
    except Exception as e:
        logging.error(f"Login error: {e}")
        raise HTTPException(status_code=500, detail="Login failed")

@router.get("/admin/verify", tags=["admin"])
async def verify_admin_session(request: Request):
    """Verify admin session and return API key"""
    try:
        token = request.cookies.get('admin_token')
        
        if not token:
            raise HTTPException(status_code=401, detail="No session found")
        
        payload = verify_hmac_token(token)
        
        if not payload:
            raise HTTPException(status_code=401, detail="Invalid or expired session")
        
        return {
            "status": "success",
            "api_key": payload['api_key']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logging.error(f"Session verification error: {str(e)}")
        raise HTTPException(status_code=500, detail="Session verification failed")

@router.get("/admin/logout", response_class=HTMLResponse, tags=["admin"])
async def admin_logout():
    """Logout and clear session"""
    logout_html = """
    <script>
        sessionStorage.removeItem('admin_api_key');
        window.location.href = '/admin';
    </script>
    """
    
    response = HTMLResponse(content=logout_html)
    
    # Clear the cookie
    is_production = os.getenv('DEBUG', 'false').lower() != 'true'
    response.set_cookie(
        'admin_token', 
        '', 
        expires=0, 
        httponly=True, 
        secure=is_production, 
        samesite='strict'
    )
    return response

@router.get("/admin/dashboard", response_class=HTMLResponse, tags=["admin"])
async def admin_dashboard():
    """Redirect to React admin dashboard"""
    
    redirect_html = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Redirecting to Admin Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="min-h-screen flex items-center justify-center">
        <div class="text-center">
            <i class="fas fa-spinner fa-spin text-6xl text-blue-600 mb-4"></i>
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
                Redirecting to Admin Dashboard
            </h2>
            <p class="text-gray-600 mb-4">
                Admin functionality has been moved to the main dashboard
            </p>
            <p class="text-sm text-gray-500">
                If you're not redirected automatically, <a href="/dashboard" class="text-blue-600 hover:underline">click here</a>
            </p>
        </div>
    </div>

    <script>
        // Redirect to the React dashboard
        setTimeout(() => {
            window.location.href = '/dashboard';
        }, 2000);
    </script>
</body>
</html>
    """
    
    return HTMLResponse(content=redirect_html)

@router.get("/admin/stats", tags=["admin"])
async def get_admin_stats(api_key: str = Depends(get_api_key)):
    """Get dashboard statistics"""
    try:
        # Get stats from Redis/job queue
        stats = {
            "active_jobs": 0,
            "completed_jobs": 0, 
            "failed_jobs": 0,
            "redis_connected": False
        }
        
        # Try to get Redis stats
        try:
            await redis_service.ping()
            stats["redis_connected"] = True
            
            # Get job counts from Redis if available
            job_keys = await redis_service.get_keys("job:*")
            stats["active_jobs"] = len([k for k in job_keys if k.endswith(":status")])
            
        except Exception as e:
            logging.warning(f"Could not get Redis stats: {e}")
        
        return stats
        
    except Exception as e:
        logging.error(f"Error getting admin stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving stats")

@router.get("/admin/jobs", tags=["admin"])
async def get_admin_jobs(api_key: str = Depends(get_api_key)):
    """Get recent jobs"""
    try:
        jobs = []
        
        # Try to get jobs from Redis
        try:
            await redis_service.ping()
            job_keys = await redis_service.get_keys("job:*:status")
            
            for key in job_keys[:20]:  # Limit to 20 most recent
                job_id = key.split(":")[1]
                try:
                    status = await redis_service.get(key)
                    job_data = await redis_service.get(f"job:{job_id}")
                    
                    if job_data:
                        import json
                        job_info = json.loads(job_data)
                        jobs.append({
                            "id": job_id,
                            "type": job_info.get("type", "unknown"),
                            "status": status,
                            "created_at": job_info.get("created_at", time.time()),
                            "progress": job_info.get("progress", 0)
                        })
                except Exception as e:
                    logging.warning(f"Error parsing job {job_id}: {e}")
                    
        except Exception as e:
            logging.warning(f"Could not get jobs from Redis: {e}")
        
        # Sort by creation time
        jobs.sort(key=lambda x: x.get("created_at", 0), reverse=True)
        
        return {"jobs": jobs}
        
    except Exception as e:
        logging.error(f"Error getting admin jobs: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving jobs")

@router.get("/admin/system", tags=["admin"])
async def get_system_info(api_key: str = Depends(get_api_key)):
    """Get system information"""
    try:
        return {
            "debug": os.getenv('DEBUG', 'false').lower() == 'true',
            "redis_url": os.getenv('REDIS_URL', '').replace('redis://', 'redis://***@') if 'redis://' in os.getenv('REDIS_URL', '') else os.getenv('REDIS_URL', ''),
            "s3_bucket": os.getenv('S3_BUCKET_NAME', ''),
            "s3_region": os.getenv('S3_REGION', ''),
            "s3_endpoint": os.getenv('S3_ENDPOINT_URL', '') or 'AWS S3',
            "kokoro_api": os.getenv('KOKORO_API_URL', '')
        }
        
    except Exception as e:
        logging.error(f"Error getting system info: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving system info")