"""
Authentication routes for username/password login.
"""
import os
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

router = APIRouter()

class LoginRequest(BaseModel):
    username: str
    password: str

class LoginResponse(BaseModel):
    success: bool
    api_key: str
    message: str

# Default credentials from environment
DEFAULT_USERNAME = os.getenv('DEFAULT_USERNAME', 'admin')
DEFAULT_PASSWORD = os.getenv('DEFAULT_PASSWORD', 'admin')
API_KEY = os.getenv('API_KEY', 'your_api_key_here')

@router.post("/auth/login", response_model=LoginResponse, tags=["auth"])
async def login(request: LoginRequest):
    """
    Authenticate with username and password and return API key.
    """
    if request.username == DEFAULT_USERNAME and request.password == DEFAULT_PASSWORD:
        return LoginResponse(
            success=True,
            api_key=API_KEY,
            message="Login successful"
        )
    
    raise HTTPException(
        status_code=401,
        detail="Invalid username or password"
    )

@router.get("/auth/status", tags=["auth"])
async def auth_status():
    """
    Check authentication status.
    """
    return {
        "isAuthenticated": False,
        "message": "Please login to access the dashboard"
    }