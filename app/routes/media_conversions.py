"""
Routes for media conversions.
"""
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Response, status
from app.models import MediaConversionRequest, JobResponse, JobStatusResponse
from app.services.media_conversion_service import media_conversion_service, SUPPORTED_FORMATS, QUALITY_PRESETS
from app.services.job_queue import job_queue, JobType
from app.utils.auth import get_api_key
import uuid
import logging
import base64

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/conversions", tags=["conversions"])


@router.get("/formats")
async def get_supported_formats():
    """
    Get comprehensive list of all supported conversion formats with codec details.
    
    Returns detailed information about supported media formats, quality presets,
    and conversion capabilities for audio, video, and image formats.
    
    Returns:
        Dict containing:
        - supported_formats: Organized by media type (audio, video, image)
        - quality_presets: Available quality settings
        - total_formats: Total number of supported formats
        - format_list: Flat list of all supported formats
    """
    # Create a flat list of all formats for easy reference
    all_formats = []
    for media_type, formats in SUPPORTED_FORMATS.items():
        for format_name, format_info in formats.items():
            all_formats.append({
                "format": format_name,
                "media_type": media_type,
                "codec": format_info.get("codec", "unknown"),
                "description": format_info.get("description", "")
            })
    
    return {
        "object": "formats",
        "supported_formats": SUPPORTED_FORMATS,
        "quality_presets": {
            preset: info for preset, info in QUALITY_PRESETS.items()
        },
        "total_formats": sum(len(formats) for formats in SUPPORTED_FORMATS.values()),
        "format_list": all_formats,
        "media_types": list(SUPPORTED_FORMATS.keys())
    }


@router.post("/", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def convert_media(
    request: MediaConversionRequest,
    _: str = Depends(get_api_key),  # API key validation (not used in function)
):
    """
    Convert media files between formats using FFmpeg with comprehensive format support.
    
    This endpoint supports conversion between audio, video, and image formats using FFmpeg.
    Supports URL-based conversions with quality presets and custom options.
    
    Args:
        request: Media conversion request containing URL, format, quality, and custom options
        
    Returns:
        JobResponse with job_id for tracking the conversion progress
        
    Raises:
        HTTPException: If parameters are invalid or conversion fails
    """
    try:
        # Validate input parameters
        if not request.input_url:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="URL parameter is required"
            )
        
        # Validate output format
        all_formats = set()
        for format_list in SUPPORTED_FORMATS.values():
            all_formats.update(format_list)
        
        if request.output_format.lower() not in all_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported output format '{request.output_format}'. Use /formats endpoint to see supported formats."
            )
        
        # Validate quality preset
        quality = request.quality or "medium"
        if quality not in QUALITY_PRESETS:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid quality preset '{quality}'. Supported presets: {', '.join(QUALITY_PRESETS.keys())}"
            )

        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "input_url": str(request.input_url),
            "output_format": request.output_format.lower(),
            "quality": quality,
            "custom_options": request.custom_options
        }
        
        # Create a wrapper function for URL-based processing
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await media_conversion_service.process_conversion(data)
        
        # Queue the job using consistent pattern (no binary data in job_data)
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_CONVERSION,
            process_func=process_wrapper,
            data=job_data  # No binary data stored here
        )
        
        logger.info(f"Created media conversion job {job_id} for format: {request.output_format}")
        
        return JobResponse(
            job_id=job_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating media conversion job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create media conversion job"
        )


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_conversion_status(job_id: str):
    """
    Get the status of a media conversion job.
    
    Args:
        job_id: The ID of the conversion job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
