"""
Routes for extracting frames from videos.
"""
import logging
import uuid
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status

from app.utils.media import download_media_file, SUPPORTED_VIDEO_FORMATS
from app.services.job_queue import job_queue, JobType
from app.services.video.frames_service import video_frames_service
from app.models import JobResponse, JobStatusResponse, VideoFramesRequest, VideoFramesResult

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/frames", tags=["videos"])

@router.post("", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_frames_job(request: VideoFramesRequest):
    """
    Create a job to extract frames from a video.
    
    Args:
        request: The request containing video URL and frame extraction options
        
    Returns:
        JobResponse: Contains the job_id for tracking the operation
        
    Raises:
        HTTPException: If the video URL is invalid or parameters are invalid
        
    Example:
        ```python
        request = {
            "video_url": "https://example.com/video.mp4",
            "interval": 1.0,
            "format": "jpg",
            "quality": 85,
            "max_frames": 100
        }
        ```
    """
    try:
        # Validate video URL format
        video_url = str(request.video_url)
        
        # Check if it's a supported video format (basic check)
        is_supported = any(
            video_url.lower().endswith(f'.{fmt}') 
            for fmt in SUPPORTED_VIDEO_FORMATS
        ) or 'video' in video_url or video_url.startswith('http')
        
        if not is_supported:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported video format. Please provide a valid video URL."
            )
        
        # Validate format
        valid_formats = ["jpg", "png", "webp"]
        if request.format not in valid_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid format. Supported formats: {', '.join(valid_formats)}"
            )
        
        # Validate max_frames if provided
        if request.max_frames and request.max_frames > 1000:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum frames cannot exceed 1000"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_params = {
            "video_url": video_url,
            "interval": request.interval,
            "format": request.format,
            "quality": request.quality,
            "max_frames": request.max_frames
        }
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await video_frames_service.process_frames_job(_job_id, data)
        
        # Queue the job
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_FRAMES,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created video frames job {job_id} for video: {video_url}")
        
        return JobResponse(
            job_id=job_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating video frames job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create video frames job"
        )


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_frames_job_status(job_id: str):
    """
    Get the status of a video frames extraction job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )