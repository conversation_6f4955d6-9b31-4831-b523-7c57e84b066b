"""
Routes for video manipulation operations.
"""
from fastapi import APIRouter

from app.routes.video.concatenate import router as concatenate_router
from app.routes.video.add_audio import router as add_audio_router
from app.routes.video.merge import router as merge_router
from app.routes.video.add_captions import router as add_captions_router
from app.routes.video.text_overlay import router as text_overlay_router
from app.routes.video.thumbnails import router as thumbnails_router
from app.routes.video.clips import router as clips_router
from app.routes.video.frames import router as frames_router
from app.routes.video.generate import router as generate_router
# Removed: wavespeed router - functionality now unified in generate router

# Create a main router that includes all video-related routes
router = APIRouter()
router.include_router(concatenate_router)
router.include_router(add_audio_router)
router.include_router(merge_router)
router.include_router(add_captions_router)
router.include_router(text_overlay_router)
router.include_router(thumbnails_router)
router.include_router(clips_router)
router.include_router(frames_router)
router.include_router(generate_router)
# Removed: wavespeed router - functionality now unified in generate router