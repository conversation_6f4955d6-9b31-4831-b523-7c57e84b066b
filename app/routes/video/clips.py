"""
Routes for extracting video clips from videos.
"""
import logging
import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, status

from app.utils.media import download_media_file, SUPPORTED_VIDEO_FORMATS
from app.services.job_queue import job_queue, JobType
from app.services.video.clips_service import video_clips_service
from app.models import JobResponse, JobStatusResponse, VideoClipsRequest, VideoClipsResult

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/clips", tags=["videos"])

@router.post("", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_clips_job(request: VideoClipsRequest):
    """
    Create a job to extract video clips from a video using either manual segments or AI-powered search.
    
    Args:
        request: The request containing video URL and either segments or AI query
        
    Returns:
        JobResponse: Contains the job_id for tracking the operation
        
    Raises:
        HTTPException: If the video URL is invalid, segments are invalid, or query is invalid
        
    Examples:
        Manual segments:
        ```python
        {
            "video_url": "https://example.com/video.mp4",
            "segments": [
                {"start": 10.5, "end": 30.0, "name": "intro"},
                {"start": 60.0, "end": 90.5, "name": "highlight"}
            ],
            "output_format": "mp4",
            "quality": "medium"
        }
        ```
        
        AI-powered search:
        ```python
        {
            "video_url": "https://example.com/video.mp4",
            "ai_query": "Find clips discussing machine learning and AI",
            "max_clips": 3,
            "output_format": "mp4",
            "quality": "medium"
        }
        ```
    """
    try:
        # Validate video URL format
        video_url = str(request.video_url)
        
        # Check if it's a supported video format (basic check)
        is_supported = any(
            video_url.lower().endswith(f'.{fmt}') 
            for fmt in SUPPORTED_VIDEO_FORMATS
        ) or 'video' in video_url or video_url.startswith('http')
        
        if not is_supported:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported video format. Please provide a valid video URL."
            )
        
        # Validate input: either segments or ai_query must be provided
        if not request.segments and not request.ai_query:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either 'segments' or 'ai_query' must be provided"
            )
        
        if request.segments and request.ai_query:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot use both 'segments' and 'ai_query' simultaneously. Choose one approach."
            )
        
        # Validate segments if provided
        if request.segments:
            for i, segment in enumerate(request.segments):
                if segment.start < 0:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Segment {i}: Start time cannot be negative"
                    )
                if segment.end <= segment.start:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Segment {i}: End time must be greater than start time"
                    )
                if segment.end - segment.start > 600:  # 10 minutes max per clip
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Segment {i}: Clip duration cannot exceed 10 minutes"
                    )
        
        # Validate AI query if provided
        if request.ai_query:
            if len(request.ai_query.strip()) < 3:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="AI query must be at least 3 characters long"
                )
            if len(request.ai_query) > 500:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="AI query cannot exceed 500 characters"
                )
        
        # Validate output format
        valid_formats = ["mp4", "webm", "avi", "mov", "mkv"]
        if request.output_format not in valid_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid output format. Supported formats: {', '.join(valid_formats)}"
            )
        
        # Validate quality
        valid_qualities = ["low", "medium", "high"]
        if request.quality and request.quality not in valid_qualities:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid quality. Supported qualities: {', '.join(valid_qualities)}"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_params = {
            "video_url": video_url,
            "output_format": request.output_format,
            "quality": request.quality or "medium"
        }
        
        # Add segments or AI query
        if request.segments:
            job_params["segments"] = [
                {
                    "start": segment.start,
                    "end": segment.end,
                    "name": segment.name
                }
                for segment in request.segments
            ]
        else:
            job_params["ai_query"] = request.ai_query.strip()
            job_params["max_clips"] = request.max_clips
        
        # Queue the job - this service method has the correct signature already
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_CLIPS,
            process_func=video_clips_service.process_clips_job,
            data=job_params
        )
        
        if request.segments:
            logger.info(f"Created video clips job {job_id} for video: {video_url} with {len(request.segments)} segments")
        else:
            logger.info(f"Created AI video clips job {job_id} for video: {video_url} with query: '{request.ai_query}'")
        
        return JobResponse(
            job_id=job_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating video clips job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create video clips job"
        )


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_clips_job_status(job_id: str):
    """
    Get the status of a video clips extraction job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )