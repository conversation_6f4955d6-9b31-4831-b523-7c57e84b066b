import uuid
import tempfile
from typing import Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, Request
from typing import Optional, cast
from pydantic import BaseModel, Field
from app.models import JobResponse, JobStatusResponse, JobType
from app.services.job_queue import job_queue
from app.services.video.wavespeed_service import wavespeed_service
from app.services.s3 import s3_service
from app.utils.auth import get_api_key
import logging

logger = logging.getLogger(__name__)

router = APIRouter(tags=["WaveSpeed Video Generation"])


class WaveSpeedTextToVideoRequest(BaseModel):
    """Request model for WaveSpeedAI text-to-video generation."""
    prompt: str = Field(
        description="Text prompt describing the video content (max 1000 characters).",
        max_length=1000
    )
    model: str = Field(
        default="wan-2.2",
        description="WaveSpeedAI model version (wan-2.2, minimax-video-02, minimax-video-01)."
    )
    size: str = Field(
        default="832*480",
        description="Video dimensions (832*480, 480*832)."
    )
    duration: int = Field(
        default=5,
        ge=1,
        le=10,
        description="Video duration in seconds (5, 8)."
    )
    seed: int = Field(
        default=-1,
        description="Random seed for reproducible results (-1 for random)."
    )


class WaveSpeedImageToVideoRequest(BaseModel):
    """Request model for WaveSpeedAI video generation from image."""
    prompt: str = Field(
        description="Text prompt describing the video motion/content (max 1000 characters).",
        max_length=1000
    )
    seed: int = Field(
        default=-1,
        description="Random seed for reproducible results (-1 for random)."
    )
    model: str = Field(
        default="wan-2.2",
        description="WaveSpeedAI model version to use."
    )
    resolution: str = Field(
        default="720p",
        description="Video resolution (720p, 1080p, etc.)."
    )


@router.post("/wavespeed/generate", response_model=JobResponse)
async def create_wavespeed_video_from_text(
    request: WaveSpeedTextToVideoRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate a video from a text prompt using WaveSpeedAI.
    
    This endpoint creates a job for WaveSpeedAI video generation from a text description.
    Use the returned job_id to check the status and retrieve the result.
    
    - **prompt**: Text description of the video content you want to generate
    - **model**: WaveSpeedAI model version (wan-2.2, minimax-video-02, minimax-video-01)
    - **size**: Video dimensions (832*480 for landscape, 480*832 for portrait)
    - **duration**: Video duration in seconds (5 or 8 seconds supported)
    - **seed**: Random seed for reproducible results (-1 for random)
    """
    if not wavespeed_service.is_available():
        raise HTTPException(
            status_code=503,
            detail="WaveSpeedAI service is not available. Please check WAVESPEEDAI_API_KEY configuration."
        )
    
    job_id = str(uuid.uuid4())
    
    # Create job data
    job_data = {
        "prompt": request.prompt,
        "model": request.model,
        "size": request.size,
        "duration": request.duration,
        "seed": request.seed
    }
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.WAVESPEED_TEXT_TO_VIDEO,
        process_func=process_wavespeed_text_to_video_wrapper,
        data=job_data
    )
    
    logger.info(f"Created WaveSpeedAI text-to-video job {job_id} with prompt: {request.prompt[:50]}...")
    
    return JobResponse(job_id=job_id)


@router.get("/wavespeed/generate/{job_id}", response_model=JobStatusResponse)
async def get_wavespeed_text_to_video_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get the status of a WaveSpeedAI text-to-video generation job.
    
    Returns the current status of the job and the result if completed.
    
    **Status values:**
    - `pending`: Job is in the queue waiting to be processed
    - `processing`: Job is currently being processed
    - `completed`: Job has completed successfully
    - `failed`: Job has failed with an error
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/wavespeed/image_to_video", response_model=JobResponse)
async def create_wavespeed_video_from_image(
    request: Request,
    prompt: str = Form(..., description="Text prompt describing the video motion/content"),
    image: UploadFile = File(..., description="Image file to animate (PNG, JPG, JPEG up to 10MB)"),
    seed: int = Form(-1, description="Random seed for reproducible results (-1 for random)"),
    model: str = Form("wan-2.2", description="WaveSpeedAI model version"),
    resolution: str = Form("720p", description="Video resolution"),
    api_key: str = Depends(get_api_key)
):
    """
    Generate a video from an image using WaveSpeedAI.
    
    This endpoint creates a job for WaveSpeedAI video generation from an uploaded image.
    Use the returned job_id to check the status and retrieve the result.
    
    - **prompt**: Text description of how the video should animate/change
    - **image**: Upload an image file (PNG, JPG, JPEG up to 10MB)
    - **seed**: Random seed for reproducible results (-1 for random)
    - **model**: WaveSpeedAI model version (default: wan-2.2)
    - **resolution**: Video resolution (default: 720p)
    """
    if not wavespeed_service.is_available():
        raise HTTPException(
            status_code=503,
            detail="WaveSpeedAI service is not available. Please check WAVESPEED_API_KEY configuration."
        )
    
    # Validate file type
    if image.content_type not in ["image/png", "image/jpeg", "image/jpg"]:
        raise HTTPException(
            status_code=400,
            detail="Invalid file type. Please upload a PNG or JPEG image."
        )
    
    # Validate file size (10MB limit)
    if image.size and image.size > 10 * 1024 * 1024:
        raise HTTPException(
            status_code=400,
            detail="Image file too large. Please upload an image smaller than 10MB."
        )
    
    job_id = str(uuid.uuid4())
    
    # Read image data
    image_data = await image.read()
    
    # Save image to temporary file for S3 upload
    with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as tmp_file:
        tmp_file.write(image_data)
        tmp_file_path = tmp_file.name
    
    try:
        # Upload image to S3 and get URL
        image_url = await s3_service.upload_file(
            tmp_file_path,
            f"wavespeed-images/{job_id}.png"
        )
    finally:
        # Clean up temporary file
        import os
        os.unlink(tmp_file_path)
    
    # Create job data
    job_data = {
        "image_url": image_url,
        "prompt": prompt,
        "seed": seed,
        "model": model,
        "resolution": resolution
    }
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.WAVESPEED_IMAGE_TO_VIDEO,
        process_func=process_wavespeed_video_wrapper,
        data=job_data
    )
    
    logger.info(f"Created WaveSpeedAI video job {job_id} with prompt: {prompt[:50]}...")
    
    return JobResponse(job_id=job_id)


@router.get("/wavespeed/image_to_video/{job_id}", response_model=JobStatusResponse)
async def get_wavespeed_video_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """
    Get the status of a WaveSpeedAI video generation job.
    
    Returns the current status of the job and the result if completed.
    
    **Status values:**
    - `pending`: Job is in the queue waiting to be processed
    - `processing`: Job is currently being processed
    - `completed`: Job has completed successfully
    - `failed`: Job has failed with an error
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


async def process_wavespeed_video_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """
    Wrapper function for WaveSpeedAI video generation that matches job queue signature.
    """
    try:
        # Extract parameters
        image_url = data["image_url"]
        prompt = data["prompt"]
        seed = data["seed"]
        model = data["model"]
        resolution = data["resolution"]
        
        logger.info(f"Processing WaveSpeedAI video generation: {prompt[:50]}...")
        
        # Generate video with WaveSpeedAI
        video_data = await wavespeed_service.image_to_video(
            image_url=image_url,
            prompt=prompt,
            seed=seed,
            model=model,
            resolution=resolution
        )
        
        # Save video to temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
            tmp_file.write(video_data)
            tmp_file_path = tmp_file.name
        
        try:
            # Upload video to S3
            video_url = await s3_service.upload_file(
                tmp_file_path,
                f"wavespeed-videos/{_job_id}.mp4"
            )
        finally:
            # Clean up temporary file
            import os
            os.unlink(tmp_file_path)
        
        result = {
            "original_image_url": image_url,
            "video_url": video_url,
            "prompt_used": prompt,
            "seed_used": seed,
            "model_used": model,
            "resolution_used": resolution,
            "provider": "wavespeed"
        }
        
        logger.info(f"WaveSpeedAI video generation completed: {video_url}")
        return result
        
    except Exception as e:
        logger.error(f"WaveSpeedAI video generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"WaveSpeedAI video generation failed: {str(e)}")


async def process_wavespeed_text_to_video_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """
    Wrapper function for WaveSpeedAI text-to-video generation that matches job queue signature.
    """
    try:
        # Extract parameters
        prompt = data["prompt"]
        model = data["model"]
        size = data["size"]
        duration = data["duration"]
        seed = data["seed"]
        
        logger.info(f"Processing WaveSpeedAI text-to-video generation: {prompt[:50]}...")
        
        # Generate video with WaveSpeedAI
        video_data = await wavespeed_service.text_to_video(
            prompt=prompt,
            model=model,
            size=size,
            duration=duration,
            seed=seed
        )
        
        # Save video to temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as tmp_file:
            tmp_file.write(video_data)
            tmp_file_path = tmp_file.name
        
        try:
            # Upload video to S3
            video_url = await s3_service.upload_file(
                tmp_file_path,
                f"wavespeed-videos/{_job_id}.mp4"
            )
        finally:
            # Clean up temporary file
            import os
            os.unlink(tmp_file_path)
        
        result = {
            "video_url": video_url,
            "prompt_used": prompt,
            "model_used": model,
            "size_used": size,
            "duration_used": duration,
            "seed_used": seed,
            "provider": "wavespeed"
        }
        
        logger.info(f"WaveSpeedAI text-to-video generation completed: {video_url}")
        return result
        
    except Exception as e:
        logger.error(f"WaveSpeedAI text-to-video generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"WaveSpeedAI text-to-video generation failed: {str(e)}")