"""
Routes for video concatenation operations.
"""
import logging
import uuid
from typing import Op<PERSON>

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from app.utils.media import SUPPORTED_VIDEO_FORMATS
from app.services.job_queue import job_queue, JobStatus, JobType
from app.services.video.concatenate import concatenation_service
from app.models import JobResponse, JobStatusResponse, VideoConcatenateRequest, VideoConcatenateResult

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/concatenate", tags=["videos"])

@router.post("", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_concatenate_job(request: VideoConcatenateRequest):
    """
    Create a job to concatenate multiple videos into a single video with transition effects.
    
    This endpoint supports professional transition effects between video segments,
    including fade, dissolve, slide, and wipe transitions for smooth video merging.
    
    Args:
        request: The request containing video URLs, output format, and transition settings.
        
    Returns:
        A JobResponse object with the job ID and initial status.
        
    Raises:
        HTTPException: If the job cannot be created or parameters are invalid.
    """
    # Validate input
    if not request.video_urls:
        raise HTTPException(status_code=400, detail="No video URLs provided")
        
    if len(request.video_urls) < 2:
        raise HTTPException(status_code=400, detail="At least 2 video URLs are required for concatenation")
    
    # Validate transition effect
    valid_transitions = ["none", "fade", "dissolve", "slide", "wipe"]
    if request.transition not in valid_transitions:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid transition effect. Supported transitions: {', '.join(valid_transitions)}"
        )
    
    # Validate output format
    output_format = request.output_format.lower()
    if not output_format.startswith('.'):
        output_format = f".{output_format}"
    
    if output_format not in SUPPORTED_VIDEO_FORMATS:
        valid_formats = [f[1:] for f in SUPPORTED_VIDEO_FORMATS]  # Remove the leading dot
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported output format. Supported formats: {', '.join(valid_formats)}"
        )
    
    # Create job data
    job_data = {
        "video_urls": request.video_urls,
        "output_format": output_format,
        "transition": request.transition,
        "transition_duration": request.transition_duration,
        "max_segment_duration": request.max_segment_duration,
        "total_duration_limit": request.total_duration_limit
    }
    
    # Create a job
    job_id = str(uuid.uuid4())
    
    try:
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_CONCATENATION,
            process_func=concatenation_service.process_job,
            data=job_data
        )
        
        logger.info(f"Created video concatenation job: {job_id}")
        
        # Return the response with the job_id and status
        # Since jobs immediately go to "processing" state after add_job
        return JobResponse(
            job_id=job_id
        )
    except Exception as e:
        logger.error(f"Failed to create concatenation job: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create concatenation job: {str(e)}"
        )

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """
    Get the status of a concatenation job.
    
    Args:
        job_id: The ID of the job to check.
        
    Returns:
        A JobStatusResponse object with the job status and result if available.
        
    Raises:
        HTTPException: If the job is not found.
    """
    # Get job status
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(
            status_code=404,
            detail=f"Job not found: {job_id}"
        )
    
    # Create response
    response = JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )
    
    return response 