"""
Routes for video merge operations - combining video concatenation with audio overlay.
"""
import logging
import uuid
from typing import Optional

from fastapi import APIRouter, HTTPException, status
from pydantic import BaseModel, Field

from app.utils.media import SUPPORTED_VIDEO_FORMATS
from app.services.job_queue import job_queue, JobStatus, JobType
from app.services.video.merge import merge_service
from app.models import JobResponse, JobStatusResponse, VideoMergeRequest, VideoMergeResult

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/merge", tags=["videos"])

@router.post("", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_merge_job(request: VideoMergeRequest):
    """
    Create a job to merge multiple videos with optional background audio.
    
    This endpoint combines video concatenation with audio overlay functionality,
    allowing you to merge multiple videos into a single video with optional
    background music or audio overlay. Supports professional transition effects
    between video segments and advanced audio mixing options.
    
    Features:
    - Concatenate multiple videos with transition effects
    - Add optional background music/audio overlay
    - Control volume levels for both video and audio
    - Support for fade effects on background audio
    - Multiple audio sync modes (replace, mix, overlay)
    
    Args:
        request: The request containing video URLs, audio URL, and merge settings.
        
    Returns:
        A JobResponse object with the job ID and initial status.
        
    Raises:
        HTTPException: If the job cannot be created or parameters are invalid.
    """
    # Validate input
    if not request.video_urls:
        raise HTTPException(status_code=400, detail="No video URLs provided")
        
    if len(request.video_urls) < 2:
        raise HTTPException(status_code=400, detail="At least 2 video URLs are required for merging")
    
    # Validate transition effect
    valid_transitions = ["none", "fade", "dissolve", "slide", "wipe"]
    if request.transition not in valid_transitions:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid transition effect. Supported transitions: {', '.join(valid_transitions)}"
        )
    
    # Validate sync mode
    valid_sync_modes = ["replace", "mix", "overlay"]
    if request.sync_mode not in valid_sync_modes:
        raise HTTPException(
            status_code=400, 
            detail=f"Invalid sync mode. Supported modes: {', '.join(valid_sync_modes)}"
        )
    
    # Validate output format
    output_format = request.output_format.lower()
    if not output_format.startswith('.'):
        output_format = f".{output_format}"
    
    if output_format not in SUPPORTED_VIDEO_FORMATS:
        valid_formats = [f[1:] for f in SUPPORTED_VIDEO_FORMATS]  # Remove the leading dot
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported output format. Supported formats: {', '.join(valid_formats)}"
        )
    
    # Create job data
    job_data = {
        "video_urls": request.video_urls,
        "background_audio_url": request.background_audio_url,
        "output_format": output_format,
        
        # Video concatenation settings
        "transition": request.transition,
        "transition_duration": request.transition_duration,
        "max_segment_duration": request.max_segment_duration,
        "total_duration_limit": request.total_duration_limit,
        
        # Audio settings
        "video_volume": request.video_volume,
        "audio_volume": request.audio_volume,
        "sync_mode": request.sync_mode,
        "fade_in_duration": request.fade_in_duration,
        "fade_out_duration": request.fade_out_duration
    }
    
    # Create a job
    job_id = str(uuid.uuid4())
    
    try:
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_MERGE,
            process_func=merge_service.process_job,
            data=job_data
        )
        
        logger.info(f"Created video merge job: {job_id}")
        
        # Return the response with the job_id and status
        return JobResponse(
            job_id=job_id
        )
    except Exception as e:
        logger.error(f"Failed to create merge job: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create merge job: {str(e)}"
        )

@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """
    Get the status of a video merge job.
    
    Args:
        job_id: The ID of the job to check.
        
    Returns:
        A JobStatusResponse object with the job status and result if available.
        
    Raises:
        HTTPException: If the job is not found.
    """
    # Get job status
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(
            status_code=404,
            detail=f"Job not found: {job_id}"
        )
    
    # Create response
    response = JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )
    
    return response

@router.get("", status_code=status.HTTP_200_OK)
async def get_merge_info():
    """
    Get information about the video merge endpoint and its capabilities.
    
    Returns:
        Dictionary with endpoint information, supported formats, and examples
    """
    return {
        "endpoint": "/v1/videos/merge",
        "method": "POST",
        "description": "Merge multiple videos with optional background audio overlay",
        "features": [
            "Concatenate multiple videos with transition effects",
            "Add optional background music/audio overlay", 
            "Control volume levels for video and audio",
            "Support for fade effects on background audio",
            "Multiple audio sync modes (replace, mix, overlay)",
            "Professional transition effects between segments"
        ],
        "supported_formats": {
            "video": ["mp4", "webm", "mov", "avi", "mkv"],
            "audio": ["mp3", "wav", "m4a", "flac", "aac", "ogg"]
        },
        "parameters": {
            "video_urls": {
                "type": "array",
                "required": True,
                "description": "List of video URLs to merge (minimum 2 required)"
            },
            "background_audio_url": {
                "type": "string", 
                "required": False,
                "description": "Optional background audio URL"
            },
            "transition": {
                "type": "string",
                "default": "none",
                "options": ["none", "fade", "dissolve", "slide", "wipe"],
                "description": "Transition effect between video segments"
            },
            "sync_mode": {
                "type": "string",
                "default": "overlay", 
                "options": ["replace", "mix", "overlay"],
                "description": "Audio synchronization mode"
            },
            "video_volume": {
                "type": "integer",
                "default": 100,
                "range": "0-100",
                "description": "Volume level for original video tracks"
            },
            "audio_volume": {
                "type": "integer", 
                "default": 20,
                "range": "0-100",
                "description": "Volume level for background audio"
            }
        },
        "examples": [
            {
                "description": "Basic video merge without audio",
                "request": {
                    "video_urls": [
                        "https://example.com/video1.mp4",
                        "https://example.com/video2.mp4"
                    ],
                    "transition": "fade",
                    "transition_duration": 1.5
                }
            },
            {
                "description": "Video merge with background music",
                "request": {
                    "video_urls": [
                        "https://example.com/video1.mp4", 
                        "https://example.com/video2.mp4",
                        "https://example.com/video3.mp4"
                    ],
                    "background_audio_url": "https://example.com/music.mp3",
                    "transition": "dissolve",
                    "sync_mode": "overlay",
                    "video_volume": 80,
                    "audio_volume": 30,
                    "fade_in_duration": 2.0,
                    "fade_out_duration": 2.0
                }
            }
        ]
    }