"""
Routes for text overlay functionality.
"""
import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException, Depends

from app.models import (
    JobResponse, 
    JobStatusResponse, 
    TextOverlayRequest, 
    TextOverlayPresetRequest,
    TextOverlayResult,
    JobType
)
from app.services.job_queue import job_queue
from app.services.video.text_overlay import text_overlay_service

logger = logging.getLogger(__name__)

router = APIRouter(tags=["videos"])


async def process_text_overlay(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """
    Process a text overlay job using the text overlay service.
    
    Args:
        job_id: The job ID
        data: Text overlay parameters
    
    Returns:
        Dict with text overlay results
    """
    return await text_overlay_service.process_text_overlay_job(job_id, data)


@router.post("/text-overlay", response_model=JobResponse)
async def create_text_overlay_job(request: TextOverlayRequest):
    """
    Create a job to add text overlay to a video.
    
    This endpoint adds customizable text overlays to videos with extensive styling options.
    Perfect for titles, subtitles, watermarks, and other text-based video enhancements.
    
    ### 🎬 Text Overlay Features:
    - **Custom positioning**: 9 position options (top-left, center, bottom-right, etc.)
    - **Advanced styling**: Font size, colors, background boxes, opacity
    - **Flexible timing**: Custom duration and positioning offsets
    - **Auto text wrapping**: Intelligent text wrapping for long content
    - **Professional quality**: FFmpeg-powered rendering
    
    ### 📝 Text Options:
    - **Font control**: Size (8-200px), color (named or hex)
    - **Background boxes**: Color, opacity, border width
    - **Positioning**: 9 preset positions + custom offsets
    - **Typography**: Line spacing, auto-wrap options
    
    ### ⚙️ Position Options:
    - `top-left`, `top-center`, `top-right`
    - `center-left`, `center`, `center-right`
    - `bottom-left`, `bottom-center`, `bottom-right`
    
    Args:
        request: Text overlay request with video URL, text, and styling options
        
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Validate text content
        if not request.text.strip():
            raise HTTPException(
                status_code=400,
                detail="Text content cannot be empty"
            )
        
        # Validate position
        valid_positions = [
            "top-left", "top-center", "top-right",
            "center-left", "center", "center-right", 
            "bottom-left", "bottom-center", "bottom-right"
        ]
        
        if request.options.position not in valid_positions:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid position. Must be one of: {', '.join(valid_positions)}"
            )
        
        # Create job parameters
        job_params = {
            "video_url": str(request.video_url),
            "text": request.text,
            "options": request.options.dict(),
            "preset_used": None
        }
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created text overlay job: {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/text-overlay/presets")
async def get_text_overlay_presets():
    """
    Get available text overlay presets.
    
    Returns a comprehensive list of predefined text overlay styles optimized for different use cases.
    Each preset includes a description and complete styling configuration.
    
    ### 🎨 Available Presets:
    - **title_overlay**: Large title text for video headers
    - **subtitle**: Bottom-positioned subtitle text
    - **watermark**: Subtle branding/watermark text
    - **alert**: Attention-grabbing notification style
    - **modern_caption**: Clean, contemporary caption style
    - **social_post**: Instagram/TikTok optimized text
    - **quote**: Elegant quote/testimonial presentation
    - **news_ticker**: Breaking news style banner
    
    ### 💡 Usage:
    Use preset names with the `/text-overlay/preset/{preset_name}` endpoint
    for quick, professional-looking text overlays.
    
    Returns:
        Dictionary with preset names as keys and preset configurations as values
    """
    try:
        presets = text_overlay_service.get_available_presets()
        return presets
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving presets: {str(e)}")


@router.post("/text-overlay/preset/{preset_name}", response_model=JobResponse)
async def create_text_overlay_with_preset(
    preset_name: str,
    request: TextOverlayPresetRequest
):
    """
    Add text overlay using predefined preset.
    
    Apply professional text overlays using optimized presets for common use cases.
    Presets can be customized by providing option overrides in the request.
    
    ### 🎨 Available Presets:
    - **title_overlay**: Large, prominent title text
    - **subtitle**: Standard subtitle positioning
    - **watermark**: Subtle corner watermark
    - **alert**: Eye-catching alert/notification
    - **modern_caption**: Contemporary caption style
    - **social_post**: Social media optimized
    - **quote**: Elegant quote presentation
    - **news_ticker**: News banner style
    
    ### ⚙️ Customization:
    You can override any preset option by including it in the `options` field.
    Only specified options will be overridden; others use preset defaults.
    
    ### 📝 Example Override:
    ```json
    {
        "options": {
            "font_color": "red",
            "duration": 10
        }
    }
    ```
    This would use the preset but change text color to red and duration to 10 seconds.
    
    Args:
        preset_name: Name of the preset to use
        request: Text overlay preset request with optional overrides
        
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Get available presets
        presets = text_overlay_service.get_available_presets()
        
        # Check if preset exists
        if preset_name not in presets:
            available_presets = ", ".join(presets.keys())
            raise HTTPException(
                status_code=404,
                detail=f"Preset '{preset_name}' not found. Available presets: {available_presets}"
            )
        
        # Validate text content
        if not request.text.strip():
            raise HTTPException(
                status_code=400,
                detail="Text content cannot be empty"
            )
        
        # Get the preset
        preset = presets[preset_name]
        
        # Start with preset options
        final_options = preset["options"].copy()
        
        # Override with any provided options
        if request.options:
            override_dict = request.options.dict(exclude_none=True)
            final_options.update(override_dict)
        
        # Create job parameters
        job_params = {
            "video_url": str(request.video_url),
            "text": request.text,
            "options": final_options,
            "preset_used": preset_name
        }
        
        # Create and start the job
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.TEXT_OVERLAY,
            process_func=process_text_overlay,
            data=job_params
        )
        
        logger.info(f"Created text overlay job with preset '{preset_name}': {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/text-overlay/{job_id}", response_model=JobStatusResponse)
async def get_text_overlay_job_status(job_id: str):
    """
    Get the status of a text overlay job.
    
    This endpoint retrieves the current status of a text overlay job. When the job
    is completed, the `result` field will contain a TextOverlayResult object
    with the following fields:
    - `video_url`: URL to the video with text overlay
    - `duration`: Duration of the output video in seconds
    - `preset_used`: Name of the preset that was used (if applicable)
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains the text overlay results (TextOverlayResult)
        - error: If failed, contains error information
    """
    # Get job status using job queue
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Return the current status
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )