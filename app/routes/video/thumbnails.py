"""
Routes for generating video thumbnails.
"""
import logging
import uuid
from typing import Dict, Any

from fastapi import APIRouter, HTTPException, status

from app.utils.media import download_media_file, SUPPORTED_VIDEO_FORMATS
from app.services.job_queue import job_queue, JobType
from app.services.video.thumbnails_service import video_thumbnails_service
from app.models import JobResponse, JobStatusResponse, VideoThumbnailsRequest, VideoThumbnailsResult

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/thumbnails", tags=["videos"])

@router.post("", response_model=JobResponse, status_code=status.HTTP_202_ACCEPTED)
async def create_thumbnails_job(request: VideoThumbnailsRequest):
    """
    Create a job to generate thumbnails from a video.
    
    Args:
        request: The request containing video URL and thumbnail generation options
        
    Returns:
        JobResponse: Contains the job_id for tracking the operation
        
    Raises:
        HTTPException: If the video URL is invalid or unsupported format
        
    Example:
        ```python
        request = {
            "video_url": "https://example.com/video.mp4",
            "count": 5,
            "format": "jpg",
            "quality": 85
        }
        ```
    """
    try:
        # Validate video URL format
        video_url = str(request.video_url)
        
        # Check if it's a supported video format (basic check)
        is_supported = any(
            video_url.lower().endswith(f'.{fmt}') 
            for fmt in SUPPORTED_VIDEO_FORMATS
        ) or 'video' in video_url or video_url.startswith('http')
        
        if not is_supported:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported video format. Please provide a valid video URL."
            )
        
        # Validate format
        valid_formats = ["jpg", "png", "webp"]
        if request.format not in valid_formats:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid format. Supported formats: {', '.join(valid_formats)}"
            )
        
        # Validate timestamps if provided
        if request.timestamps:
            for i, ts in enumerate(request.timestamps):
                if ts < 0:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Timestamp at index {i} cannot be negative"
                    )
            
            # Check if timestamps list matches count
            if len(request.timestamps) != request.count:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Number of timestamps must match the count parameter"
                )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_params = {
            "video_url": video_url,
            "timestamps": request.timestamps,
            "count": request.count,
            "format": request.format,
            "quality": request.quality
        }
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await video_thumbnails_service.process_thumbnails_job(_job_id, data)
        
        # Queue the job
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_THUMBNAILS,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created video thumbnails job {job_id} for video: {video_url}")
        
        return JobResponse(
            job_id=job_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating video thumbnails job: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create video thumbnails job"
        )


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_thumbnails_job_status(job_id: str):
    """
    Get the status of a video thumbnails generation job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )