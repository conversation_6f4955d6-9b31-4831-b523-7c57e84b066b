import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, Request
from typing import Optional, cast
from pydantic import BaseModel, Field
from app.models import JobResponse, JobStatusResponse, JobType
from app.services.job_queue import job_queue
from app.services.video.ltx_video_service import ltx_video_service
from app.services.video.wavespeed_service import wavespeed_service
from app.services.video.comfyui_service import comfyui_service
from app.services.s3 import s3_service
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(tags=["Video Generation"])


class VideoGenerationRequest(BaseModel):
    """Request model for AI video generation."""
    prompt: str = Field(
        description="Text prompt for video generation (max 1000 characters).",
        max_length=1000
    )
    provider: str = Field(
        default="ltx_video",
        description="AI video provider (ltx_video, wavespeed, comfyui)."
    )
    negative_prompt: str = Field(
        default="",
        description="Negative prompt for what to avoid in the video."
    )
    width: int = Field(
        default=704,
        ge=256,
        le=1024,
        description="Video width in pixels (must be divisible by 32)."
    )
    height: int = Field(
        default=480,
        ge=256,
        le=1024,
        description="Video height in pixels (must be divisible by 32)."
    )
    num_frames: int = Field(
        default=150,
        ge=1,
        le=257,
        description="Number of frames to generate (1-257)."
    )
    num_inference_steps: int = Field(
        default=200,
        ge=1,
        le=500,
        description="Number of inference steps."
    )
    guidance_scale: float = Field(
        default=4.5,
        ge=1.0,
        le=20.0,
        description="Guidance scale for prompt adherence."
    )
    seed: Optional[int] = Field(
        default=None,
        description="Optional seed for reproducible results."
    )


class VideoGenerationResult(BaseModel):
    """Result model for video generation."""
    video_url: str = Field(
        description="URL to the generated video stored in S3."
    )
    prompt_used: str = Field(
        description="The prompt that was used for generation."
    )
    negative_prompt_used: str = Field(
        description="The negative prompt that was used for generation."
    )
    dimensions: dict[str, int] = Field(
        description="Video dimensions (width, height)."
    )
    num_frames: int = Field(
        description="Number of frames in the generated video."
    )
    processing_time: float = Field(
        description="Processing time in seconds."
    )
    provider_used: str = Field(
        description="AI video provider that was used for generation."
    )


class VideoFromImageRequest(BaseModel):
    """Request model for AI video generation from image."""
    prompt: str = Field(
        description="Text prompt describing the video motion/content (max 1000 characters).",
        max_length=1000
    )
    negative_prompt: str = Field(
        default="",
        description="Negative prompt for what to avoid in the video."
    )
    width: int = Field(
        default=704,
        ge=256,
        le=1024,
        description="Video width in pixels (must be divisible by 32)."
    )
    height: int = Field(
        default=480,
        ge=256,
        le=1024,
        description="Video height in pixels (must be divisible by 32)."
    )
    num_frames: int = Field(
        default=150,
        ge=1,
        le=257,
        description="Number of frames to generate (1-257)."
    )
    num_inference_steps: int = Field(
        default=200,
        ge=1,
        le=500,
        description="Number of inference steps."
    )
    guidance_scale: float = Field(
        default=4.5,
        ge=1.0,
        le=20.0,
        description="Guidance scale for prompt adherence."
    )
    seed: Optional[int] = Field(
        default=None,
        description="Optional seed for reproducible results."
    )


class VideoFromImageResult(BaseModel):
    """Result model for video generation from image."""
    original_image_url: str = Field(
        description="URL to the original image stored in S3."
    )
    video_url: str = Field(
        description="URL to the generated video stored in S3."
    )
    prompt_used: str = Field(
        description="The prompt that was used for generation."
    )
    negative_prompt_used: str = Field(
        description="The negative prompt that was used for generation."
    )
    dimensions: dict[str, int] = Field(
        description="Video dimensions (width, height)."
    )
    num_frames: int = Field(
        description="Number of frames in the generated video."
    )
    processing_time: float = Field(
        description="Processing time in seconds."
    )
    provider_used: str = Field(
        description="AI video provider that was used for generation."
    )


async def process_video_generation_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for video generation job processing."""
    import time
    import tempfile
    import os
    start_time = time.time()
    
    temp_file_path = None
    try:
        # Extract parameters
        prompt = data["prompt"]
        provider = data.get("provider", "ltx_video")
        negative_prompt = data.get("negative_prompt", "")
        width = data.get("width", 704)
        height = data.get("height", 480)
        num_frames = data.get("num_frames", 150)
        num_inference_steps = data.get("num_inference_steps", 200)
        guidance_scale = data.get("guidance_scale", 4.5)
        seed = data.get("seed")
        
        # Route to appropriate service based on provider
        if provider == "wavespeed":
            if not wavespeed_service.is_available():
                raise ValueError("WaveSpeed service is not available (API key or URL not configured)")
            
            # Convert dimensions to WaveSpeed format
            if width == 704 and height == 480:
                size = "832*480"  # Landscape
            elif width == 480 and height == 704:
                size = "480*832"  # Portrait
            else:
                size = f"{width}*{height}"
            
            # Convert frame count to duration (WaveSpeed uses seconds)
            duration = min(int(num_frames / 15), 8)  # Assuming 15fps, max 8 seconds
            
            # Generate video with WaveSpeed
            video_data = await wavespeed_service.text_to_video(
                prompt=prompt,
                model="wan-2.2",  # Default model
                size=size,
                duration=duration,
                seed=seed if seed is not None else -1
            )
        elif provider == "comfyui":
            if not comfyui_service.is_available():
                raise ValueError("ComfyUI service is not available (URL and auth not configured)")
            
            logger.info("Starting ComfyUI video generation")
            
            # Generate video with ComfyUI
            video_data = await comfyui_service.text_to_video(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_frames=num_frames
            )
        else:  # Default to LTX-Video
            if not ltx_video_service.is_available():
                raise ValueError("LTX-Video service is not available (API key or URL not configured)")
            
            # Generate video with LTX-Video
            video_data = await ltx_video_service.generate_video(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_frames=num_frames,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                seed=seed
            )
        
        # Save binary data to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
            temp_file.write(video_data)
            temp_file_path = temp_file.name
        
        # Upload to S3
        s3_path = f"generated-videos/{job_id}.mp4"
        video_url = await s3_service.upload_file(
            file_path=temp_file_path,
            object_name=s3_path,
            content_type="video/mp4"
        )
        
        processing_time = time.time() - start_time
        
        result = VideoGenerationResult(
            video_url=video_url,
            prompt_used=prompt,
            negative_prompt_used=negative_prompt,
            dimensions={"width": width, "height": height},
            num_frames=num_frames,
            processing_time=processing_time,
            provider_used=provider
        )
        
        return result.model_dump()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Video generation failed: {str(e)}")
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                # Log error but don't fail the main operation
                print(f"Warning: Failed to clean up temporary file {temp_file_path}: {e}")


async def process_video_from_image_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for video generation from image job processing."""
    import time
    import tempfile
    import os
    import base64
    start_time = time.time()
    
    original_temp_file = None
    video_temp_file = None
    try:
        # Extract parameters
        prompt = data["prompt"]
        provider = data.get("provider", "ltx_video")
        negative_prompt = data.get("negative_prompt", "")
        width = data.get("width", 704)
        height = data.get("height", 480)
        num_frames = data.get("num_frames", 150)
        num_inference_steps = data.get("num_inference_steps", 200)
        guidance_scale = data.get("guidance_scale", 4.5)
        seed = data.get("seed")
        
        # Handle both old format (binary) and new format (base64)
        if "original_image_data_b64" in data:
            original_image_data = base64.b64decode(data["original_image_data_b64"])
        else:
            # Fallback for old format (if any exist)
            original_image_data = data["original_image_data"]
        
        # Save original image to temporary file for S3 upload
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(original_image_data)
            original_temp_file = temp_file.name
        
        # Upload original image to S3
        original_s3_path = f"original-images/{job_id}.png"
        original_image_url = await s3_service.upload_file(
            file_path=original_temp_file,
            object_name=original_s3_path,
            content_type="image/png"
        )
        
        # Route to appropriate service based on provider
        if provider == "wavespeed":
            if not wavespeed_service.is_available():
                raise ValueError("WaveSpeed service is not available (API key or URL not configured)")
            
            # WaveSpeed resolution format (e.g., "720p", "1080p", or "WIDTHxHEIGHT")
            resolution = f"{width}x{height}" if width and height else "720p"
            
            # Upload image to S3 first to get a URL for WaveSpeed 
            import tempfile
            import uuid
            
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                temp_file.write(original_image_data)
                temp_file_path = temp_file.name
            
            try:
                # Upload to S3 and get URL
                object_name = f"temp_images/{uuid.uuid4()}.png"
                image_url = await s3_service.upload_file(temp_file_path, object_name, "image/png")
                
                # Generate video from image with WaveSpeed
                video_data = await wavespeed_service.image_to_video(
                    image_url=image_url,
                    prompt=prompt,
                    model="wan-2.2",  # Default model
                    resolution=resolution,
                    seed=seed if seed is not None else -1
                )
            finally:
                # Clean up temp file
                import os
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        elif provider == "comfyui":
            # Note: ComfyUI image-to-video requires a different workflow
            # For now, we'll use the text-to-video workflow with the prompt
            # Future enhancement could implement proper image-to-video workflow
            if not comfyui_service.is_available():
                raise ValueError("ComfyUI service is not available (URL and auth not configured)")
            
            logger.info("Starting ComfyUI image-to-video generation")
            
            # Generate video with ComfyUI (using text-to-video workflow)
            video_data = await comfyui_service.text_to_video(
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_frames=num_frames
            )
        else:  # Default to LTX-Video
            if not ltx_video_service.is_available():
                raise ValueError("LTX-Video service is not available (API key or URL not configured)")
            
            # Generate video from image with LTX-Video
            video_data = await ltx_video_service.image_to_video(
                image_bytes=original_image_data,
                prompt=prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_frames=num_frames,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                seed=seed
            )
        
        # Save video to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as temp_file:
            temp_file.write(video_data)
            video_temp_file = temp_file.name
        
        # Upload video to S3
        video_s3_path = f"generated-videos/{job_id}.mp4"
        video_url = await s3_service.upload_file(
            file_path=video_temp_file,
            object_name=video_s3_path,
            content_type="video/mp4"
        )
        
        processing_time = time.time() - start_time
        
        result = VideoFromImageResult(
            original_image_url=original_image_url,
            video_url=video_url,
            prompt_used=prompt,
            negative_prompt_used=negative_prompt,
            dimensions={"width": width, "height": height},
            num_frames=num_frames,
            processing_time=processing_time,
            provider_used=provider
        )
        
        # Add video_url for media library compatibility
        result_dict = result.model_dump()
        result_dict['video_url'] = video_url  # Primary URL for media library
        
        return result_dict
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Video generation from image failed: {str(e)}")
    finally:
        # Clean up temporary files
        for temp_file_path in [original_temp_file, video_temp_file]:
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    print(f"Warning: Failed to clean up temporary file {temp_file_path}: {e}")


@router.post("/generate", response_model=JobResponse)
async def generate_video(
    request: VideoGenerationRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate an AI video from a text prompt using your choice of AI video provider.
    
    Creates an asynchronous job that generates a video using the specified AI video provider.
    
    **Supported Providers:**
    - **ltx_video** (default): High-quality video generation with fine control
    - **wavespeed**: Fast video generation with multiple models
    - **comfyui**: Custom video generation using ComfyUI workflows
    
    **Features:**
    - Multiple AI video providers to choose from
    - High-quality AI video generation from text prompts
    - Customizable video dimensions
    - Provider-specific optimization (frame count, quality settings)
    - Optional seed for reproducible results
    - Automatic S3 storage with direct download URLs
    
    **Perfect for:**
    - Social media video content creation
    - Marketing and promotional videos
    - Creative storytelling
    - Concept visualization
    - Educational content
    
    **Processing Time:** Varies by provider (LTX: 30-120s, WaveSpeed: 15-60s)
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Validate provider
        if request.provider not in ["ltx_video", "wavespeed", "comfyui"]:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported provider: {request.provider}. Supported providers: ltx_video, wavespeed, comfyui"
            )
        
        # Check if the requested service is available
        if request.provider == "wavespeed" and not wavespeed_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="WaveSpeed service is currently unavailable (API key or URL not configured)"
            )
        elif request.provider == "comfyui" and not comfyui_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="ComfyUI service is currently unavailable (URL and auth not configured)"
            )
        elif request.provider == "ltx_video" and not ltx_video_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="LTX-Video service is currently unavailable (API key or URL not configured)"
            )
        
        # Prepare job data
        job_data = request.model_dump()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_GENERATION,
            process_func=process_video_generation_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create video generation job: {str(e)}")


@router.get("/generate/{job_id}", response_model=JobStatusResponse)
async def get_video_generation_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a video generation job.
    
    Poll this endpoint to monitor the progress of your video generation.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → AI model generating video
    3. `completed` → Video ready and uploaded to S3
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "video_url": "https://s3.../generated_video.mp4",
        "prompt_used": "a beautiful sunset over mountains",
        "negative_prompt_used": "blurry, low quality",
        "dimensions": {"width": 704, "height": 480},
        "num_frames": 150,
        "processing_time": 45.2,
        "provider_used": "wavespeed"
    }
    ```
    
    **Download Links:**
    - `video_url`: Direct link to your generated video
    
    **Metadata:**
    - `processing_time`: Total generation and upload time in seconds
    - `dimensions`: Actual video dimensions
    - `num_frames`: Number of frames in the video
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/from_image", response_model=JobResponse)
async def generate_video_from_image(
    prompt: str = Form(..., description="Text prompt describing the video motion/content (max 1000 characters)."),
    image: UploadFile = File(..., description="Image file to animate (PNG, JPG, JPEG up to 10MB)."),
    provider: str = Form("ltx_video", description="AI video provider (ltx_video, wavespeed, comfyui)."),
    negative_prompt: str = Form("", description="Negative prompt for what to avoid in the video."),
    width: int = Form(704, ge=256, le=1024, description="Video width in pixels (must be divisible by 32)."),
    height: int = Form(480, ge=256, le=1024, description="Video height in pixels (must be divisible by 32)."),
    num_frames: int = Form(150, ge=1, le=257, description="Number of frames to generate (1-257)."),
    num_inference_steps: int = Form(200, ge=1, le=500, description="Number of inference steps."),
    guidance_scale: float = Form(4.5, ge=1.0, le=20.0, description="Guidance scale for prompt adherence."),
    seed: Optional[int] = Form(None, description="Optional seed for reproducible results."),
    _: str = Depends(get_api_key)
):
    """
    Generate a video from an image using LTX-Video.
    
    Creates an asynchronous job that animates an uploaded image using the LTX-Video model.
    
    **Features:**
    - High-quality AI video generation from images
    - Upload any image (PNG, JPG, JPEG) up to 10MB
    - Natural language prompts for video motion/content
    - Customizable video dimensions (256x256 to 1024x1024, divisible by 32)
    - Adjustable number of frames (1-257 frames)
    - Adjustable inference steps for quality vs speed
    - Guidance scale control for prompt adherence
    - Optional seed for reproducible results
    - Automatic S3 storage with direct download URLs
    
    **Perfect for:**
    - Animating still images
    - Creating dynamic content from artwork
    - Bringing photos to life
    - Social media video content
    - Creative storytelling
    
    **Processing Time:** Typically 30-120 seconds depending on complexity and parameters.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    import logging
    logger = logging.getLogger(__name__)
    job_id = str(uuid.uuid4())
    
    try:
        # Validate prompt length
        if len(prompt) > 1000:
            raise HTTPException(
                status_code=400,
                detail="Prompt too long. Maximum 1000 characters allowed."
            )
        
        # Validate file type
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Invalid file type. Please upload a PNG or JPEG image."
            )
        
        logger.info(f"Video from image request received - prompt: '{prompt}', provider: '{provider}', width: {width}, height: {height}, num_frames: {num_frames}")
        logger.info(f"Image info - filename: {image.filename}, content_type: {image.content_type}")
        
        # Validate provider
        if provider not in ["ltx_video", "wavespeed", "comfyui"]:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported provider: {provider}. Supported providers: ltx_video, wavespeed, comfyui"
            )
        
        # Check if the requested service is available
        if provider == "wavespeed" and not wavespeed_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="WaveSpeed service is currently unavailable (API key or URL not configured)"
            )
        elif provider == "comfyui" and not comfyui_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="ComfyUI service is currently unavailable (URL and auth not configured)"
            )
        elif provider == "ltx_video" and not ltx_video_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="LTX-Video service is currently unavailable (API key or URL not configured)"
            )
        
        # Read image data after all other validations
        image_data = await image.read()
        
        # Validate file size (10MB limit)
        if len(image_data) > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="Image file too large. Please upload an image smaller than 10MB."
            )
        
        # Convert image data to base64 for JSON serialization
        import base64
        image_data_b64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare job data
        job_data = {
            "prompt": prompt,
            "provider": provider,
            "negative_prompt": negative_prompt,
            "width": width,
            "height": height,
            "num_frames": num_frames,
            "num_inference_steps": num_inference_steps,
            "guidance_scale": guidance_scale,
            "seed": seed,
            "original_image_data_b64": image_data_b64
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_FROM_IMAGE,
            process_func=process_video_from_image_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in video generation from image: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create video generation job: {str(e)}")


@router.get("/from_image/{job_id}", response_model=JobStatusResponse)
async def get_video_from_image_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a video generation from image job.
    
    Poll this endpoint to monitor the progress of your video generation from image.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → AI model generating video from image
    3. `completed` → Video ready and uploaded to S3
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "original_image_url": "https://s3.../original_image.png",
        "video_url": "https://s3.../generated_video.mp4",
        "prompt_used": "make the flowers bloom and butterflies fly",
        "negative_prompt_used": "blurry, low quality",
        "dimensions": {"width": 704, "height": 480},
        "num_frames": 150,
        "processing_time": 52.7
    }
    ```
    
    **Download Links:**
    - `original_image_url`: Link to your original uploaded image
    - `video_url`: Link to the AI-generated video
    
    **Metadata:**
    - `processing_time`: Total generation and upload time in seconds
    - `dimensions`: Actual video dimensions
    - `num_frames`: Number of frames in the video
    - `prompt_used`: The exact prompt used for generation
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )