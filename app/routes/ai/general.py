import os
import logging
from typing import Any, Dict
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from openai import OpenAI
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)

try:
    from groq import Groq
    GROQ_AVAILABLE = True
except ImportError:
    Groq = None
    GROQ_AVAILABLE = False

router = APIRouter(prefix="/v1/ai", tags=["AI General"])

class AIGenerationRequest(BaseModel):
    prompt: str
    max_tokens: int = 1000
    temperature: float = 0.7
    provider: str = "auto"

class AIGenerationResponse(BaseModel):
    content: str
    provider_used: str
    model_used: str

class AIGenerationService:
    """Simple AI generation service for frontend use."""
    
    def __init__(self):
        self.openai_client = None
        self.groq_client = None
        self._setup_clients()
    
    def _setup_clients(self):
        """Initialize AI clients based on available API keys."""
        # Setup OpenAI client
        openai_key = os.getenv('OPENAI_API_KEY') or os.getenv('OPENAI_KEY')
        if openai_key:
            openai_base_url = os.getenv('OPENAI_BASE_URL')
            if openai_base_url:
                self.openai_client = OpenAI(api_key=openai_key, base_url=openai_base_url)
            else:
                self.openai_client = OpenAI(api_key=openai_key)
        
        # Setup Groq client
        groq_key = os.getenv('GROQ_API_KEY')
        if groq_key and GROQ_AVAILABLE and Groq and len(groq_key) > 30:
            groq_base_url = os.getenv('GROQ_BASE_URL')
            if groq_base_url:
                self.groq_client = Groq(api_key=groq_key, base_url=groq_base_url)
            else:
                self.groq_client = Groq(api_key=groq_key)
    
    def _get_provider_and_model(self, provider: str):
        """Get the appropriate client and model based on provider preference."""
        openai_model = os.getenv('OPENAI_MODEL', 'gpt-4o')
        groq_model = os.getenv('GROQ_MODEL', 'mixtral-8x7b-32768')
        
        if provider == "groq" and self.groq_client:
            return self.groq_client, groq_model, "groq"
        elif provider == "openai" and self.openai_client:
            return self.openai_client, openai_model, "openai"
        elif provider == "auto":
            # Auto-select based on availability (prefer Groq if available)
            if self.groq_client:
                return self.groq_client, groq_model, "groq"
            elif self.openai_client:
                return self.openai_client, openai_model, "openai"
        else:
            # Fallback to any available client
            if self.openai_client:
                return self.openai_client, openai_model, "openai"
            elif self.groq_client:
                return self.groq_client, groq_model, "groq"
        
        raise ValueError("No AI provider available. Please set OPENAI_API_KEY or GROQ_API_KEY environment variable.")
    
    async def generate(self, request: AIGenerationRequest) -> AIGenerationResponse:
        """Generate AI content based on the prompt."""
        try:
            # Get the appropriate client and model
            client, model, actual_provider = self._get_provider_and_model(request.provider)
            
            # Make the API call
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": request.prompt}
                ],
                max_tokens=request.max_tokens,
                temperature=request.temperature
            )
            
            content = response.choices[0].message.content or ""
            
            return AIGenerationResponse(
                content=content,
                provider_used=actual_provider,
                model_used=model
            )
            
        except Exception as e:
            logger.error(f"AI generation failed: {e}")
            raise HTTPException(status_code=500, detail=f"AI generation failed: {str(e)}")

# Create service instance
ai_service = AIGenerationService()

@router.post("/generate", response_model=AIGenerationResponse)
async def generate_ai_content(
    request: AIGenerationRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate AI content from a prompt.
    
    Simple endpoint for generating AI content using OpenAI or Groq.
    Used by frontend components for scene generation and search term extraction.
    """
    return await ai_service.generate(request)