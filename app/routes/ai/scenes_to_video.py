"""
Scenes-to-video creation routes for external API compatibility.

This module provides routes for creating videos from predefined scenes
with explicit search terms, matching external API formats.
"""

from typing import Any
from fastapi import APIRouter, HTTPException
from app.models import (
    ScenesVideoRequest, 
    ScenesVideoResult,
    JobResponse,
    JobStatusResponse,
    JobType
)
from app.services.job_queue import job_queue
from app.services.ai.scenes_video_service import ScenesVideoService

router = APIRouter()


@router.post("/scenes-to-video")
async def create_scenes_video(request: ScenesVideoRequest) -> JobResponse:
    """
    Create a video from predefined scenes with search terms.
    
    This endpoint accepts a list of scenes with text content and search terms
    for background videos, matching external API formats like dahopevi.com/create.
    
    Each scene contains:
    - text: The narration text for this scene
    - searchTerms: Keywords for finding background video footage
    - duration: Optional scene duration (calculated from TTS if not provided)
    
    Returns a job ID for tracking the video creation progress.
    """
    try:
        # Validate scenes
        if not request.scenes:
            raise HTTPException(status_code=400, detail="At least one scene is required")
        
        # Generate unique job ID
        import uuid
        job_id = str(uuid.uuid4())
        
        # Create wrapper function for job queue compatibility
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            service = ScenesVideoService()
            return await service.create_video(data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.SCENES_TO_VIDEO,
            process_func=process_wrapper,
            data=request.dict()
        )
        
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create scenes video job: {str(e)}")


@router.get("/scenes-to-video/{job_id}")
async def get_scenes_video_status(job_id: str) -> JobStatusResponse:
    """
    Get the status of a scenes-to-video creation job.
    
    Returns the current status of the video creation process,
    including progress updates and final results when completed.
    """
    try:
        job_info = await job_queue.get_job_info(job_id)
        
        if not job_info:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobStatusResponse(
            job_id=job_id,
            status=job_info.status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")