import uuid
from typing import Any, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus
)
from app.services.job_queue import job_queue
from app.services.ai.pexels_image_service import PexelsImageService
from app.services.ai.pixabay_image_service import Pi<PERSON>bayImageService
from app.utils.auth import get_api_key

# Initialize service instances
pexels_image_service = PexelsImageService()
pixabay_image_service = PixabayImageService()

# Request models
class StockImageSearchRequest(BaseModel):
    """Request model for stock image search."""
    query: str
    orientation: str = "landscape"  # "landscape", "portrait", "square"
    quality: str = "high"  # "standard", "high", "ultra"
    per_page: int = 20
    color: Optional[str] = None  # "red", "orange", "yellow", "green", "turquoise", "blue", "violet", "pink", "brown", "black", "gray", "white"
    size: Optional[str] = None  # "large", "medium", "small"
    provider: str = "pexels"  # "pexels", "pixabay"

class ImageBrowseRequest(BaseModel):
    """Request model for manual image browsing."""
    query: str
    orientation: str = "landscape"
    quality: str = "high"
    per_page: int = 20
    page: int = 1
    color: Optional[str] = None
    size: Optional[str] = None
    provider: str = "pexels"

# Response models
class ImageResult(BaseModel):
    """Individual image result."""
    id: str
    url: str
    download_url: str
    width: int
    height: int
    photographer: Optional[str] = None
    photographer_url: Optional[str] = None
    alt: Optional[str] = None
    tags: Optional[str] = None
    source: str
    aspect_ratio: float

class StockImageSearchResult(BaseModel):
    """Result model for stock image search."""
    images: list[ImageResult]
    total_results: int
    page: int
    per_page: int
    query_used: str
    provider_used: str

router = APIRouter(prefix="/v1/ai", tags=["AI Image Search"])


async def process_stock_image_search_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for stock image search job processing."""
    provider = data.get('provider', 'pexels')
    
    if provider == 'pexels':
        # Convert to Pexels API format
        params = {
            'query': data['query'],
            'per_page': data.get('per_page', 20),
            'orientation': data.get('orientation', 'landscape'),
            'quality': data.get('quality', 'high'),
            'color': data.get('color'),
            'size': data.get('size')
        }
        result = await pexels_image_service.search_images(params)
        
        # Convert to standardized format
        images = []
        for img in result.get('images', []):
            images.append({
                'id': str(img.get('id', '')),
                'url': img.get('url', ''),
                'download_url': img.get('download_url', ''),
                'width': img.get('width', 0),
                'height': img.get('height', 0),
                'photographer': img.get('photographer'),
                'photographer_url': img.get('photographer_url'),
                'alt': img.get('alt', ''),
                'tags': '',
                'source': 'pexels',
                'aspect_ratio': img.get('width', 1) / max(img.get('height', 1), 1)
            })
        
        return {
            'images': images,
            'total_results': result.get('total_results', len(images)),
            'page': result.get('page', 1),
            'per_page': result.get('per_page', 20),
            'query_used': data['query'],
            'provider_used': 'pexels'
        }
    
    elif provider == 'pixabay':
        # Use Pixabay service
        images_data = await pixabay_image_service.search_images(
            query=data['query'],
            orientation=data.get('orientation', 'landscape'),
            quality=data.get('quality', 'high'),
            per_page=data.get('per_page', 20),
            color=data.get('color'),
            size=data.get('size')
        )
        
        # Convert to standardized format
        images = []
        for img in images_data:
            images.append({
                'id': str(img.get('id', '')),
                'url': img.get('url', ''),
                'download_url': img.get('download_url', ''),
                'width': img.get('width', 0),
                'height': img.get('height', 0),
                'photographer': img.get('user'),
                'photographer_url': None,
                'alt': '',
                'tags': img.get('tags', ''),
                'source': 'pixabay',
                'aspect_ratio': img.get('aspect_ratio', 1.0)
            })
        
        return {
            'images': images,
            'total_results': len(images),
            'page': 1,
            'per_page': data.get('per_page', 20),
            'query_used': data['query'],
            'provider_used': 'pixabay'
        }
    
    else:
        raise HTTPException(status_code=400, detail=f"Unsupported provider: {provider}")


@router.post("/image-search/stock-images", response_model=JobResponse)
async def search_stock_images(
    request: StockImageSearchRequest,
    _: str = Depends(get_api_key)
):
    """
    Search for stock images using Pexels or Pixabay APIs.
    
    Creates an asynchronous job to search for high-quality stock images based on
    your query and filtering criteria. Results are optimized for various use cases
    with resolution, orientation, and color filtering.
    
    **Features:**
    - Pexels and Pixabay API integration for high-quality stock photos
    - Advanced filtering by orientation, quality, color, and size
    - Automatic image quality selection
    - Direct download URLs for seamless integration
    - Optimized for web and mobile applications
    
    **Image Quality:**
    - Ultra: Highest resolution available (4K+)
    - High: High resolution (1920x1080+)
    - Standard: Medium resolution for faster loading
    
    **Orientation Options:**
    - Landscape: 16:9 or wider aspect ratio
    - Portrait: 9:16 or taller aspect ratio  
    - Square: 1:1 aspect ratio
    
    **Color Filters:**
    - Specific colors: red, orange, yellow, green, turquoise, blue, violet, pink, brown, black, gray, white
    - Grayscale and transparent options available
    
    **Providers:**
    - Pexels: Professional stock photos with high quality
    - Pixabay: Free stock photos with diverse content library
    """
    try:
        # Validate provider
        if request.provider not in ['pexels', 'pixabay']:
            raise HTTPException(
                status_code=400,
                detail="Provider must be 'pexels' or 'pixabay'"
            )
        
        # Validate orientation
        if request.orientation not in ['landscape', 'portrait', 'square']:
            raise HTTPException(
                status_code=400,
                detail="Orientation must be 'landscape', 'portrait', or 'square'"
            )
        
        # Validate quality
        if request.quality not in ['standard', 'high', 'ultra']:
            raise HTTPException(
                status_code=400,
                detail="Quality must be 'standard', 'high', or 'ultra'"
            )
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "query": request.query,
            "orientation": request.orientation,
            "quality": request.quality,
            "per_page": min(request.per_page, 80),  # Limit to reasonable amount
            "color": request.color,
            "size": request.size,
            "provider": request.provider
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_SEARCH,
            process_func=process_stock_image_search_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create image search job: {str(e)}"
        )


@router.get("/image-search/stock-images/{job_id}", response_model=JobStatusResponse)
async def get_image_search_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """Get the status and result of an image search job."""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/image-browse", response_model=JobResponse)
async def browse_images(
    request: ImageBrowseRequest,
    _: str = Depends(get_api_key)
):
    """
    Browse images with pagination support.
    
    Similar to stock image search but with pagination for browsing large result sets.
    Useful for building image galleries or selection interfaces.
    """
    try:
        # Create job with browse-specific data
        job_id = str(uuid.uuid4())
        job_data = {
            "query": request.query,
            "orientation": request.orientation,
            "quality": request.quality,
            "per_page": min(request.per_page, 80),
            "page": request.page,
            "color": request.color,
            "size": request.size,
            "provider": request.provider
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_SEARCH,
            process_func=process_stock_image_search_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create image browse job: {str(e)}"
        )


@router.get("/image-browse/{job_id}", response_model=JobStatusResponse)
async def get_image_browse_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """Get the status and result of an image browse job."""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.get("/image-providers/status")
async def get_image_providers_status(_: str = Depends(get_api_key)):
    """Get the status of available image providers."""
    return {
        "providers": {
            "pexels": {
                "available": pexels_image_service.is_available(),
                "name": "Pexels",
                "description": "High-quality stock photos from professional creators",
                "features": ["Professional Quality", "Free License", "Large Library"]
            },
            "pixabay": {
                "available": pixabay_image_service.is_available(),
                "name": "Pixabay", 
                "description": "Free stock photos with diverse library",
                "features": ["Diverse Content", "Vector Graphics", "Free License"]
            }
        },
        "supported_orientations": ["landscape", "portrait", "square"],
        "supported_qualities": ["standard", "high", "ultra"],
        "supported_colors": ["red", "orange", "yellow", "green", "turquoise", "blue", "violet", "pink", "brown", "black", "gray", "white"],
        "supported_sizes": ["large", "medium", "small"]
    }
