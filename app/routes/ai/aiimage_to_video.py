import uuid
from typing import Any
from fastapi import APIRouter, HTTPException, Depends
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    AiimageToVideoRequest, AiimageToVideoResult
)
from app.services.job_queue import job_queue
from app.services.ai.unified_video_pipeline import unified_video_pipeline
from app.services.ai.topic_discovery_service import topic_discovery_service
from app.utils.auth import get_api_key

router = APIRouter(prefix="/v1/ai", tags=["AI Video Generation"])


async def process_aiimage_to_video_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for aiimage-to-video pipeline job processing."""
    # Set the media strategy to AI images for this route
    data['footage_provider'] = 'ai_generated'
    data['media_type'] = 'image'
    return await unified_video_pipeline.process_unified_video_generation(data)


@router.post("/aiimage-to-video", response_model=JobResponse)
async def generate_video_from_script_with_ai_images(
    request: AiimageToVideoRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate a complete video from a script using AI-generated images.
    
    Creates an asynchronous job that performs the complete aiimage-to-video workflow:
    1. **Script Generation**: AI creates engaging script from your topic
    2. **Audio Generation**: Text-to-speech converts script to narration
    3. **Audio Transcription**: Extract timing segments for each part of the script
    4. **Image Prompt Generation**: AI creates visual prompts for each script segment
    5. **Image Generation**: AI models (Together.ai FLUX or Flux Kontext Dev) create high-quality images
    6. **Video Creation**: Convert images to videos with dynamic effects (zoom, pan, etc.)
    7. **Background Music**: Optional AI-generated background music
    8. **Video Composition**: Automatically syncs images with audio timing
    9. **Caption Addition**: Adds styled captions with modern effects
    10. **Final Rendering**: Produces ready-to-publish video content
    
    **Perfect for:**
    - AI-powered content creation
    - Social media video automation
    - Educational content with custom visuals
    - Marketing videos with branded imagery
    - Storytelling with AI-generated scenes
    - YouTube Shorts with unique visuals
    
    **AI Models Used:**
    - Script: OpenAI GPT-4o / Groq Mixtral-8x7b
    - Image Generation: Together.ai FLUX.1-schnell or Flux Kontext Dev
    - Image Prompts: OpenAI GPT-4o-mini
    - Music Generation: Meta MusicGen (optional)
    - Voice: Kokoro TTS / Microsoft Edge TTS
    
    **Video Effects:**
    - `zoom`: Progressive zoom effect on images
    - `pan`: Smooth panning across images
    - `ken_burns`: Classic Ken Burns effect (zoom + pan)
    - `fade`: Fade in/out transitions
    - `slide`: Sliding transitions (future)
    
    **Auto-Topic Discovery:**
    Set `auto_topic: true` to automatically discover trending topics based on `script_type`.
    When enabled, the `topic` parameter becomes optional and the system will:
    - Search for trending news and content related to the script type
    - Generate appropriate topics using Google Search or Perplexity
    - Create videos about current events and trending subjects
    
    **Image Generation Features:**
    - Customizable dimensions (256x256 to 2048x2048)
    - Multiple inference steps for quality control
    - AI-optimized prompts for each script segment
    - High-quality FLUX model for photorealistic results
    
    **Output Specifications:**
    - Format: MP4 with H.264 codec
    - Resolution: Customizable (default based on orientation)
    - Audio: High-quality TTS narration + optional background music
    - Captions: Modern TikTok-style effects
    - Duration: Matches script length (typically 20-120 seconds)
    
    **Processing Time:** Typically 3-8 minutes depending on video length, number of images, and complexity.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Validate input: either topic provided or auto_topic enabled
        if not request.auto_topic and not request.topic:
            raise HTTPException(
                status_code=400, 
                detail="Either 'topic' must be provided or 'auto_topic' must be set to true"
            )
        
        # Prepare data for job processing
        # Note: Service validation will be done during job processing to avoid timeouts
        job_data = request.model_dump()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.AIIMAGE_TO_VIDEO,
            process_func=process_aiimage_to_video_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create aiimage-to-video job: {str(e)}")


@router.get("/aiimage-to-video/{job_id}", response_model=JobStatusResponse)
async def get_aiimage_to_video_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a aiimage-to-video generation job.
    
    Poll this endpoint to monitor the progress of your AI-powered video generation.
    The pipeline involves multiple AI processing steps, so please allow
    several minutes for completion.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → Pipeline running through multiple steps:
       - Script generation
       - Audio synthesis
       - Audio transcription for timing
       - Image prompt generation for each segment
       - AI image generation with FLUX model
       - Video creation with effects
       - Background music generation (if enabled)
       - Video composition and concatenation
       - Caption addition
    3. `completed` → Video ready with all assets
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "final_video_url": "https://s3.../final_video.mp4",
        "video_with_audio_url": "https://s3.../video_no_captions.mp4",
        "script_generated": "Amazing facts you didn't know...",
        "audio_url": "https://s3.../narration.wav",
        "generated_images": [
            {
                "url": "https://s3.../image_1.png",
                "prompt": "Majestic underwater coral reef...",
                "index": 0
            }
        ],
        "background_music_url": "https://s3.../music.wav",
        "music_prompt_generated": "Uplifting ambient music...",
        "srt_url": "https://s3.../captions.srt",
        "video_duration": 45.2,
        "processing_time": 420.5,
        "word_count": 142,
        "segments_count": 8,
        "segments_data": [
            {
                "start_time": 0.0,
                "end_time": 5.2,
                "text": "Did you know that...",
                "prompt": "Amazing ocean scene..."
            }
        ]
    }
    ```
    
    **Download Links:**
    - `final_video_url`: Your finished video with captions ready for upload
    - `video_with_audio_url`: Video with audio but without captions (clean version)
    - `audio_url`: Just the narration audio file
    - `background_music_url`: Generated background music (if enabled)
    - `srt_url`: Caption file for manual editing (if captions enabled)
    - `generated_images`: Array of all AI-generated images with their prompts
    
    **Metadata:**
    - `processing_time`: Total pipeline execution time in seconds
    - `word_count`: Words in generated script
    - `segments_count`: Number of image/video segments created
    - `segments_data`: Detailed timing and content for each segment
    - `music_prompt_generated`: AI-generated prompt used for background music
    
    **AI-Generated Assets:**
    - All images are created specifically for your script content
    - Image prompts are optimized for visual storytelling
    - Background music matches the mood and content type
    - Each segment is timed precisely to the narration
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )