import uuid
from typing import Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel
from app.models import (
    JobResponse, JobStatusResponse, JobType, JobStatus,
    VideoSearchQueryRequest, VideoSearchQueryResult,
    StockVideoSearchRequest, StockVideoSearchResult
)
from app.services.job_queue import job_queue
from app.services.ai.video_search_query_generator import video_search_query_generator
from app.services.ai.pexels_service import pexels_service
from app.utils.auth import get_api_key

# Additional models for manual browsing
class VideoBrowseRequest(BaseModel):
    query: str
    orientation: str = "landscape"
    per_page: int = 15
    page: int = 1

class FrontendVideoSearchRequest(BaseModel):
    """Frontend-compatible video search request model."""
    topic: str
    language: str = "en"
    orientation: str = "portrait"
    per_page: int = 15
    page: int = 1

router = APIRouter(prefix="/v1/ai", tags=["AI Video Search"])


async def process_video_search_query_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for video search query generation job processing."""
    return await video_search_query_generator.generate_video_search_queries(data)


async def process_stock_video_search_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for stock video search job processing."""
    return await pexels_service.search_videos(data)


@router.post("/video-search-queries", response_model=JobResponse)
async def generate_video_search_queries(
    request: FrontendVideoSearchRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate video search queries from script content using AI.
    
    Creates an asynchronous job to analyze script text and generate visually concrete
    search queries that can be used to find relevant background videos for each segment.
    Uses AI to identify visual concepts and create timing-aware search terms.
    
    **Features:**
    - AI-powered visual concept extraction
    - Timing-aware query generation
    - Support for multiple AI providers (OpenAI, Groq)
    - Optimized for stock video search
    - Automatic segment duration management
    - Visual concreteness validation
    
    **Use Case:**
    Perfect for automated video production pipelines where you need to find
    background footage that matches your script content timing.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Convert frontend request to backend format
        # Since frontend sends topic but backend expects script, create a simple script from topic
        job_data = {
            "script": f"This video is about {request.topic}. Let me tell you some interesting facts about {request.topic}.",
            "segment_duration": 3.0,
            "max_segments": 10,
            "language": request.language
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.VIDEO_SEARCH_QUERY_GENERATION,
            process_func=process_video_search_query_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create video search query generation job: {str(e)}")


@router.get("/video-search-queries/{job_id}", response_model=JobStatusResponse)
async def get_video_search_queries_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a video search query generation job.
    
    Poll this endpoint to check the progress of your query generation job.
    When completed, the result will include timed video search queries with
    visual concepts and timing information.
    
    **Result Format (when completed):**
    ```json
    {
        "queries": [
            {
                "query": "ocean waves",
                "start_time": 0.0,
                "end_time": 3.0,
                "duration": 3.0,
                "visual_concept": "Ocean waves crashing on shore"
            },
            {
                "query": "mountain landscape",
                "start_time": 3.0,
                "end_time": 6.0,
                "duration": 3.0,
                "visual_concept": "Mountain peaks and valleys"
            }
        ],
        "total_duration": 45.2,
        "total_segments": 15,
        "provider_used": "groq"
    }
    ```
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/video-search/stock-videos", response_model=JobResponse)
async def search_stock_videos(
    request: StockVideoSearchRequest,
    _: str = Depends(get_api_key)
):
    """
    Search for stock videos using the Pexels API.
    
    Creates an asynchronous job to search for high-quality stock videos based on
    your query and filtering criteria. Results are optimized for video production
    with resolution, duration, and orientation filtering.
    
    **Features:**
    - Pexels API integration for high-quality stock footage
    - Advanced filtering by duration, resolution, orientation
    - Automatic video quality selection
    - Duplicate video avoidance
    - Optimized for YouTube Shorts and social media
    - Direct download URLs for seamless integration
    
    **Video Quality:**
    - Large: HD quality (1920x1080 or higher)
    - Medium: Standard quality
    - Small: Lower resolution for faster processing
    
    **Orientation Options:**
    - Landscape: 16:9 aspect ratio (1920x1080)
    - Portrait: 9:16 aspect ratio (1080x1920) 
    - Square: 1:1 aspect ratio (1080x1080)
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.STOCK_VIDEO_SEARCH,
            process_func=process_stock_video_search_wrapper,
            data=request.dict()
        )
        
        return JobResponse(job_id=job_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create stock video search job: {str(e)}")


@router.get("/video-search/stock-videos/{job_id}", response_model=JobStatusResponse)
async def get_stock_video_search_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a stock video search job.
    
    Poll this endpoint to check the progress of your video search job.
    When completed, the result will include a list of filtered and optimized
    stock videos with download URLs and metadata.
    
    **Result Format (when completed):**
    ```json
    {
        "videos": [
            {
                "id": 123456,
                "url": "https://www.pexels.com/video/...",
                "download_url": "https://player.vimeo.com/external/...",
                "duration": 15,
                "width": 1920,
                "height": 1080,
                "file_size": 5242880,
                "quality": "hd",
                "file_type": "mp4",
                "tags": ["ocean", "waves", "nature"]
            }
        ],
        "total_results": 42,
        "page": 1,
        "per_page": 15,
        "query_used": "ocean waves"
    }
    ```
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


async def process_video_browse_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for manual video browsing job processing."""
    # Create a simplified request for Pexels service
    search_request = {
        "query": data.get("query", ""),
        "orientation": data.get("orientation", "landscape"),
        "per_page": data.get("per_page", 15),
        "page": data.get("page", 1),
        "size": "large"  # Default to high quality  
    }
    return await pexels_service.search_videos(search_request)


@router.post("/video-browse", response_model=JobResponse)
async def browse_stock_videos(
    request: VideoBrowseRequest,
    _: str = Depends(get_api_key)
):
    """
    Browse stock videos with simple search interface.
    
    A simplified endpoint for manually browsing and searching stock videos
    from Pexels. Perfect for content creators who want to preview and select
    background videos manually.
    """
    job_id = str(uuid.uuid4())
    
    try:
        job_data = request.dict()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.STOCK_VIDEO_SEARCH,
            process_func=process_video_browse_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create video browse job: {str(e)}")


@router.get("/video-browse/{job_id}", response_model=JobStatusResponse)
async def get_video_browse_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """Get the status and result of a video browse job."""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )
