"""
Pollinations.AI Text Generation Routes

Provides endpoints for text generation and chat using Pollinations.AI API
integrated with the Ouinhi job queue system.
"""

import uuid
import time
import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import JSONResponse

from app.models import (
    JobResponse, 
    JobStatusResponse, 
    JobType,
    PollinationsTextRequest,
    PollinationsChatRequest,
    PollinationsTextResult
)
from app.services.job_queue import job_queue
from app.services.pollinations_service import pollinations_service, APIError, CircuitBreakerError
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/pollinations", tags=["Pollinations Text"])


@router.post("/text/generate", response_model=JobResponse)
async def generate_text(
    request: PollinationsTextRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate text using Pollinations.AI Text API (GET method)
    
    Creates an async job that generates text based on the prompt.
    """
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Generate text using Pollinations Text API
            generated_text = await pollinations_service.generate_text(
                prompt=data["prompt"],
                model=data.get("model", "openai"),
                seed=data.get("seed"),
                temperature=data.get("temperature"),
                top_p=data.get("top_p"),
                presence_penalty=data.get("presence_penalty"),
                frequency_penalty=data.get("frequency_penalty"),
                system=data.get("system"),
                json_mode=data.get("json_mode", False),
                referrer=data.get("referrer")
            )
            
            generation_time = time.time() - start_time
            
            return {
                "text": generated_text,
                "model_used": data.get("model", "openai"),
                "generation_time": generation_time,
                "prompt": data["prompt"],
                "character_count": len(generated_text)
            }
            
        except CircuitBreakerError as e:
            logger.error(f"Circuit breaker open for text generation: {e}")
            raise Exception("Text generation service is temporarily unavailable due to repeated failures. Please try again in a few minutes.")
        except APIError as e:
            logger.error(f"API error in text generation: {e} (status: {e.status_code}, type: {e.error_type})")
            if e.error_type == "retry_exhausted":
                raise Exception(str(e))
            elif e.status_code and e.status_code >= 500:
                raise Exception("Pollinations API is experiencing server issues. Please try again later.")
            elif e.status_code and e.status_code == 429:
                raise Exception("Rate limit exceeded. Please wait a moment and try again.")
            else:
                raise Exception(f"Text generation failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error generating text: {e}")
            raise Exception(f"Text generation failed due to an unexpected error: {str(e)}")
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_TEXT,
        process_func=process_wrapper,
        data=request.model_dump()
    )
    
    return JobResponse(job_id=job_id)


@router.get("/text/generate/{job_id}", response_model=JobStatusResponse)
async def get_text_generation_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of a text generation job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/chat/completions", response_model=JobResponse)
async def create_chat_completion(
    request: PollinationsChatRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a chat completion using Pollinations.AI Chat API (POST method)
    
    Supports advanced features like vision, function calling, and multimodal inputs.
    Creates an async job that processes the chat request.
    """
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Process chat completion using Pollinations Chat API
            response_data = await pollinations_service.generate_text_chat(
                messages=data["messages"],
                model=data.get("model", "openai"),
                seed=data.get("seed"),
                temperature=data.get("temperature"),
                top_p=data.get("top_p"),
                presence_penalty=data.get("presence_penalty"),
                frequency_penalty=data.get("frequency_penalty"),
                stream=False,  # No streaming in async jobs
                json_mode=data.get("json_mode", False),
                tools=data.get("tools"),
                tool_choice=data.get("tool_choice"),
                referrer=data.get("referrer")
            )
            
            generation_time = time.time() - start_time
            
            # Extract assistant message content
            assistant_content = ""
            if response_data.get("choices") and len(response_data["choices"]) > 0:
                choice = response_data["choices"][0]
                if choice.get("message", {}).get("content"):
                    assistant_content = choice["message"]["content"]
            
            return {
                "response": response_data,
                "assistant_message": assistant_content,
                "model_used": data.get("model", "openai"),
                "generation_time": generation_time,
                "message_count": len(data["messages"]),
                "has_tool_calls": bool(response_data.get("choices", [{}])[0].get("message", {}).get("tool_calls"))
            }
            
        except CircuitBreakerError as e:
            logger.error(f"Circuit breaker open for chat completion: {e}")
            raise Exception("Chat completion service is temporarily unavailable due to repeated failures. Please try again in a few minutes.")
        except APIError as e:
            logger.error(f"API error in chat completion: {e} (status: {e.status_code}, type: {e.error_type})")
            if e.error_type == "retry_exhausted":
                raise Exception(str(e))
            elif e.status_code and e.status_code >= 500:
                raise Exception("Pollinations API is experiencing server issues. Please try again later.")
            elif e.status_code and e.status_code == 429:
                raise Exception("Rate limit exceeded. Please wait a moment and try again.")
            else:
                raise Exception(f"Chat completion failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in chat completion: {e}")
            raise Exception(f"Chat completion failed due to an unexpected error: {str(e)}")
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_TEXT,
        process_func=process_wrapper,
        data=request.model_dump()
    )
    
    return JobResponse(job_id=job_id)


@router.get("/chat/completions/{job_id}", response_model=JobStatusResponse)
async def get_chat_completion_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of a chat completion job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


# Synchronous endpoints for immediate responses (suitable for simple requests)

@router.post("/text/generate/sync")
async def generate_text_sync(
    request: PollinationsTextRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate text synchronously using Pollinations.AI Text API
    
    Returns the generated text immediately. Use for short prompts only.
    """
    try:
        start_time = time.time()
        
        generated_text = await pollinations_service.generate_text(
            prompt=request.prompt,
            model=request.model,
            seed=request.seed,
            temperature=request.temperature,
            top_p=request.top_p,
            presence_penalty=request.presence_penalty,
            frequency_penalty=request.frequency_penalty,
            system=request.system,
            json_mode=request.json_mode,
            referrer=request.referrer
        )
        
        generation_time = time.time() - start_time
        
        return {
            "text": generated_text,
            "model_used": request.model,
            "generation_time": generation_time,
            "prompt": request.prompt,
            "character_count": len(generated_text)
        }
        
    except CircuitBreakerError as e:
        logger.error(f"Circuit breaker open for sync text generation: {e}")
        raise HTTPException(
            status_code=503,
            detail="Text generation service is temporarily unavailable due to repeated failures. Please try again in a few minutes."
        )
    except APIError as e:
        logger.error(f"API error in sync text generation: {e} (status: {e.status_code}, type: {e.error_type})")
        if e.error_type == "retry_exhausted":
            raise HTTPException(status_code=503, detail=str(e))
        elif e.status_code and e.status_code >= 500:
            raise HTTPException(status_code=503, detail="Pollinations API is experiencing server issues. Please try again later.")
        elif e.status_code and e.status_code == 429:
            raise HTTPException(status_code=429, detail="Rate limit exceeded. Please wait a moment and try again.")
        elif e.status_code and e.status_code >= 400:
            raise HTTPException(status_code=400, detail=f"Invalid request: {str(e)}")
        else:
            raise HTTPException(status_code=500, detail=f"Text generation failed: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in sync text generation: {e}")
        raise HTTPException(status_code=500, detail=f"Text generation failed due to an unexpected error: {str(e)}")


@router.post("/chat/completions/sync")
async def create_chat_completion_sync(
    request: PollinationsChatRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a chat completion synchronously using Pollinations.AI Chat API
    
    Returns the chat response immediately. Use for simple conversations only.
    """
    try:
        start_time = time.time()
        
        response_data = await pollinations_service.generate_text_chat(
            messages=[msg.model_dump() for msg in request.messages],
            model=request.model,
            seed=request.seed,
            temperature=request.temperature,
            top_p=request.top_p,
            presence_penalty=request.presence_penalty,
            frequency_penalty=request.frequency_penalty,
            stream=False,
            json_mode=request.json_mode,
            tools=request.tools,
            tool_choice=request.tool_choice,
            referrer=request.referrer
        )
        
        generation_time = time.time() - start_time
        
        # Add metadata
        response_data["_metadata"] = {
            "generation_time": generation_time,
            "model_used": request.model,
            "message_count": len(request.messages)
        }
        
        return response_data
        
    except CircuitBreakerError as e:
        logger.error(f"Circuit breaker open for sync chat completion: {e}")
        raise HTTPException(
            status_code=503,
            detail="Chat completion service is temporarily unavailable due to repeated failures. Please try again in a few minutes."
        )
    except APIError as e:
        logger.error(f"API error in sync chat completion: {e} (status: {e.status_code}, type: {e.error_type})")
        if e.error_type == "retry_exhausted":
            raise HTTPException(status_code=503, detail=str(e))
        elif e.status_code and e.status_code >= 500:
            raise HTTPException(status_code=503, detail="Pollinations API is experiencing server issues. Please try again later.")
        elif e.status_code and e.status_code == 429:
            raise HTTPException(status_code=429, detail="Rate limit exceeded. Please wait a moment and try again.")
        elif e.status_code and e.status_code >= 400:
            raise HTTPException(status_code=400, detail=f"Invalid request: {str(e)}")
        else:
            raise HTTPException(status_code=500, detail=f"Chat completion failed: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error in sync chat completion: {e}")
        raise HTTPException(status_code=500, detail=f"Chat completion failed due to an unexpected error: {str(e)}")