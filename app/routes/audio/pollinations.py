"""
Pollinations.AI Audio Generation Routes

Provides endpoints for text-to-speech and audio transcription using Pollinations.AI API
integrated with the Ouinhi job queue system.
"""

import uuid
import time
import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse

from app.models import (
    JobResponse, 
    JobStatusResponse, 
    JobType,
    PollinationsAudioRequest,
    PollinationsTranscriptionRequest,
    PollinationsResult
)
from app.services.job_queue import job_queue
from app.services.pollinations_service import pollinations_service
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/pollinations", tags=["Pollinations Audio"])


@router.post("/audio/tts", response_model=JobResponse)
async def text_to_speech(
    request: PollinationsAudioRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate speech audio from text using Pollinations.AI TTS
    
    Creates an async job that converts text to speech and saves the audio to S3.
    """
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Generate TTS audio using Pollinations API
            audio_bytes = await pollinations_service.generate_audio_tts(
                text=data["text"],
                voice=data.get("voice", "alloy"),
                model=data.get("model", "openai-audio")
            )
            
            # Save to S3
            filename = f"pollinations-tts-{_job_id}.mp3"
            s3_url = await pollinations_service.save_generated_content_to_s3(
                audio_bytes,
                filename,
                "audio/mpeg"
            )
            
            generation_time = time.time() - start_time
            
            return {
                "content_url": s3_url,
                "content_type": "audio/mpeg",
                "file_size": len(audio_bytes),
                "generation_time": generation_time,
                "model_used": data.get("model", "openai-audio"),
                "voice_used": data.get("voice", "alloy"),
                "text": data["text"],
                "text_length": len(data["text"])
            }
            
        except Exception as e:
            logger.error(f"Error generating TTS audio: {e}")
            raise Exception(f"TTS generation failed: {str(e)}")
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_AUDIO,
        process_func=process_wrapper,
        data=request.model_dump()
    )
    
    return JobResponse(job_id=job_id)


@router.get("/audio/tts/{job_id}", response_model=JobStatusResponse)
async def get_tts_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of a TTS generation job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/audio/transcribe", response_model=JobResponse)
async def transcribe_audio(
    file: UploadFile = File(...),
    question: str = "Transcribe this audio",
    api_key: str = Depends(get_api_key)
):
    """
    Transcribe audio file using Pollinations.AI STT
    
    Creates an async job that transcribes uploaded audio file to text.
    """
    # Validate file type
    if not file.content_type or not file.content_type.startswith("audio/"):
        raise HTTPException(status_code=400, detail="File must be an audio file")
    
    # Determine audio format
    audio_format = "wav"  # Default
    if file.content_type == "audio/mpeg" or (file.filename and file.filename.endswith(".mp3")):
        audio_format = "mp3"
    elif file.content_type == "audio/wav" or (file.filename and file.filename.endswith(".wav")):
        audio_format = "wav"
    else:
        # Try to guess from filename
        if file.filename and "." in file.filename:
            ext = file.filename.split(".")[-1].lower()
            if ext in ["mp3", "wav"]:
                audio_format = ext
    
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Transcribe audio using Pollinations API
            transcription = await pollinations_service.transcribe_audio(
                audio_data=data["audio_data"],
                audio_format=data["audio_format"],
                question=data.get("question", "Transcribe this audio")
            )
            
            generation_time = time.time() - start_time
            
            return {
                "transcription": transcription,
                "audio_format": data["audio_format"],
                "generation_time": generation_time,
                "file_name": data.get("file_name"),
                "file_size": len(data["audio_data"]),
                "character_count": len(transcription)
            }
            
        except Exception as e:
            logger.error(f"Error transcribing audio: {e}")
            raise Exception(f"Audio transcription failed: {str(e)}")
    
    # Read file data
    audio_data = await file.read()
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_AUDIO,
        process_func=process_wrapper,
        data={
            "audio_data": audio_data,
            "audio_format": audio_format,
            "question": question,
            "file_name": file.filename
        }
    )
    
    return JobResponse(job_id=job_id)


@router.get("/audio/transcribe/{job_id}", response_model=JobStatusResponse)
async def get_transcription_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of an audio transcription job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


# Synchronous endpoints for immediate responses

@router.post("/audio/tts/sync")
async def text_to_speech_sync(
    request: PollinationsAudioRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate speech audio synchronously using Pollinations.AI TTS
    
    Returns audio file info immediately. Use for short text only.
    """
    try:
        start_time = time.time()
        
        # Generate TTS audio
        audio_bytes = await pollinations_service.generate_audio_tts(
            text=request.text,
            voice=request.voice,
            model=request.model
        )
        
        # Save to S3
        job_id = str(uuid.uuid4())
        filename = f"pollinations-tts-sync-{job_id}.mp3"
        s3_url = await pollinations_service.save_generated_content_to_s3(
            audio_bytes,
            filename,
            "audio/mpeg"
        )
        
        generation_time = time.time() - start_time
        
        return {
            "content_url": s3_url,
            "content_type": "audio/mpeg",
            "file_size": len(audio_bytes),
            "generation_time": generation_time,
            "model_used": request.model,
            "voice_used": request.voice,
            "text": request.text,
            "text_length": len(request.text)
        }
        
    except Exception as e:
        logger.error(f"Error in sync TTS generation: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")


@router.get("/voices")
async def list_available_voices(api_key: str = Depends(get_api_key)):
    """List available TTS voices for Pollinations audio generation"""
    try:
        # Get text models data which includes voice information
        models_data = await pollinations_service.list_text_models()
        
        # Extract voice information from models data
        voices = []
        
        # Standard OpenAI voices (always available)
        standard_voices = [
            {"name": "alloy", "description": "Neutral, balanced voice"},
            {"name": "echo", "description": "Male, clear voice"},
            {"name": "fable", "description": "Expressive, storytelling voice"},
            {"name": "onyx", "description": "Deep, authoritative voice"},
            {"name": "nova", "description": "Young, energetic voice"},
            {"name": "shimmer", "description": "Soft, whispery voice"}
        ]
        
        voices.extend(standard_voices)
        
        # Try to extract additional voices from API response
        if isinstance(models_data, dict):
            openai_audio_data = models_data.get("openai-audio", {})
            if isinstance(openai_audio_data, dict) and "voices" in openai_audio_data:
                api_voices = openai_audio_data["voices"]
                if isinstance(api_voices, list):
                    for voice in api_voices:
                        if isinstance(voice, str) and voice not in [v["name"] for v in voices]:
                            voices.append({"name": voice, "description": "Available voice"})
        
        return {
            "voices": voices,
            "model": "openai-audio",
            "total_count": len(voices)
        }
        
    except Exception as e:
        logger.error(f"Error fetching voices: {e}")
        # Return standard voices as fallback
        return {
            "voices": [
                {"name": "alloy", "description": "Neutral, balanced voice"},
                {"name": "echo", "description": "Male, clear voice"},
                {"name": "fable", "description": "Expressive, storytelling voice"},
                {"name": "onyx", "description": "Deep, authoritative voice"},
                {"name": "nova", "description": "Young, energetic voice"},
                {"name": "shimmer", "description": "Soft, whispery voice"}
            ],
            "model": "openai-audio",
            "total_count": 6,
            "note": "Fallback voice list - API may have additional voices available"
        }