"""
Routes for media transcription using Whisper.
"""
import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import AnyUrl

from app.models import JobResponse, JobStatusResponse, MediaTranscriptionRequest, MediaTranscriptionResult, JobType
from app.services.job_queue import job_queue
from app.services.media.transcription import transcription_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/transcriptions", tags=["audio"])


async def process_transcription(job_id: str, params: dict[str, Any]) -> dict[str, Any]:
    """
    Process a transcription job using the enhanced transcription service.
    
    Args:
        job_id: The job ID
        params: Transcription parameters
    
    Returns:
        Dict with transcription results
    """
    return await transcription_service.process_media_transcription(job_id, params)


@router.post("", response_model=JobResponse)
async def create_transcriptions_job(request: MediaTranscriptionRequest):
    """
    Create a job to transcribe media using Whisper.
    
    This endpoint accepts a media URL and transcribes it using Whisper.
    The transcription is processed asynchronously, and the results can be retrieved
    using the returned job_id.
    
    Args:
        request: Media transcription request
            - media_url: URL of the media file to be transcribed
            - include_text: Include plain text transcription in the response
            - include_srt: Include SRT format subtitles in the response
            - word_timestamps: Include timestamps for individual words
            - include_segments: Include timestamped segments in the response
            - language: Source language code for transcription (optional)
            - max_words_per_line: Maximum words per line in SRT (default: 10)
        
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Create job parameters
        job_params = {
            "media_url": str(request.media_url),
            "include_text": request.include_text,
            "include_srt": request.include_srt,
            "word_timestamps": request.word_timestamps,
            "include_segments": request.include_segments,
            "language": request.language,
            "max_words_per_line": request.max_words_per_line,
            "beam_size": request.beam_size
        }
        
        # Create and start the job using new job queue
        job_id = str(uuid.uuid4())
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.MEDIA_TRANSCRIPTION,
            process_func=process_transcription,
            data=job_params
        )
        
        logger.info(f"Created media transcription job: {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/{job_id}", response_model=JobStatusResponse)
async def get_transcriptions_job_status(job_id: str):
    """
    Get the status of a transcription job.
    
    This endpoint retrieves the current status of a transcription job. When the job
    is completed, the `result` field will contain a MediaTranscriptionResult object
    with the following possible fields:
    - `text`: The full text transcription of the media (if include_text was true)
    - `srt_url`: URL to the SRT subtitle file in S3 (if include_srt was true)
    - `words`: Word-level timestamps with time information (if word_timestamps was true)
    - `segments`: Timestamped segments with start/end times for larger chunks of speech (if include_segments was true)
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing:
        - job_id: The ID of the job
        - status: Current job status (pending, processing, completed, failed)
        - result: If completed, contains the transcription results (MediaTranscriptionResult)
        - error: If failed, contains error information
    """
    # Get job status using new job queue
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    # Return the current status
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    ) 