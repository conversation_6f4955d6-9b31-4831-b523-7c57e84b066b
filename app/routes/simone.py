from typing import Optional, List, Dict, Any
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, Field
import logging
import uuid

from app.services.job_queue import job_queue, JobType
from app.services.simone_service import SimoneService
from app.models import JobStatusResponse, JobResponse
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/simone", tags=["simone"])

# Initialize services
simone_service = SimoneService()


class SimoneVideoRequest(BaseModel):
    url: str = Field(..., description="Video URL to process")
    platform: Optional[str] = Field(None, description="Social media platform for post generation")
    cookies_content: Optional[str] = Field(None, description="Cookie content for authentication")
    cookies_url: Optional[str] = Field(None, description="Cookie URL for authentication")


class SimoneEnhancedRequest(BaseModel):
    url: str = Field(..., description="Video URL to process")
    include_topics: bool = Field(True, description="Include topic identification")
    include_x_thread: bool = Field(True, description="Include X thread generation")
    platforms: List[str] = Field(default=["x", "linkedin", "instagram"], description="Social media platforms")
    thread_config: Dict[str, Any] = Field(
        default={
            "max_posts": 8,
            "character_limit": 280,
            "thread_style": "viral"
        },
        description="Thread configuration"
    )
    cookies_content: Optional[str] = Field(None, description="Cookie content for authentication")
    cookies_url: Optional[str] = Field(None, description="Cookie URL for authentication")


@router.post("/video-to-blog", response_model=JobResponse)
async def create_video_to_blog_job(
    request: SimoneVideoRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a job to process a video into a blog post with screenshots.
    
    This endpoint downloads video/audio, transcribes content, generates a blog post,
    optionally creates social media posts, and extracts key video frames.
    """
    try:
        # Create job parameters
        job_params = request.dict()
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Create wrapper function that matches job queue signature
        async def process_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await simone_service.process_video_to_blog(data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.SIMONE_VIDEO_TO_BLOG,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created video-to-blog job {job_id} for URL: {request.url}")
        
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        logger.error(f"Error creating video-to-blog job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create job: {str(e)}")


@router.post("/viral-content", response_model=JobResponse)
async def create_viral_content_job(
    request: SimoneEnhancedRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Create a job for viral content generation with advanced features.
    
    This endpoint provides advanced features including:
    - Topic identification
    - X thread generation
    - Multi-platform social media posts
    - Blog post generation
    - Video frame extraction and scoring
    """
    try:
        # Create job parameters
        job_params = request.dict()
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Create wrapper function that matches job queue signature
        async def process_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await simone_service.process_video_with_enhanced_features(data)
        
        # Add job to queue
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.SIMONE_ENHANCED_PROCESSING,
            process_func=process_wrapper,
            data=job_params
        )
        
        logger.info(f"Created viral content job {job_id} for URL: {request.url}")
        
        return JobResponse(job_id=job_id)
        
    except Exception as e:
        logger.error(f"Error creating viral content job: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create job: {str(e)}")


@router.get("/video-to-blog/{job_id}", response_model=JobStatusResponse)
async def get_video_to_blog_status(job_id: str, api_key: str = Depends(get_api_key)):
    """Get the status of a video-to-blog processing job."""
    try:
        job_info = await job_queue.get_job_info(job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobStatusResponse(
            job_id=job_id,
            status=job_info.status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")


@router.get("/viral-content/{job_id}", response_model=JobStatusResponse)
async def get_viral_content_status(job_id: str, api_key: str = Depends(get_api_key)):
    """Get the status of a viral content generation job."""
    try:
        job_info = await job_queue.get_job_info(job_id)
        if not job_info:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return JobStatusResponse(
            job_id=job_id,
            status=job_info.status,
            result=job_info.result,
            error=job_info.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get job status: {str(e)}")