"""
Routes for S3 file uploads.
"""
from fastapi import APIRouter, UploadFile, File, Form, Depends, HTTPException, status
from app.models import S3UploadRequest, JobResponse, JobStatusResponse
from typing import Optional
from app.services.s3_service import s3_upload_service
from app.services.job_queue import job_queue
from app.utils.auth import get_api_key
import uuid
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/s3", tags=["s3"])


@router.post("/upload", response_model=JobResponse)
async def upload_file(
    file: Optional[UploadFile] = File(None),
    url: Optional[str] = Form(None),
    file_name: Optional[str] = Form(None),
    public: Optional[str] = Form("true"),
    api_key: str = Depends(get_api_key),
):
    """
    Upload a file to S3 from either a file upload or URL.
    
    Parameters:
    - file: File to upload (multipart form data)
    - url: URL to download and upload to S3
    - file_name: Custom filename (optional)
    - public: Whether the file should be publicly accessible (default: True)
    
    Note: Provide either 'file' or 'url', not both.
    """
    try:
        # Convert public string to boolean
        public_bool = public.lower() in ('true', '1', 'yes') if public else True
        logger.info(f"S3 upload request: file={file is not None}, url={url}, file_name={file_name}, public={public_bool}")
        
        # Validate that exactly one input method is provided
        if not file and not url:
            logger.error("No file or URL provided")
            raise HTTPException(
                status_code=400, 
                detail="Either 'file' or 'url' parameter must be provided"
            )
        
        if file and url:
            logger.error("Both file and URL provided")
            raise HTTPException(
                status_code=400, 
                detail="Cannot provide both 'file' and 'url' parameters. Choose one."
            )
            
        # Additional file validation
        if file:
            if not file.filename:
                logger.error("File provided but filename is empty")
                raise HTTPException(
                    status_code=400,
                    detail="Uploaded file must have a filename"
                )
            if file.size == 0:
                logger.error("File provided but size is 0")
                raise HTTPException(
                    status_code=400,
                    detail="Uploaded file cannot be empty"
                )
        
        job_id = str(uuid.uuid4())
        
        if file:
            # Handle file upload
            logger.info(f"Processing file upload: {file.filename}, size: {file.size}")
            result = await s3_upload_service.upload_file(
                job_id=job_id,
                file=file,
                file_name=file_name,
                public=public_bool,
            )
        else:
            # Handle URL upload
            logger.info(f"Processing URL upload: {url}")
            result = await s3_upload_service.upload_from_url(
                job_id=job_id,
                url=url,
                file_name=file_name,
                public=public_bool,
            )
        
        logger.info(f"S3 upload job created successfully: {result}")
        return JobResponse(job_id=result["job_id"])
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create S3 upload job: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/upload/{job_id}", response_model=JobStatusResponse)
async def get_upload_status(job_id: str, api_key: str = Depends(get_api_key)):
    """
    Get the status of an S3 upload job.
    
    Args:
        job_id: The ID of the job to check
        
    Returns:
        JobStatusResponse: Current status, result, and error information
        
    Raises:
        HTTPException: If the job is not found
    """
    try:
        job = await job_queue.get_job(job_id)
        
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )
        
        return JobStatusResponse(
            job_id=job.id,
            status=job.status,
            result=job.result,
            error=job.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get job status"
        )
