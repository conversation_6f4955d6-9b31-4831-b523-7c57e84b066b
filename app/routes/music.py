"""
Music management endpoints for the API.
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from app.services.music_service import music_service
from app.utils.auth import get_api_key
import os

router = APIRouter(prefix="/v1/music", tags=["Music"])

@router.get("/tracks", response_model=dict)
async def list_music_tracks(
    mood: Optional[str] = Query(None, description="Filter tracks by mood (sad, happy, chill, etc.)"),
    _: str = Depends(get_api_key)
):
    """
    Get available background music tracks.
    
    Returns a list of all available music tracks with metadata including
    mood, duration, and file information. Tracks can be filtered by mood.
    
    **Parameters:**
    - mood: Optional mood filter (sad, happy, chill, dark, etc.)
    
    **Returns:**
    - tracks: List of music track objects with metadata
    - total: Total number of tracks
    - moods: Available mood categories
    """
    try:
        if mood:
            tracks = music_service.get_tracks_by_mood(mood)
            if not tracks:
                available_moods = music_service.get_available_moods()
                raise HTTPException(
                    status_code=404, 
                    detail=f"No tracks found for mood '{mood}'. Available moods: {', '.join(available_moods)}"
                )
        else:
            tracks = music_service.get_all_tracks()
        
        return {
            "success": True,
            "tracks": tracks,
            "total": len(tracks),
            "moods": music_service.get_available_moods()
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get music tracks: {str(e)}")

@router.get("/moods", response_model=dict)
async def list_music_moods(
    _: str = Depends(get_api_key)
):
    """
    Get available music mood categories.
    
    Returns a list of all available music mood categories that can be used
    to filter background music tracks.
    
    **Returns:**
    - moods: List of available mood categories
    """
    try:
        moods = music_service.get_available_moods()
        
        # Get track counts by mood
        mood_counts = {}
        for mood in moods:
            tracks = music_service.get_tracks_by_mood(mood)
            mood_counts[mood] = len(tracks)
        
        return {
            "success": True,
            "moods": moods,
            "mood_counts": mood_counts,
            "total_moods": len(moods)
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get music moods: {str(e)}")

@router.get("/track/{filename}", response_model=dict)
async def get_music_track(
    filename: str,
    _: str = Depends(get_api_key)
):
    """
    Get details for a specific music track.
    
    Returns detailed information about a specific music track by filename.
    
    **Parameters:**
    - filename: Name of the music file
    
    **Returns:**
    - track: Music track object with metadata
    """
    try:
        track = music_service.get_track_by_file(filename)
        if not track:
            raise HTTPException(status_code=404, detail=f"Music track not found: {filename}")
        
        return {
            "success": True,
            "track": track
        }
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get music track: {str(e)}")

@router.get("/debug/paths", response_model=dict)
async def debug_music_paths(
    _: str = Depends(get_api_key)
):
    """
    Debug endpoint to check music file paths and accessibility.
    
    **Returns:**
    - music_dir: Current music directory path
    - directory_exists: Whether the directory exists
    - files_found: List of files in the directory
    - sample_paths: Sample file paths and their existence status
    """
    try:
        music_dir = music_service.music_dir
        directory_exists = os.path.exists(music_dir)
        
        files_found = []
        sample_paths = {}
        
        if directory_exists:
            files_found = os.listdir(music_dir)
            # Test first 3 files for path resolution
            for filename in files_found[:3]:
                full_path = music_service.get_track_path(filename)
                sample_paths[filename] = {
                    "expected_path": os.path.join(music_dir, filename),
                    "resolved_path": full_path,
                    "exists": full_path is not None
                }
        
        # Test specific chill track
        chill_tracks = music_service.get_tracks_by_mood("chill")
        chill_path_test = {}
        if chill_tracks:
            test_filename = chill_tracks[0]["file"]
            chill_path_test = {
                "filename": test_filename,
                "track_data": chill_tracks[0],
                "resolved_path": music_service.get_track_path(test_filename),
                "expected_path": os.path.join(music_dir, test_filename)
            }
        
        return {
            "success": True,
            "music_dir": music_dir,
            "directory_exists": directory_exists,
            "files_count": len(files_found),
            "files_found": files_found[:10],  # First 10 files
            "sample_paths": sample_paths,
            "chill_track_test": chill_path_test
        }
    
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to debug music paths: {str(e)}")

