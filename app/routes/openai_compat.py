"""
OpenAI-compatible endpoints for TTS models and voices.
"""
from fastapi import APIRouter, HTTPException, Request, Depends
from fastapi.responses import StreamingResponse
from app.models import TextToSpeechRequest
from app.services.audio.tts_service import tts_service
from app.services.audio.edge_tts_service import edge_tts_service
from app.utils.auth import get_api_key
import logging
import tempfile
import os
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1", tags=["openai-compatibility"])


@router.get("/models")
@router.post("/models")
async def list_models():
    """
    List available TTS models (OpenAI-compatible format).
    
    Returns:
        Dictionary with models list
    """
    try:
        models = tts_service.get_models_formatted()
        return {"models": models}
    except Exception as e:
        logger.error(f"Failed to get models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/audio/models")
@router.post("/audio/models")
async def list_audio_models():
    """
    List available audio/TTS models (OpenAI-compatible format).
    
    Returns:
        Dictionary with models list
    """
    return await list_models()


@router.get("/audio/voices")
@router.post("/audio/voices")
async def list_audio_voices():
    """
    List available voices for audio generation (OpenAI-compatible format).
    
    Returns:
        Dictionary with voices list
    """
    try:
        voices = tts_service.get_voices_formatted()
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


@router.get("/voices")
@router.post("/voices")
async def list_voices(request: Request = None):
    """
    List Edge TTS voices with optional language filtering.
    
    Returns:
        Dictionary with voices list
    """
    try:
        # Get language parameter from query string or request body
        language = None
        if request.method == "GET":
            language = request.query_params.get("language") or request.query_params.get("locale")
        elif request.method == "POST" and hasattr(request, "json"):
            try:
                data = await request.json()
                language = data.get("language") or data.get("locale")
            except:
                pass
        
        voices = await edge_tts_service.get_available_voices(language)
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


@router.get("/voices/all")
@router.post("/voices/all")
async def list_all_voices():
    """
    List all available Edge TTS voices.
    
    Returns:
        Dictionary with all voices
    """
    try:
        voices = await edge_tts_service.get_available_voices("all")
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Failed to get all voices: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get voices: {str(e)}")


# ElevenLabs compatibility endpoints
@router.post("/elevenlabs/text-to-speech/{voice_id}")
async def elevenlabs_text_to_speech(voice_id: str, request: Request, api_key: str = Depends(get_api_key)):
    """
    ElevenLabs-compatible text-to-speech endpoint.
    
    Args:
        voice_id: Voice identifier (Edge TTS voice name)
        request: HTTP request with JSON payload
        
    Returns:
        Audio file response
    """
    try:
        # Parse the incoming JSON payload
        try:
            payload = await request.json()
            if not payload or 'text' not in payload:
                raise HTTPException(status_code=400, detail="Missing 'text' in request body")
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Invalid JSON payload: {str(e)}")

        text = payload['text']
        
        # Use Edge TTS service to generate speech
        audio_file_path = await edge_tts_service.generate_speech(
            text=text,
            voice=voice_id,
            response_format='mp3',
            speed=1.0
        )
        
        # Read the file and return as streaming response
        def file_generator():
            with open(audio_file_path, 'rb') as f:
                yield f.read()
            # Clean up the file after sending
            try:
                os.unlink(audio_file_path)
            except OSError:
                pass
        
        return StreamingResponse(
            file_generator(),
            media_type="audio/mpeg",
            headers={
                'Content-Disposition': 'attachment; filename="speech.mp3"'
            }
        )
        
    except Exception as e:
        logger.error(f"ElevenLabs TTS failed: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")


# Azure Cognitive Services compatibility
@router.post("/azure/cognitiveservices/v1")
async def azure_cognitive_services_tts(request: Request, api_key: str = Depends(get_api_key)):
    """
    Azure Cognitive Services-compatible text-to-speech endpoint.
    Accepts SSML payloads and converts them to Edge TTS requests.
    
    Args:
        request: HTTP request with SSML payload
        
    Returns:
        Audio file response
    """
    try:
        # Parse the SSML payload
        try:
            ssml_data = await request.body()
            ssml_string = ssml_data.decode('utf-8')
            if not ssml_string:
                raise HTTPException(status_code=400, detail="Missing SSML payload")

            # Extract the text and voice from SSML
            root = ET.fromstring(ssml_string)
            
            # Find the voice element
            voice_element = root.find('.//{http://www.w3.org/2001/10/synthesis}voice')
            if voice_element is None:
                raise HTTPException(status_code=400, detail="No voice element found in SSML")
            
            text = voice_element.text or ""
            voice = voice_element.get('name')
            
            if not voice:
                raise HTTPException(status_code=400, detail="No voice name specified in SSML")
                
        except ET.ParseError as e:
            raise HTTPException(status_code=400, detail=f"Invalid SSML payload: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=400, detail=f"Error parsing SSML: {str(e)}")

        # Use Edge TTS service to generate speech
        audio_file_path = await edge_tts_service.generate_speech(
            text=text,
            voice=voice,
            response_format='mp3',
            speed=1.0
        )
        
        # Read the file and return as streaming response
        def file_generator():
            with open(audio_file_path, 'rb') as f:
                yield f.read()
            # Clean up the file after sending
            try:
                os.unlink(audio_file_path)
            except OSError:
                pass
        
        return StreamingResponse(
            file_generator(),
            media_type="audio/mpeg",
            headers={
                'Content-Disposition': 'attachment; filename="speech.mp3"'
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Azure TTS failed: {e}")
        raise HTTPException(status_code=500, detail=f"TTS generation failed: {str(e)}")