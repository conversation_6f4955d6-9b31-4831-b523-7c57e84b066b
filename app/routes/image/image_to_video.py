"""
Routes for image to video conversion.
"""
import uuid
import logging
from typing import Any
from fastapi import APIRouter, HTTPException
from app.models import (
    ImageToVideoRequest, 
    JobResponse, 
    JobStatusResponse,
    JobType
)
from app.services.job_queue import job_queue
from app.services.image.image_to_video import image_to_video_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/v1/videos", tags=["videos"])


@router.post("/generations", response_model=JobResponse)
async def create_video_generation_job(request: ImageToVideoRequest):
    """
    Create an optimized job to convert an image to a video with optional audio and captions.
    
    This endpoint is a high-performance version of /to-video that uses a 
    streamlined processing pipeline to significantly reduce S3 uploads/downloads and 
    processing time. It combines multiple steps into fewer FFmpeg operations.
    
    1. Converts an image to video with a Ken Burns zoom effect
    2. Optionally generates narrator audio from text or uses provided narrator audio URL
    3. Optionally adds background music (can be from YouTube)
    4. Mixes the video, narrator audio, and background music
    5. Optionally adds captions to the video
    
    Args:
        request: Comprehensive request with the following parameters:
            - image_url: URL of the image to convert to video
            - video_length, frame_rate, zoom_speed: Video parameters
            - narrator_speech_text: Text to convert to speech (optional)
            - voice: Voice to use for speech synthesis (optional)
            - narrator_audio_url: URL of audio file to add as narration (optional, ignored if narrator_speech_text is provided)
            - narrator_vol: Volume level for the narrator audio track (0-100)
            - background_music_url: URL of background music to add (optional, can be YouTube URL)
            - background_music_vol: Volume level for the background music track (0-100, default: 20)
            - should_add_captions: Whether to automatically add captions by transcribing audio
            - caption_properties: Styling properties for captions (optional) including:
                - max_words_per_line: Control how many words appear per line of captions (1-20, default: 10)
                - font_size, font_family, color, position, etc.
            - match_length: Whether to match the output length to 'audio' or 'video'
            
    Returns:
        JobResponse with job_id that can be used to check the status of the job
    """
    try:
        # Validate match_length parameter
        if request.match_length not in ["audio", "video"]:
            raise ValueError("match_length must be either 'audio' or 'video'")
        
        # Create a new job with all the parameters
        params = {
            "image_url": str(request.image_url),
            "video_length": request.video_length,
            "frame_rate": request.frame_rate,
            "zoom_speed": request.zoom_speed,
            "match_length": request.match_length,
            "narrator_vol": request.narrator_vol,
            "should_add_captions": request.should_add_captions,
            "effect_type": request.effect_type,
            "pan_direction": request.pan_direction,
            "ken_burns_keypoints": request.ken_burns_keypoints
        }
        
        # Add optional narrator audio parameters if provided
        if request.narrator_speech_text:
            params["narrator_speech_text"] = request.narrator_speech_text
            params["voice"] = request.voice
            params["provider"] = request.provider
        elif request.narrator_audio_url:
            params["narrator_audio_url"] = str(request.narrator_audio_url)
        
        # Add optional background music parameters if provided
        if request.background_music_url:
            params["background_music_url"] = str(request.background_music_url)
            params["background_music_vol"] = request.background_music_vol
        
        if request.caption_properties:
            params["caption_properties"] = request.caption_properties.dict(
                exclude_none=True  # Only include non-None values
            )
        
        # Add caption language for transcription
        if request.caption_language:
            params["caption_language"] = request.caption_language
        
        # Create and start the job using new job queue
        job_id = str(uuid.uuid4())
        
        # Create a wrapper function that matches the expected signature
        async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
            return await image_to_video_service.image_to_video(data)
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_TO_VIDEO,
            process_func=process_wrapper,
            data=params
        )
        
        logger.info(f"Created image-to-video job: {job_id}")
        
        return JobResponse(job_id=job_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/generations/{job_id}", response_model=JobStatusResponse)
async def get_image_to_video_job_status(job_id: str):
    """
    Get the status of an image-to-video with audio and captions job.
    
    This is the status endpoint for jobs created through /generations.
    
    Args:
        job_id: ID of the job to get status for
        
    Returns:
        JobStatusResponse containing the job status and results when completed
    """
    job_info = await job_queue.get_job_info(job_id)
    if not job_info:
        raise HTTPException(status_code=404, detail=f"Job with ID {job_id} not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    ) 