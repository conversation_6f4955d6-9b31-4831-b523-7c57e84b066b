"""
Pollinations.AI Image Generation Routes

Provides endpoints for generating images using Pollinations.AI API
integrated with the Ouinhi job queue system.
"""

import uuid
import time
import logging
from typing import Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request
from fastapi.responses import JSONResponse

from app.models import (
    JobResponse, 
    JobStatusResponse, 
    JobType,
    PollinationsImageRequest,
    PollinationsVisionRequest,
    PollinationsResult
)
from app.services.job_queue import job_queue
from app.services.pollinations_service import pollinations_service
from app.utils.auth import get_api_key

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/pollinations", tags=["Pollinations Image"])


@router.post("/image/generate", response_model=JobResponse)
async def generate_image(
    request: PollinationsImageRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Generate an image using Pollinations.AI
    
    Creates an async job that generates an image based on the text prompt
    and saves it to S3 storage.
    """
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Generate image using Pollinations API
            image_bytes = await pollinations_service.generate_image(
                prompt=data["prompt"],
                model=data.get("model", "flux"),
                width=data.get("width", 1024),
                height=data.get("height", 1024),
                seed=data.get("seed"),
                enhance=data.get("enhance", False),
                nologo=data.get("nologo", False),
                safe=data.get("safe", False),
                transparent=data.get("transparent", False),
                image_url=data.get("image_url"),
                referrer=data.get("referrer")
            )
            
            # Determine content type based on model
            content_type = "image/png" if data.get("transparent") else "image/jpeg"
            file_extension = "png" if data.get("transparent") else "jpg"
            
            # Save to S3
            filename = f"pollinations-image-{_job_id}.{file_extension}"
            s3_url = await pollinations_service.save_generated_content_to_s3(
                image_bytes,
                filename,
                content_type
            )
            
            generation_time = time.time() - start_time
            
            return {
                "content_url": s3_url,
                "content_type": content_type,
                "file_size": len(image_bytes),
                "generation_time": generation_time,
                "model_used": data.get("model", "flux"),
                "prompt": data["prompt"],
                "dimensions": f"{data.get('width', 1024)}x{data.get('height', 1024)}"
            }
            
        except Exception as e:
            logger.error(f"Error generating image: {e}")
            raise Exception(f"Image generation failed: {str(e)}")
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_IMAGE,
        process_func=process_wrapper,
        data=request.model_dump()
    )
    
    return JobResponse(job_id=job_id)


@router.get("/image/generate/{job_id}", response_model=JobStatusResponse)
async def get_image_generation_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of an image generation job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/vision/analyze", response_model=JobResponse)
async def analyze_image_vision(
    request: PollinationsVisionRequest,
    api_key: str = Depends(get_api_key)
):
    """
    Analyze an image using Pollinations Vision API
    
    Creates an async job that analyzes an image and returns text description.
    """
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Analyze image using Pollinations Vision API
            analysis_text = await pollinations_service.analyze_image(
                image_url=data.get("image_url"),
                question=data.get("question", "What's in this image?"),
                model=data.get("model", "openai")
            )
            
            generation_time = time.time() - start_time
            
            return {
                "text": analysis_text,
                "model_used": data.get("model", "openai"),
                "generation_time": generation_time,
                "question": data.get("question", "What's in this image?"),
                "image_url": data.get("image_url")
            }
            
        except Exception as e:
            logger.error(f"Error analyzing image: {e}")
            raise Exception(f"Image analysis failed: {str(e)}")
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_VISION,
        process_func=process_wrapper,
        data=request.model_dump()
    )
    
    return JobResponse(job_id=job_id)


@router.post("/vision/analyze-upload", response_model=JobResponse)
async def analyze_uploaded_image(
    file: UploadFile = File(...),
    question: str = "What's in this image?",
    model: str = "openai",
    api_key: str = Depends(get_api_key)
):
    """
    Analyze an uploaded image using Pollinations Vision API
    
    Creates an async job that analyzes an uploaded image file.
    """
    if not file.content_type or not file.content_type.startswith("image/"):
        raise HTTPException(status_code=400, detail="File must be an image")
    
    job_id = str(uuid.uuid4())
    
    async def process_wrapper(_job_id: str, data: dict[str, Any]) -> dict[str, Any]:
        """Wrapper function for job queue processing"""
        start_time = time.time()
        
        try:
            # Read image data
            image_data = data["image_data"]
            
            # Analyze image using Pollinations Vision API
            analysis_text = await pollinations_service.analyze_image(
                image_data=image_data,
                question=data.get("question", "What's in this image?"),
                model=data.get("model", "openai")
            )
            
            generation_time = time.time() - start_time
            
            return {
                "text": analysis_text,
                "model_used": data.get("model", "openai"),
                "generation_time": generation_time,
                "question": data.get("question", "What's in this image?"),
                "file_name": data.get("file_name"),
                "file_size": len(image_data)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing uploaded image: {e}")
            raise Exception(f"Image analysis failed: {str(e)}")
    
    # Read file data
    file_data = await file.read()
    
    # Add job to queue
    await job_queue.add_job(
        job_id=job_id,
        job_type=JobType.POLLINATIONS_VISION,
        process_func=process_wrapper,
        data={
            "image_data": file_data,
            "question": question,
            "model": model,
            "file_name": file.filename
        }
    )
    
    return JobResponse(job_id=job_id)


@router.get("/vision/analyze/{job_id}", response_model=JobStatusResponse)
async def get_vision_analysis_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
):
    """Get the status of a vision analysis job"""
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.get("/models/image")
async def list_image_models(api_key: str = Depends(get_api_key)):
    """List available Pollinations image generation models"""
    try:
        # Check cache first
        cached_models = await pollinations_service.get_cached_models_list("pollinations:image_models")
        if cached_models:
            return {"models": cached_models}
        
        # Fetch from API
        models = await pollinations_service.list_image_models()
        
        # Cache for 1 hour
        await pollinations_service.cache_models_list("pollinations:image_models", models, 3600)
        
        return {"models": models}
        
    except Exception as e:
        logger.error(f"Error fetching image models: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch image models")


@router.get("/models/text")
async def list_text_models(api_key: str = Depends(get_api_key)):
    """List available Pollinations text/vision models and voices"""
    try:
        # Check cache first
        cached_models = await pollinations_service.get_cached_models_list("pollinations:text_models")
        if cached_models:
            return cached_models
        
        # Fetch from API
        models_data = await pollinations_service.list_text_models()
        
        # Cache for 1 hour
        await pollinations_service.cache_models_list("pollinations:text_models", models_data, 3600)
        
        return models_data
        
    except Exception as e:
        logger.error(f"Error fetching text models: {e}")
        raise HTTPException(status_code=500, detail="Failed to fetch text models")