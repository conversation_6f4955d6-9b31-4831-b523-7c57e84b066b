import uuid
from typing import Any
from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Form, Request
from typing import Optional, cast
from pydantic import BaseModel, Field
from app.models import JobResponse, JobStatusResponse, JobType
from app.services.job_queue import job_queue
from app.services.ai.together_ai_service import together_ai_service
from app.services.ai.flux_service import flux_service
from app.services.s3 import s3_service
from app.utils.auth import get_api_key

router = APIRouter(prefix="/v1/images", tags=["Image Generation"])

# Create a separate router for the simple image edit endpoint
simple_router = APIRouter(tags=["Simple Image Editing"])


class ImageGenerationRequest(BaseModel):
    """Request model for AI image generation."""
    prompt: str = Field(
        description="Text prompt for image generation (max 1000 characters).",
        max_length=1000
    )
    model: str = Field(
        default="black-forest-labs/FLUX.1-schnell-Free",
        description="Image generation model to use."
    )
    width: int = Field(
        default=576,
        ge=256,
        le=2048,
        description="Image width in pixels."
    )
    height: int = Field(
        default=1024,
        ge=256,
        le=2048,
        description="Image height in pixels."
    )
    steps: int = Field(
        default=4,
        ge=1,
        le=50,
        description="Number of inference steps."
    )
    provider: str = Field(
        default="together",
        description="Image generation provider: 'together' or 'flux'."
    )


class ImageGenerationResult(BaseModel):
    """Result model for image generation."""
    image_url: str = Field(
        description="URL to the generated image stored in S3."
    )
    prompt_used: str = Field(
        description="The prompt that was used for generation."
    )
    model_used: str = Field(
        description="The model that was used for generation."
    )
    dimensions: dict[str, int] = Field(
        description="Image dimensions (width, height)."
    )
    processing_time: float = Field(
        description="Processing time in seconds."
    )


class ImageEditRequest(BaseModel):
    """Request model for AI image editing."""
    prompt: str = Field(
        description="Text prompt describing the desired edit (max 1000 characters).",
        max_length=1000
    )
    guidance_scale: float = Field(
        default=3.5,
        ge=1.0,
        le=20.0,
        description="Guidance scale (higher = more prompt adherence)."
    )
    num_inference_steps: int = Field(
        default=20,
        ge=1,
        le=50,
        description="Number of inference steps."
    )
    seed: int | None = Field(
        default=None,
        description="Optional seed for reproducible results."
    )


class ImageEditResult(BaseModel):
    """Result model for image editing."""
    original_image_url: str = Field(
        description="URL to the original image stored in S3."
    )
    edited_image_url: str = Field(
        description="URL to the edited image stored in S3."
    )
    prompt_used: str = Field(
        description="The prompt that was used for editing."
    )
    processing_time: float = Field(
        description="Processing time in seconds."
    )


async def process_image_generation_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for image generation job processing."""
    import time
    import tempfile
    import os
    start_time = time.time()
    
    temp_file_path = None
    try:
        # Extract parameters
        prompt = data["prompt"]
        model = data.get("model", "black-forest-labs/FLUX.1-schnell")
        width = data.get("width", 576)
        height = data.get("height", 1024)
        steps = data.get("steps", 4)
        provider = data.get("provider", "together")
        
        if provider == "together":
            if not together_ai_service.is_available():
                raise ValueError("Together.ai service is not available (API key not configured)")
            
            # Generate image with Together.ai
            image_data = await together_ai_service.generate_image_from_b64(
                prompt=prompt,
                model=model,
                width=width,
                height=height,
                steps=steps
            )
        elif provider == "flux":
            if not flux_service.is_available():
                raise ValueError("Flux service is not available (API key or URL not configured)")
            
            # Generate image with Flux (using steps as num_inference_steps)
            image_data = await flux_service.generate_image(
                prompt=prompt,
                width=width,
                height=height,
                num_inference_steps=steps
            )
        else:
            raise ValueError(f"Unsupported provider: {provider}. Supported providers: 'together', 'flux'")
        
        # Save binary data to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(image_data)
            temp_file_path = temp_file.name
        
        # Upload to S3
        s3_path = f"generated-images/{job_id}.png"
        image_url = await s3_service.upload_file(
            file_path=temp_file_path,
            object_name=s3_path,
            content_type="image/png"
        )
        
        processing_time = time.time() - start_time
        
        result = ImageGenerationResult(
            image_url=image_url,
            prompt_used=prompt,
            model_used=model,
            dimensions={"width": width, "height": height},
            processing_time=processing_time
        )
        
        return result.model_dump()
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image generation failed: {str(e)}")
    finally:
        # Clean up temporary file
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                # Log error but don't fail the main operation
                print(f"Warning: Failed to clean up temporary file {temp_file_path}: {e}")


async def process_image_edit_wrapper(job_id: str, data: dict[str, Any]) -> dict[str, Any]:
    """Wrapper function for image editing job processing."""
    import time
    import tempfile
    import os
    start_time = time.time()
    
    original_temp_file = None
    edited_temp_file = None
    try:
        # Extract parameters
        prompt = data["prompt"]
        guidance_scale = data.get("guidance_scale", 3.5)
        num_inference_steps = data.get("num_inference_steps", 20)
        seed = data.get("seed")
        
        # Handle both old format (binary) and new format (base64)
        if "original_image_data_b64" in data:
            import base64
            original_image_data = base64.b64decode(data["original_image_data_b64"])
        else:
            # Fallback for old format (if any exist)
            original_image_data = data["original_image_data"]
        
        if not flux_service.is_available():
            raise ValueError("Flux service is not available (API key or URL not configured)")
        
        # Save original image to temporary file for S3 upload
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(original_image_data)
            original_temp_file = temp_file.name
        
        # Upload original image to S3
        original_s3_path = f"original-images/{job_id}.png"
        original_image_url = await s3_service.upload_file(
            file_path=original_temp_file,
            object_name=original_s3_path,
            content_type="image/png"
        )
        
        # Edit image with Flux
        edited_image_data = await flux_service.edit_image(
            image_bytes=original_image_data,
            prompt=prompt,
            guidance_scale=guidance_scale,
            num_inference_steps=num_inference_steps,
            seed=seed
        )
        
        # Save edited image to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as temp_file:
            temp_file.write(edited_image_data)
            edited_temp_file = temp_file.name
        
        # Upload edited image to S3
        edited_s3_path = f"edited-images/{job_id}.png"
        edited_image_url = await s3_service.upload_file(
            file_path=edited_temp_file,
            object_name=edited_s3_path,
            content_type="image/png"
        )
        
        processing_time = time.time() - start_time
        
        result = ImageEditResult(
            original_image_url=original_image_url,
            edited_image_url=edited_image_url,
            prompt_used=prompt,
            processing_time=processing_time
        )
        
        # Add image_url for media library compatibility
        result_dict = result.model_dump()
        result_dict['image_url'] = edited_image_url  # Primary URL for media library
        
        return result_dict
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image editing failed: {str(e)}")
    finally:
        # Clean up temporary files
        for temp_file_path in [original_temp_file, edited_temp_file]:
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    print(f"Warning: Failed to clean up temporary file {temp_file_path}: {e}")


@router.post("/generate", response_model=JobResponse)
async def generate_image(
    request: ImageGenerationRequest,
    _: str = Depends(get_api_key)
):
    """
    Generate an AI image from a text prompt.
    
    Creates an asynchronous job that generates an image using AI models like FLUX.
    
    **Supported Models:**
    - `black-forest-labs/FLUX.1-schnell`: Fast, high-quality image generation
    - More models may be added in the future
    
    **Features:**
    - High-quality AI image generation
    - Customizable dimensions (256x256 to 2048x2048)
    - Adjustable inference steps for quality vs speed
    - Automatic S3 storage with direct download URLs
    
    **Perfect for:**
    - Social media content creation
    - Video background generation
    - Art and design projects
    - Marketing materials
    - Creative workflows
    
    **Processing Time:** Typically 5-15 seconds depending on complexity and model.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    job_id = str(uuid.uuid4())
    
    try:
        # Validate provider and check availability
        if request.provider == "together":
            if not together_ai_service.is_available():
                raise HTTPException(
                    status_code=503,
                    detail="Together.ai service is currently unavailable (API key not configured)"
                )
        elif request.provider == "flux":
            if not flux_service.is_available():
                raise HTTPException(
                    status_code=503,
                    detail="Flux service is currently unavailable (API key or URL not configured)"
                )
        else:
            raise HTTPException(
                status_code=400, 
                detail="Unsupported provider. Supported providers: 'together', 'flux'"
            )
        
        # Prepare job data
        job_data = request.model_dump()
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_GENERATION,
            process_func=process_image_generation_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create image generation job: {str(e)}")


@router.get("/generate/{job_id}", response_model=JobStatusResponse)
async def get_image_generation_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of an image generation job.
    
    Poll this endpoint to monitor the progress of your image generation.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → AI model generating image
    3. `completed` → Image ready and uploaded to S3
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "image_url": "https://s3.../generated_image.png",
        "prompt_used": "beautiful sunset over mountains",
        "model_used": "black-forest-labs/FLUX.1-schnell",
        "dimensions": {"width": 576, "height": 1024},
        "processing_time": 12.5
    }
    ```
    
    **Download Links:**
    - `image_url`: Direct link to your generated image
    
    **Metadata:**
    - `processing_time`: Total generation and upload time in seconds
    - `dimensions`: Actual image dimensions
    - `model_used`: AI model that generated the image
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.post("/edit", response_model=JobResponse)
async def edit_image(
    prompt: str = Form(..., description="Text prompt describing the desired edit (max 1000 characters)."),
    image: UploadFile = File(..., description="Image file to edit (PNG, JPG, JPEG up to 10MB)."),
    guidance_scale: float = Form(3.5, ge=1.0, le=20.0, description="Guidance scale (higher = more prompt adherence)."),
    num_inference_steps: int = Form(20, ge=1, le=50, description="Number of inference steps."),
    seed: Optional[int] = Form(None, description="Optional seed for reproducible results."),
    _: str = Depends(get_api_key)
):
    """
    Edit an image using AI models (currently supports Flux only).
    
    Creates an asynchronous job that edits an uploaded image using Flux Kontext Dev model.
    
    **Features:**
    - High-quality AI image editing with Flux Kontext Dev
    - Upload any image (PNG, JPG, JPEG) up to 10MB
    - Natural language editing prompts
    - Adjustable guidance scale and inference steps
    - Optional seed for reproducible results
    - Automatic S3 storage with direct download URLs
    
    **Perfect for:**
    - Photo editing and enhancement
    - Style transfer and artistic effects
    - Object addition/removal/modification
    - Creative image transformations
    - Design iteration workflows
    
    **Processing Time:** Typically 10-30 seconds depending on complexity.
    
    **Aspect Ratio Requirements:** Image must have aspect ratio between 3:7 and 7:3.
    
    **Returns:** Job ID for status polling and result retrieval.
    """
    import logging
    logger = logging.getLogger(__name__)
    job_id = str(uuid.uuid4())
    
    try:
        # Validate prompt length
        if len(prompt) > 1000:
            raise HTTPException(
                status_code=400,
                detail="Prompt too long. Maximum 1000 characters allowed."
            )
        
        # Validate file type
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Invalid file type. Please upload a PNG or JPEG image."
            )
        
        logger.info(f"Image edit request received - prompt: '{prompt}', guidance_scale: {guidance_scale}, num_inference_steps: {num_inference_steps}, seed: {seed}")
        logger.info(f"Image info - filename: {image.filename}, content_type: {image.content_type}")
        
        # Check if Flux service is available
        if not flux_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="Image editing service is currently unavailable (Flux API key or URL not configured)"
            )
        
        # Read image data after all other validations
        image_data = await image.read()
        
        # Validate file size (10MB limit)
        if len(image_data) > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="Image file too large. Please upload an image smaller than 10MB."
            )
        
        # Convert image data to base64 for JSON serialization
        import base64
        image_data_b64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare job data
        job_data = {
            "prompt": prompt,
            "guidance_scale": guidance_scale,
            "num_inference_steps": num_inference_steps,
            "seed": seed,
            "original_image_data_b64": image_data_b64
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_EDITING,
            process_func=process_image_edit_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in image editing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create image editing job: {str(e)}")


# Simple image edit endpoint with API key requirement for external compatibility
@router.post("/edit_image", response_model=JobResponse)
async def simple_edit_image(
    prompt: str = Form(..., description="Text prompt describing the desired edit (max 1000 characters)."),
    image: UploadFile = File(..., description="Image file to edit (PNG, JPG, JPEG up to 10MB)."),
    guidance_scale: float = Form(3.5, description="Guidance scale (higher = more prompt adherence)."),
    num_inference_steps: int = Form(20, description="Number of inference steps."),
    seed: Optional[int] = Form(None, description="Optional seed for reproducible results."),
    _: str = Depends(get_api_key)
):
    """
    Simple image editing endpoint for external compatibility.
    
    This endpoint provides a straightforward interface for image editing with API key authentication,
    making it compatible with external services like n8n and other automation tools.
    
    **Parameters:**
    - prompt: Text description of the desired edit
    - image: Image file to edit (PNG, JPG, JPEG)
    - guidance_scale: How closely to follow the prompt (default: 3.5)
    - num_inference_steps: Number of denoising steps (default: 20)
    - seed: Optional seed for reproducible results
    
    **Returns:** Job ID for tracking the editing process
    """
    import logging
    logger = logging.getLogger(__name__)
    job_id = str(uuid.uuid4())
    
    try:
        # Validate prompt length
        if len(prompt) > 1000:
            raise HTTPException(
                status_code=400,
                detail="Prompt too long. Maximum 1000 characters allowed."
            )
        
        # Validate file type
        if not image.content_type or not image.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Invalid file type. Please upload a PNG or JPEG image."
            )
        
        logger.info(f"Simple image edit request received - prompt: '{prompt}', guidance_scale: {guidance_scale}, num_inference_steps: {num_inference_steps}, seed: {seed}")
        logger.info(f"Image info - filename: {image.filename}, content_type: {image.content_type}")
        
        # Check if Flux service is available
        if not flux_service.is_available():
            raise HTTPException(
                status_code=503,
                detail="Image editing service is currently unavailable (Flux API key or URL not configured)"
            )
        
        # Read image data after all other validations
        image_data = await image.read()
        
        # Validate file size (10MB limit)
        if len(image_data) > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="Image file too large. Please upload an image smaller than 10MB."
            )
        
        # Convert image data to base64 for JSON serialization
        import base64
        image_data_b64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare job data
        job_data = {
            "prompt": prompt,
            "guidance_scale": guidance_scale,
            "num_inference_steps": num_inference_steps,
            "seed": seed,
            "original_image_data_b64": image_data_b64
        }
        
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.IMAGE_EDITING,
            process_func=process_image_edit_wrapper,
            data=job_data
        )
        
        return JobResponse(job_id=job_id)
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in simple image editing: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create image editing job: {str(e)}")


@router.get("/edit/{job_id}", response_model=JobStatusResponse)
async def get_image_edit_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of an image editing job.
    
    Poll this endpoint to monitor the progress of your image editing.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → AI model editing image
    3. `completed` → Image ready and uploaded to S3
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "original_image_url": "https://s3.../original_image.png",
        "edited_image_url": "https://s3.../edited_image.png",
        "prompt_used": "make the sky more dramatic",
        "processing_time": 25.3
    }
    ```
    
    **Download Links:**
    - `original_image_url`: Link to your original uploaded image
    - `edited_image_url`: Link to the AI-edited result
    
    **Metadata:**
    - `processing_time`: Total editing and upload time in seconds
    - `prompt_used`: The exact prompt used for editing
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


@router.get("/edit_image/{job_id}", response_model=JobStatusResponse)
async def get_simple_image_edit_status(
    job_id: str,
    _: str = Depends(get_api_key)
):
    """
    Get the status and result of a simple image editing job.
    
    This endpoint provides a straightforward interface for checking image editing job status
    with API key authentication, making it compatible with external services like n8n
    and other automation tools.
    
    **Processing Stages:**
    1. `pending` → Job queued
    2. `processing` → AI model editing image
    3. `completed` → Image ready and uploaded to S3
    4. `failed` → Error occurred (check error field)
    
    **Result Format (when completed):**
    ```json
    {
        "original_image_url": "https://s3.../original_image.png",
        "edited_image_url": "https://s3.../edited_image.png",
        "prompt_used": "make the sky more dramatic",
        "processing_time": 25.3
    }
    ```
    
    **Download Links:**
    - `original_image_url`: Link to your original uploaded image
    - `edited_image_url`: Link to the AI-edited result
    
    **Metadata:**
    - `processing_time`: Total editing and upload time in seconds
    - `prompt_used`: The exact prompt used for editing
    """
    job_info = await job_queue.get_job_info(job_id)
    
    if not job_info:
        raise HTTPException(status_code=404, detail="Job not found")
    
    return JobStatusResponse(
        job_id=job_id,
        status=job_info.status,
        result=job_info.result,
        error=job_info.error
    )


