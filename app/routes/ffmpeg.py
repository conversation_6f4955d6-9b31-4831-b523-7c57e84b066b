"""
FFmpeg compose routes for complex media processing operations.
"""
import uuid
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks

from app.models import (
    FFmpegComposeRequest, 
    FFmpegComposeResult,
    JobResponse,
    JobStatusResponse,
    JobType
)
from app.utils.auth import get_api_key
from app.services.job_queue import job_queue
from app.services.ffmpeg_composer import ffmpeg_composer

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/v1/ffmpeg",
    tags=["ffmpeg"],
    responses={
        400: {"description": "Bad Request - Invalid parameters"},
        401: {"description": "Unauthorized - Invalid API key"},
        500: {"description": "Internal Server Error"}
    }
)

@router.post(
    "/compose",
    response_model=JobResponse,
    summary="Compose and Execute FFmpeg Commands",
    description="""
    Compose and execute complex FFmpeg commands from JSON configuration.
    
    This endpoint allows you to:
    - Process multiple input files with individual options
    - Apply complex filter graphs (simple and complex filters)
    - Configure multiple outputs with different settings
    - Map streams globally or per-output
    - Extract metadata (thumbnails, duration, bitrate, encoder info)
    - Process operations asynchronously with job tracking
    
    **Key Features:**
    - **Input Files**: Support for multiple input files with individual FFmpeg options
    - **Stream Mapping**: Global stream mapping or per-output stream mapping
    - **Filters**: Support for both simple filters (-vf, -af) and complex filter graphs
    - **Metadata Extraction**: Optional extraction of thumbnails, filesize, duration, bitrate, and encoder information
    - **Background Processing**: Jobs are processed asynchronously with status tracking
    - **Webhook Notifications**: Optional webhook notifications when jobs complete
    
    **Stream Mapping Examples:**
    - `"0:v:0"` - First video stream from first input (input index 0)
    - `"1:a:0"` - First audio stream from second input (input index 1)
    - `"0:v"` - All video streams from first input
    - `"0:a"` - All audio streams from first input
    - `"[labelname]"` - Output from a filter with specific label
    
    **Filter Examples:**
    
    Simple video filter (using -vf):
    ```json
    {
      "filters": [
        {
          "filter": "scale",
          "arguments": ["1920", "1080"],
          "type": "video"
        }
      ],
      "use_simple_video_filter": true
    }
    ```
    
    Complex filter graph:
    ```json
    {
      "filters": [
        {
          "filter": "scale",
          "arguments": ["1920", "1080"],
          "input_labels": ["0:v"],
          "output_label": "scaled"
        },
        {
          "filter": "overlay",
          "arguments": ["10", "10"],
          "input_labels": ["scaled", "1:v"],
          "output_label": "output"
        }
      ]
    }
    ```
    
    **Example Request:**
    ```json
    {
      "id": "video-conversion-example",
      "inputs": [
        {
          "file_url": "https://example.com/input.mov",
          "options": [
            {"option": "-ss", "argument": "10"},
            {"option": "-t", "argument": "30"}
          ]
        }
      ],
      "outputs": [
        {
          "options": [
            {"option": "-c:v", "argument": "libx264"},
            {"option": "-crf", "argument": "23"},
            {"option": "-c:a", "argument": "aac"}
          ]
        }
      ],
      "metadata": {
        "thumbnail": true,
        "filesize": true,
        "duration": true,
        "bitrate": true,
        "encoder": true
      }
    }
    ```
    
    **Processing Notes:**
    - The system provides estimated processing times based on input complexity
    - Jobs are processed asynchronously with real-time status updates
    - All temporary files are automatically cleaned up after processing
    - Output files are stored in S3 and accessible via returned URLs
    """,
    operation_id="compose_ffmpeg_command"
)
async def compose_ffmpeg_command(
    request: FFmpegComposeRequest,
    api_key: str = Depends(get_api_key)
) -> JobResponse:
    """
    Compose and execute FFmpeg commands from JSON configuration.
    
    This endpoint processes complex FFmpeg operations including:
    - Multiple input files with individual options
    - Complex filter graphs and stream mapping
    - Multiple output configurations
    - Metadata extraction and thumbnail generation
    - Background processing with status tracking
    """
    try:
        logger.info(f"Received FFmpeg compose request: {request.id}")
        
        # Validate the request using the composer service
        validation_errors = ffmpeg_composer.validate_request(request)
        if validation_errors:
            logger.warning(f"Request validation failed: {validation_errors}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Request validation failed",
                    "validation_errors": validation_errors,
                    "message": "Please check your request parameters and try again"
                }
            )
        
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Compose FFmpeg command for logging/debugging
        try:
            ffmpeg_command = ffmpeg_composer.compose_command(request)
            logger.info(f"Composed FFmpeg command for job {job_id}: {ffmpeg_command}")
        except Exception as e:
            logger.error(f"Failed to compose FFmpeg command: {str(e)}")
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Failed to compose FFmpeg command",
                    "message": str(e)
                }
            )
        
        # Prepare job parameters
        job_params = request.model_dump()
        
        # Create job
        await job_queue.add_job(
            job_id=job_id,
            job_type=JobType.FFMPEG_COMPOSE,
            process_func=ffmpeg_composer.process_ffmpeg_compose,
            data=job_params
        )
        
        logger.info(f"FFmpeg compose request {request.id} queued with job ID {job_id}")
        
        return JobResponse(job_id=job_id)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in FFmpeg compose endpoint: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Internal server error",
                "message": "An unexpected error occurred while processing your request"
            }
        )

@router.get(
    "/compose/{job_id}",
    response_model=JobStatusResponse,
    summary="Get FFmpeg Compose Job Status",
    description="""
    Get the status of an FFmpeg compose job.
    
    Returns detailed information about the job including:
    - Current status (pending, processing, completed, failed)
    - Results (when completed) with output file URLs and metadata
    - Error information (when failed)
    - Processing progress and timing information
    
    **Job Status Values:**
    - **pending**: Job is queued and waiting to start
    - **processing**: Job is currently executing
    - **completed**: Job finished successfully with results
    - **failed**: Job failed with error information
    
    **Example Response (Completed):**
    ```json
    {
      "job_id": "abc123-def456",
      "status": "completed",
      "result": {
        "outputs": [
          {
            "file_url": "https://s3.amazonaws.com/bucket/ffmpeg_compose/abc123/output_0.mp4",
            "thumbnail_url": "https://s3.amazonaws.com/bucket/ffmpeg_compose/abc123/thumbnail_0.jpg",
            "filesize": 15678234,
            "duration": 30.5,
            "bitrate": 1500000,
            "encoder": "libx264"
          }
        ],
        "command": "ffmpeg -y -i input.mov -c:v libx264 -crf 23 output.mp4",
        "processing_time": 45.2
      }
    }
    ```
    """,
    operation_id="get_ffmpeg_compose_status"
)
async def get_ffmpeg_compose_status(
    job_id: str,
    api_key: str = Depends(get_api_key)
) -> JobStatusResponse:
    """Get the status and results of an FFmpeg compose job."""
    try:
        # Get job status from job queue
        job_status = await job_queue.get_job_info(job_id)
        
        if not job_status:
            raise HTTPException(
                status_code=404,
                detail={
                    "error": "Job not found",
                    "message": f"No job found with ID {job_id}"
                }
            )
        
        return job_status
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status for {job_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to get job status",
                "message": str(e)
            }
        )

@router.get(
    "/compose/examples",
    response_model=Dict[str, Any],
    summary="Get FFmpeg Compose Examples",
    description="""
    Get example configurations for common FFmpeg compose operations.
    
    Returns a collection of example requests for:
    - Video conversion and transcoding
    - Audio processing and mixing
    - Filter applications (scaling, overlays, effects)
    - Stream mapping scenarios
    - Complex multi-input operations
    - Metadata extraction
    
    These examples demonstrate the full capabilities of the FFmpeg compose endpoint
    and can be used as templates for your own requests.
    """,
    operation_id="get_ffmpeg_compose_examples"
)
async def get_ffmpeg_compose_examples() -> Dict[str, Any]:
    """Get example configurations for FFmpeg compose operations."""
    return {
        "examples": {
            "video_conversion": {
                "description": "Convert video to different format with quality settings",
                "request": {
                    "id": "video-conversion-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/input.mov",
                            "options": []
                        }
                    ],
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:v", "argument": "libx264"},
                                {"option": "-crf", "argument": 23},
                                {"option": "-c:a", "argument": "aac"},
                                {"option": "-b:a", "argument": "128k"}
                            ]
                        }
                    ],
                    "metadata": {
                        "filesize": True,
                        "duration": True,
                        "bitrate": True,
                        "encoder": True,
                        "thumbnail": True
                    }
                }
            },
            "video_scaling": {
                "description": "Scale video to specific resolution using simple filter",
                "request": {
                    "id": "video-scaling-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/input.mp4",
                            "options": []
                        }
                    ],
                    "filters": [
                        {
                            "filter": "scale",
                            "arguments": ["1920", "1080"],
                            "type": "video"
                        }
                    ],
                    "use_simple_video_filter": True,
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:v", "argument": "libx264"},
                                {"option": "-c:a", "argument": "copy"}
                            ]
                        }
                    ]
                }
            },
            "audio_mixing": {
                "description": "Mix multiple audio files with volume adjustment",
                "request": {
                    "id": "audio-mixing-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/audio1.mp3",
                            "options": []
                        },
                        {
                            "file_url": "https://example.com/audio2.mp3",
                            "options": []
                        }
                    ],
                    "filters": [
                        {
                            "filter": "volume",
                            "arguments": ["0.8"],
                            "input_labels": ["0:a"],
                            "output_label": "a1"
                        },
                        {
                            "filter": "volume", 
                            "arguments": ["0.6"],
                            "input_labels": ["1:a"],
                            "output_label": "a2"
                        },
                        {
                            "filter": "amix",
                            "arguments": ["inputs=2"],
                            "input_labels": ["a1", "a2"],
                            "output_label": "mixed"
                        }
                    ],
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:a", "argument": "mp3"},
                                {"option": "-b:a", "argument": "192k"}
                            ],
                            "stream_mappings": ["[mixed]"]
                        }
                    ]
                }
            },
            "video_overlay": {
                "description": "Overlay one video on top of another with positioning",
                "request": {
                    "id": "video-overlay-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/background.mp4",
                            "options": []
                        },
                        {
                            "file_url": "https://example.com/overlay.mp4",
                            "options": []
                        }
                    ],
                    "filters": [
                        {
                            "filter": "scale",
                            "arguments": ["320", "240"],
                            "input_labels": ["1:v"],
                            "output_label": "overlay_scaled"
                        },
                        {
                            "filter": "overlay",
                            "arguments": ["10", "10"],
                            "input_labels": ["0:v", "overlay_scaled"],
                            "output_label": "output"
                        }
                    ],
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:v", "argument": "libx264"},
                                {"option": "-c:a", "argument": "copy"}
                            ],
                            "stream_mappings": ["[output]", "0:a"]
                        }
                    ]
                }
            },
            "stream_mapping": {
                "description": "Map specific streams from multiple inputs",
                "request": {
                    "id": "stream-mapping-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/video.mp4",
                            "options": []
                        },
                        {
                            "file_url": "https://example.com/audio.mp3",
                            "options": []
                        }
                    ],
                    "stream_mappings": ["0:v:0", "1:a:0"],
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:v", "argument": "copy"},
                                {"option": "-c:a", "argument": "aac"}
                            ]
                        }
                    ]
                }
            },
            "extract_segment": {
                "description": "Extract a segment from video with timing options",
                "request": {
                    "id": "extract-segment-example",
                    "inputs": [
                        {
                            "file_url": "https://example.com/long_video.mp4",
                            "options": [
                                {"option": "-ss", "argument": "00:01:30"},
                                {"option": "-t", "argument": "00:00:30"}
                            ]
                        }
                    ],
                    "outputs": [
                        {
                            "options": [
                                {"option": "-c:v", "argument": "libx264"},
                                {"option": "-c:a", "argument": "aac"}
                            ]
                        }
                    ],
                    "metadata": {
                        "duration": True,
                        "filesize": True,
                        "thumbnail": True
                    }
                }
            }
        },
        "tips": {
            "input_options": "Input-specific options (like -ss, -t) should be placed in the input's options array",
            "stream_mapping": "Use 0:v:0 for first video stream, 1:a:0 for first audio from second input, etc.",
            "complex_filters": "For complex operations, use filter graphs with input_labels and output_label",
            "simple_filters": "For single-input operations, use simple filters with use_simple_video_filter or use_simple_audio_filter",
            "global_options": "Use global_options for settings that apply to the entire FFmpeg command (like -y for overwrite)"
        }
    }