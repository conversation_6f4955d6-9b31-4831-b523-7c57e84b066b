"""
Utilities for media file operations.
"""
import os
import logging
import tempfile
import subprocess
import requests
from urllib.parse import urlparse
from typing import Tuple, Optional

from app.services.s3 import s3_service
from app.utils.youtube import is_youtube_url

# Configure logging
logger = logging.getLogger(__name__)

# Define supported file extensions
SUPPORTED_AUDIO_FORMATS = ['.mp3', '.wav', '.m4a', '.flac', '.aac', '.ogg']
SUPPORTED_VIDEO_FORMATS = ['.mp4', '.webm', '.mov', '.avi', '.mkv']
SUPPORTED_FORMATS = SUPPORTED_AUDIO_FORMATS + SUPPORTED_VIDEO_FORMATS

async def download_media_file(media_url: str, temp_dir: str = "temp") -> Tuple[str, str]:
    """
    Download media file from URL.
    
    Args:
        media_url: URL of the media file
        temp_dir: Directory to save temporary files
        
    Returns:
        Tuple of (local file path, file extension)
        
    Raises:
        RuntimeError: If download fails
    """
    # Parse URL to get hostname and path
    parsed_url = urlparse(media_url)
    hostname = parsed_url.netloc
    path = parsed_url.path
    
    # Get file extension
    _, file_extension = os.path.splitext(path)
    file_extension = file_extension.lower()
    
    # If no extension or not recognized, use defaults
    if not file_extension:
        file_extension = ".mp4"  # Default to mp4 if no extension
    
    # Create temporary file
    os.makedirs(temp_dir, exist_ok=True)
    temp_file = tempfile.NamedTemporaryFile(suffix=file_extension, delete=False, dir=temp_dir)
    temp_file.close()
    local_file_path = temp_file.name
    
    logger.info(f"Downloading media from {media_url} to {local_file_path}")
    
    # Check if URL is from our S3-compatible storage
    bucket_name = os.environ.get("S3_BUCKET_NAME", "")
    s3_endpoint = os.environ.get("S3_ENDPOINT_URL", "")
    
    # Check if URL is from our S3 storage (could be AWS S3, DigitalOcean, etc.)
    is_from_our_s3 = False
    if bucket_name or s3_endpoint:
        # Check if hostname matches our bucket name or S3 endpoint
        is_from_our_s3 = (
            (bucket_name and bucket_name in hostname) or 
            (s3_endpoint and urlparse(s3_endpoint).netloc in hostname) or
            # Also check for MinIO-style URLs that might have the bucket name in the subdomain
            'minio' in hostname.lower()
        )
    
    try:
        if is_from_our_s3:
            # Extract object key from path
            object_path = path.lstrip('/')
            
            # For DigitalOcean Spaces URLs, extract bucket from hostname and use full path as object key
            # URL format: https://bucketname.nyc3.digitaloceanspaces.com/folder/file.ext
            if 'digitaloceanspaces.com' in hostname:
                # Extract bucket from hostname (first part before first dot)
                url_bucket_name = hostname.split('.')[0]
                object_key = object_path  # Use full path as object key (preserves folder structure)
                logger.info(f"Detected DigitalOcean Spaces URL: bucket={url_bucket_name}, object={object_key}")
            else:
                # For other S3-compatible services, split path to get bucket and object
                path_parts = object_path.split('/', 1)
                if len(path_parts) == 2:
                    url_bucket_name, object_key = path_parts
                    logger.info(f"Detected S3 URL with bucket in path: bucket={url_bucket_name}, object={object_key}")
                else:
                    # Fallback: use default bucket and full path as object key
                    url_bucket_name = None
                    object_key = object_path
                    logger.info(f"Using default bucket with object key: {object_key}")
                
            try:
                # Use the bucket name from the URL, not from environment variable
                local_file_path = await s3_service.download_file(object_key, local_file_path, bucket_name=url_bucket_name)
            except Exception as s3_error:
                if "403" in str(s3_error) or "Forbidden" in str(s3_error) or "404" in str(s3_error) or "Not Found" in str(s3_error):
                    logger.warning(f"S3 download failed with {s3_error}, trying HTTP download as fallback")
                    # Fallback to HTTP download if we get permission denied or file not found
                    # Check if this is a MinIO server and disable SSL verification if needed
                    verify_ssl = True
                    if 'minio' in hostname.lower():
                        verify_ssl = False
                        logger.info(f"Detected MinIO server, disabling SSL verification for {media_url}")
                    
                    response = requests.get(media_url, timeout=30, verify=verify_ssl)
                    response.raise_for_status()
                    
                    with open(local_file_path, 'wb') as f:
                        f.write(response.content)
                    logger.info(f"Successfully downloaded media via HTTP fallback: {local_file_path}")
                else:
                    raise
            else:
                # Fallback to using environment bucket and full path as object key
                object_key = object_path
                logger.info(f"Detected S3 URL, downloading object: {object_key}")
                local_file_path = await s3_service.download_file(object_key, local_file_path)
            logger.info(f"Successfully downloaded media from S3: {local_file_path}")
        elif is_youtube_url(media_url):
            # Use yt-dlp for YouTube URLs
            logger.info(f"Detected YouTube URL, using yt-dlp: {media_url}")
            download_with_ytdlp(media_url, local_file_path)
        else:
            # Use direct HTTP download for external URLs (like Cloudinary)
            logger.info(f"Detected external URL, using direct HTTP download: {media_url}")
            
            # Check if this is a MinIO server and disable SSL verification if needed
            verify_ssl = True
            if 'minio' in hostname.lower():
                verify_ssl = False
                logger.info(f"Detected MinIO server, disabling SSL verification for {media_url}")
            
            response = requests.get(media_url, timeout=30, verify=verify_ssl)
            response.raise_for_status()
            
            with open(local_file_path, 'wb') as f:
                f.write(response.content)
            
            logger.info(f"Successfully downloaded media via HTTP: {local_file_path}")
            
        logger.info(f"Media downloaded successfully to {local_file_path}")
        return local_file_path, file_extension
    except Exception as e:
        # Clean up temporary file if download failed
        if os.path.exists(local_file_path):
            os.unlink(local_file_path)
        logger.error(f"Failed to download media from {media_url}: {e}")
        raise RuntimeError(f"Failed to download media: {e}")

def download_with_ytdlp(url: str, output_path: str):
    """
    Download media using yt-dlp.
    
    Args:
        url: URL to download from
        output_path: Path to save the downloaded file
        
    Raises:
        RuntimeError: If download fails
    """
    try:
        # For videos, use best video with audio
        format_option = "bestaudio/best"
        if output_path.lower().endswith(tuple(SUPPORTED_VIDEO_FORMATS)):
            format_option = "bestvideo+bestaudio/best"
            
        # Run yt-dlp with appropriate options
        cmd = [
            "yt-dlp",
            "-o", output_path,
            "--no-playlist",
            "--quiet",
            "--format", format_option,
            url
        ]
        
        logger.info(f"Running yt-dlp command: {' '.join(cmd)}")
        result = subprocess.run(cmd, check=True, capture_output=True)
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            logger.info(f"Download successful, file size: {file_size} bytes")
        else:
            logger.error("Download failed: Output file does not exist")
            raise RuntimeError("Download completed but file doesn't exist")
    except subprocess.CalledProcessError as e:
        logger.error(f"yt-dlp failed: {e.stderr.decode() if e.stderr else str(e)}")
        raise RuntimeError(f"yt-dlp failed: {e.stderr.decode() if e.stderr else str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during download: {e}")
        raise 

async def download_subtitle_file(subtitle_url: str, temp_dir: str = "temp") -> str:
    """
    Download a subtitle file from a URL.
    
    Args:
        subtitle_url: URL of the subtitle file to download
        temp_dir: Directory to save temporary files
        
    Returns:
        Local path to the downloaded subtitle file
        
    Raises:
        RuntimeError: If download fails or format is unsupported
    """
    # Parse URL to get hostname and path
    parsed_url = urlparse(subtitle_url)
    hostname = parsed_url.netloc
    path = parsed_url.path
    file_extension = os.path.splitext(path)[1].lower()
    
    # Validate subtitle format
    if file_extension not in [".srt", ".ass", ".vtt"]:
        raise RuntimeError(f"Unsupported subtitle format: {file_extension}")
    
    # Create temporary file
    os.makedirs(temp_dir, exist_ok=True)
    temp_file = tempfile.NamedTemporaryFile(
        delete=False, 
        suffix=file_extension,
        dir=temp_dir
    )
    temp_file_path = temp_file.name
    temp_file.close()
    
    logger.info(f"Downloading subtitle file from URL: {subtitle_url}")
    
    # Check if URL is from our S3-compatible storage
    bucket_name = os.environ.get("S3_BUCKET_NAME", "")
    s3_endpoint = os.environ.get("S3_ENDPOINT_URL", "")
    
    # Check if URL is from our S3 storage
    is_from_our_s3 = False
    if bucket_name or s3_endpoint:
        is_from_our_s3 = (
            (bucket_name and bucket_name in hostname) or 
            (s3_endpoint and urlparse(s3_endpoint).netloc in hostname) or
            # Also check for MinIO-style URLs that might have the bucket name in the subdomain
            'minio' in hostname.lower()
        )
    
    try:
        if is_from_our_s3:
            # Extract object key from path - this is the full path including folder prefixes
            object_key = path.lstrip('/')
            logger.info(f"Detected S3 URL, downloading subtitle: {object_key}")
            
            # Use environment bucket name and full path as object key (including folder prefixes like transcriptions/)
            temp_file_path = await s3_service.download_file(object_key, temp_file_path)
            logger.info(f"Successfully downloaded subtitle from S3: {temp_file_path}")
        else:
            # Use regular HTTP download for non-S3 URLs
            import requests
            response = requests.get(subtitle_url, timeout=30)
            response.raise_for_status()
            
            with open(temp_file_path, 'wb') as f:
                f.write(response.content)
        
        logger.info(f"Subtitle file downloaded successfully to {temp_file_path}")
        return temp_file_path
    except Exception as e:
        # Clean up temporary file if download failed
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)
        logger.error(f"Failed to download subtitle file from {subtitle_url}: {e}")
        raise RuntimeError(f"Failed to download subtitle file: {str(e)}")


async def get_media_info(file_path: str) -> dict:
    """
    Get media information using FFprobe.
    
    Args:
        file_path: Path to the media file
        
    Returns:
        Dictionary containing media information
        
    Raises:
        RuntimeError: If FFprobe fails or file doesn't exist
    """
    if not os.path.exists(file_path):
        raise RuntimeError(f"File does not exist: {file_path}")
    
    try:
        # Use FFprobe to get media information
        cmd = [
            "ffprobe",
            "-v", "quiet",
            "-print_format", "json",
            "-show_format",
            "-show_streams",
            file_path
        ]
        
        logger.info(f"Getting media info for: {file_path}")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        
        import json
        media_info = json.loads(result.stdout)
        
        # Extract useful information
        info = {}
        
        # Format information
        if "format" in media_info:
            format_info = media_info["format"]
            info.update({
                "duration": float(format_info.get("duration", 0)),
                "bit_rate": int(format_info.get("bit_rate", 0)),
                "size": int(format_info.get("size", 0)),
                "format_name": format_info.get("format_name", ""),
                "format_long_name": format_info.get("format_long_name", "")
            })
        
        # Stream information
        if "streams" in media_info:
            streams = media_info["streams"]
            video_streams = [s for s in streams if s.get("codec_type") == "video"]
            audio_streams = [s for s in streams if s.get("codec_type") == "audio"]
            
            if video_streams:
                video_stream = video_streams[0]  # Use first video stream
                info.update({
                    "width": int(video_stream.get("width", 0)),
                    "height": int(video_stream.get("height", 0)),
                    "codec_name": video_stream.get("codec_name", ""),
                    "codec_long_name": video_stream.get("codec_long_name", ""),
                    "frame_rate": video_stream.get("r_frame_rate", ""),
                    "pixel_format": video_stream.get("pix_fmt", "")
                })
            
            if audio_streams:
                audio_stream = audio_streams[0]  # Use first audio stream
                info.update({
                    "audio_codec": audio_stream.get("codec_name", ""),
                    "audio_codec_long_name": audio_stream.get("codec_long_name", ""),
                    "sample_rate": int(audio_stream.get("sample_rate", 0)),
                    "channels": int(audio_stream.get("channels", 0)),
                    "channel_layout": audio_stream.get("channel_layout", "")
                })
        
        logger.info(f"Successfully extracted media info: duration={info.get('duration', 0)}s")
        return info
        
    except subprocess.CalledProcessError as e:
        error_msg = e.stderr.decode() if e.stderr else str(e)
        logger.error(f"FFprobe failed for {file_path}: {error_msg}")
        raise RuntimeError(f"Failed to get media info: {error_msg}")
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse FFprobe output: {e}")
        raise RuntimeError(f"Failed to parse media info: {e}")
    except Exception as e:
        logger.error(f"Unexpected error getting media info: {e}")
        raise RuntimeError(f"Unexpected error getting media info: {e}") 