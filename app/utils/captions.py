"""
Utility functions for creating caption files (SRT/ASS) from text with various styles.

This module provides functions for:
1. Creating SRT and ASS subtitle files from text
2. Formatting timestamps for different subtitle formats
3. Styling captions with different visual effects (highlight, karaoke, word-by-word, underline)
"""
import os
import uuid
import logging
import subprocess
from typing import Dict, List, Optional

# Configure logging
logger = logging.getLogger(__name__)

def create_timestamps_from_text(text: str, duration: float) -> List[Dict]:
    """
    Helper function to create artificial word timestamps from text.
    
    Args:
        text: The caption text
        duration: Duration of the video in seconds
        
    Returns:
        List of word timestamps
    """
    words = text.split()
    if not words:
        return []
    
    seconds_per_word = duration / len(words)
    return [
        {
            "word": word,
            "start": i * seconds_per_word,
            "end": (i + 1) * seconds_per_word
        }
        for i, word in enumerate(words)
    ]

def format_srt_timestamp(seconds: float) -> str:
    """
    Format seconds as SRT timestamp (HH:MM:SS,mmm).
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Formatted timestamp
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = seconds % 60
    milliseconds = int((seconds - int(seconds)) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"

def format_ass_timestamp(seconds: float) -> str:
    """
    Format seconds as ASS timestamp (H:MM:SS.cc).
    
    Args:
        seconds: Time in seconds
        
    Returns:
        Formatted timestamp
    """
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    centiseconds = int((seconds - int(seconds)) * 100)
    
    return f"{hours}:{minutes:02d}:{secs:02d}.{centiseconds:02d}"

def convert_color_to_ass_with_alpha(color: str, opacity: float = 1.0) -> str:
    """
    Convert a color string to ASS subtitle format with alpha channel.
    
    Args:
        color: Color string in hex (#RRGGBB), named format, or rgb()
        opacity: Opacity value from 0.0 (transparent) to 1.0 (opaque)
        
    Returns:
        Color in ASS format with alpha (&HAABBGGRR&)
    """
    # Handle hex colors
    if color.startswith('#'):
        # Remove # and ensure 6 characters
        color = color.lstrip('#')
        if len(color) == 3:  # Shorthand #RGB
            color = ''.join([c*2 for c in color])
        
        try:
            r = int(color[0:2], 16)
            g = int(color[2:4], 16)
            b = int(color[4:6], 16)
        except ValueError:
            logger.warning(f"Invalid hex color format: {color}")
            r, g, b = 255, 255, 255  # Default to white
    
    # Handle named colors
    elif color.lower() in {
        "white", "black", "red", "green", "blue", 
        "yellow", "cyan", "magenta", "gray", "purple"
    }:
        color_map = {
            "white": (255, 255, 255),
            "black": (0, 0, 0),
            "red": (255, 0, 0),
            "green": (0, 255, 0),
            "blue": (0, 0, 255),
            "yellow": (255, 255, 0),
            "cyan": (0, 255, 255),
            "magenta": (255, 0, 255),
            "gray": (128, 128, 128),
            "purple": (128, 0, 128)
        }
        r, g, b = color_map[color.lower()]
    
    # Handle rgb() format
    elif color.startswith('rgb(') and color.endswith(')'):
        try:
            rgb = color[4:-1].split(',')
            if len(rgb) == 3:
                r = int(rgb[0].strip())
                g = int(rgb[1].strip())
                b = int(rgb[2].strip())
            else:
                r, g, b = 255, 255, 255  # Default to white
        except ValueError:
            logger.warning(f"Invalid rgb() color format: {color}")
            r, g, b = 255, 255, 255  # Default to white
    
    # Default for unrecognized formats
    else:
        logger.warning(f"Unrecognized color format: {color}, defaulting to white")
        r, g, b = 255, 255, 255
    
    # Convert opacity to ASS alpha (inverted)
    alpha = int((1 - opacity) * 255)
    
    # Format in ASS: &HAABBGGRR& (AA=alpha, BB=blue, GG=green, RR=red)
    return f"&H{alpha:02X}{b:02X}{g:02X}{r:02X}&"

def prepare_subtitle_styling(caption_properties: Optional[Dict] = None) -> Dict:
    """
    Prepare subtitle styling options for FFmpeg based on caption properties.
    
    Args:
        caption_properties: Dictionary of caption styling properties
        
    Returns:
        Dictionary of FFmpeg subtitle styling options
    """

    print(f"Preparing subtitle styling with properties: {caption_properties}")
    if not caption_properties:
        return {}
    
    # Define default options with improved positioning
    options = {
        'FontName': 'Arial',
        'FontSize': 42,  # Reduced from 48 for better readability
        'PrimaryColour': '&HFFFFFF&',  # White
        'OutlineColour': '&H000000&',  # Black
        'BackColour': '&H000000&',    # Black
        'SecondaryColour': '&HFFFF00&',  # Yellow for highlighted words
        'Bold': 0,
        'Italic': 0,
        'Underline': 0,
        'StrikeOut': 0,
        'Alignment': 2,  # Center-bottom aligned
        'MarginV': 180,  # Increased from 20 to avoid video player controls
        'MarginL': 80,   # Increased from 20 for better left margin
        'MarginR': 80,   # Increased from 20 for better right margin
        'Outline': 2,
        'Shadow': 1,
        'Spacing': 0,
        'Angle': 0,
        'BorderStyle': 1  # Default to outline + drop shadow
    }
    
    # Determine caption style
    style_value = caption_properties.get("style")
    style = style_value.lower() if style_value else "highlight"
    
    # Map properties to ASS style options
    if caption_properties.get("font_family"):
        requested_font = caption_properties["font_family"]
        options['FontName'] = requested_font
        logger.info(f"Font family requested: '{requested_font}'")
        
        # Check if the requested font exists on the system
        try:
            # Run fc-list to check if the font is available
            font_check_cmd = ["fc-list", requested_font]
            font_check_result = subprocess.run(
                font_check_cmd, 
                capture_output=True,
                text=True
            )
            
            if font_check_result.stdout.strip():
                logger.info(f"Font '{requested_font}' is available on the system")
            else:
                logger.warning(f"Font '{requested_font}' may not be available on the system, falling back to Arial")
                logger.info(f"Available fonts similar to '{requested_font}': {subprocess.run(['fc-list', ':', 'family', '|', 'grep', '-i', requested_font], capture_output=True, text=True).stdout.strip()}")
        except Exception as e:
            logger.warning(f"Error checking font availability: {e}")
    
    if caption_properties.get("font_size"):
        # Scale font size to prevent screen overflow
        requested_size = caption_properties["font_size"]
        # Cap maximum font size to prevent overflow
        max_font_size = 72  # Reasonable maximum for 1920x1080
        if requested_size > max_font_size:
            logger.warning(f"Font size {requested_size} too large, capping at {max_font_size}")
            options['FontSize'] = max_font_size
        else:
            options['FontSize'] = requested_size
    
    # Handle colors
    if caption_properties.get("line_color"):
        options['PrimaryColour'] = convert_color_to_ass_with_alpha(
            caption_properties["line_color"],
            caption_properties.get("line_opacity", 1.0)
        )
        logger.info(f"Setting line color to {options['PrimaryColour']} (AABBGGRR format)")
    
    if caption_properties.get("outline_color"):
        options['OutlineColour'] = convert_color_to_ass_with_alpha(
            caption_properties["outline_color"],
             1.0
        )
        logger.info(f"Setting outline color to {options['OutlineColour']} (AABBGGRR format)")
    
    # Word color is specially important for highlight style
    if caption_properties.get("word_color"):
        options['SecondaryColour'] = convert_color_to_ass_with_alpha(
            caption_properties["word_color"],
             1.0
        )
        logger.info(f"Setting word color to {options['SecondaryColour']} (AABBGGRR format)")
    
    # Handle background properties
    if caption_properties.get("background_color"):
        # Set the border style first
        options['BorderStyle'] = 4  # Opaque box
        
        options['BackColour'] = convert_color_to_ass_with_alpha(
            caption_properties["background_color"],
            caption_properties.get("background_opacity", 1.0)
        )
        logger.info(f"Setting background color to {options['BackColour']} (AABBGGRR format)")
        
        # When using background, disable outline but keep minimal shadow
        options['Outline'] = 0
        options['Shadow'] = 1
        
        # Handle padding through margins
        if caption_properties.get("background_padding") is not None:
            padding = caption_properties["background_padding"]
            options['MarginL'] = padding
            options['MarginR'] = padding
            options['MarginV'] = padding
    
    # Handle boolean properties
    for prop, option in [
        ("bold", "Bold"), 
        ("italic", "Italic"),
        ("underline", "Underline"),
        ("strikeout", "StrikeOut")
    ]:
        if prop in caption_properties and caption_properties[prop] is not None:
            options[option] = 1 if caption_properties[prop] else 0
    
    # Handle numeric properties
    for prop, option in [
        ("outline_width", "Outline"),
        ("shadow_offset", "Shadow"),
        ("spacing", "Spacing"),
        ("angle", "Angle")
    ]:
        if prop in caption_properties and caption_properties[prop] is not None:
            options[option] = caption_properties[prop]
    
    # Handle position and alignment (enhanced dahopevi-style)
    if caption_properties.get("x") is not None and caption_properties.get("y") is not None:
        # Custom X,Y positioning - use absolute positioning
        x_pos = caption_properties["x"]
        y_pos = caption_properties["y"]
        
        # For ASS subtitles with custom positioning, we'll use margins
        # Note: This is a simplified approach. Full custom positioning requires \pos() tags
        options['MarginL'] = x_pos
        options['MarginR'] = x_pos
        options['MarginV'] = y_pos
        
        # Use center alignment for custom positioning
        options['Alignment'] = 5  # Middle center
        logger.info(f"Using custom positioning: X={x_pos}, Y={y_pos}")
        
    elif caption_properties.get("position"):
        # Map predefined positions to alignment values with better margins
        position_map = {
            "bottom_left": 1, "bottom_center": 2, "bottom_right": 3,
            "middle_left": 4, "middle_center": 5, "middle_right": 6,
            "top_left": 7, "top_center": 8, "top_right": 9
        }
        if caption_properties["position"] in position_map:
            options['Alignment'] = position_map[caption_properties["position"]]
            
            # Adjust margins based on position for better spacing - FIXED for both top and bottom
            if "top" in caption_properties["position"]:
                options['MarginV'] = 150  # Increased space from top to avoid being cut off/hidden
            elif "bottom" in caption_properties["position"]:
                options['MarginV'] = 180  # Much more space from bottom to avoid player controls
            elif "middle" in caption_properties["position"]:
                options['MarginV'] = 60   # Centered with moderate margin
                
            if "left" in caption_properties["position"]:
                options['MarginL'] = 120  # More space from left
                options['MarginR'] = 40   # Less space from right
            elif "right" in caption_properties["position"]:
                options['MarginL'] = 40   # Less space from left
                options['MarginR'] = 120  # More space from right
            elif "center" in caption_properties["position"]:
                options['MarginL'] = 80   # Balanced margins
                options['MarginR'] = 80
                
            logger.info(f"Using predefined position: {caption_properties['position']} with margins L:{options['MarginL']} R:{options['MarginR']} V:{options['MarginV']}")
    
    # Independent alignment setting (can override position alignment)
    if caption_properties.get("alignment"):
        # Map alignment to appropriate values based on current vertical position
        current_alignment = options.get('Alignment', 2)
        if current_alignment in [1, 2, 3]:  # Bottom row
            alignment_map = {"left": 1, "center": 2, "right": 3}
        elif current_alignment in [4, 5, 6]:  # Middle row
            alignment_map = {"left": 4, "center": 5, "right": 6}
        else:  # Top row [7, 8, 9]
            alignment_map = {"left": 7, "center": 8, "right": 9}
        
        if caption_properties["alignment"] in alignment_map:
            options['Alignment'] = alignment_map[caption_properties["alignment"]]
            logger.info(f"Applied alignment: {caption_properties['alignment']}")
    
    # Handle gradient colors for modern effects
    if caption_properties.get("gradient_colors"):
        gradient_colors = caption_properties["gradient_colors"]
        if isinstance(gradient_colors, list) and len(gradient_colors) > 0:
            # Use first color as primary, second as secondary if available
            options['PrimaryColour'] = convert_color_to_ass_with_alpha(gradient_colors[0], 1.0)
            if len(gradient_colors) > 1:
                options['SecondaryColour'] = convert_color_to_ass_with_alpha(gradient_colors[1], 1.0)
            logger.info(f"Applied gradient colors: {gradient_colors}")
    
    # Handle glow effects
    if caption_properties.get("glow_effect"):
        glow_color = caption_properties.get("glow_color", "#00FFFF")
        glow_intensity = caption_properties.get("glow_intensity", 1.0)
        
        # Implement glow through enhanced outline
        options['OutlineColour'] = convert_color_to_ass_with_alpha(glow_color, glow_intensity)
        options['Outline'] = max(3, int(glow_intensity * 4))  # Increase outline width for glow
        logger.info(f"Applied glow effect: color={glow_color}, intensity={glow_intensity}")
    
    # Style-specific adjustments
    if style == "highlight":
        if not caption_properties.get("word_color"):
            options['SecondaryColour'] = "&HFFFF00&"  # Yellow for highlighted words
    elif style == "word_by_word":
        if not caption_properties.get("word_color"):
            options['PrimaryColour'] = "&HFFFF00&"  # Yellow for word-by-word
    elif style == "karaoke":
        # Karaoke typically uses secondary color for highlighting
        if not caption_properties.get("word_color"):
            options['SecondaryColour'] = "&HFFFF00&"  # Yellow for karaoke
    elif style in ["bounce", "viral_bounce"]:
        # Bounce styles often use bright colors
        if not caption_properties.get("line_color"):
            options['PrimaryColour'] = "&HFFFFFF&"  # Bright white
        if not caption_properties.get("word_color"):
            options['SecondaryColour'] = "&H00FFFF&"  # Cyan for bounce effect
    
    # Handle modern visual effects and animation settings
    animation_speed = caption_properties.get("animation_speed", 1.0) if caption_properties else 1.0
    bounce_intensity = caption_properties.get("bounce_intensity", 1.0) if caption_properties else 1.0
    typewriter_speed = caption_properties.get("typewriter_speed", 2.0) if caption_properties else 2.0
    
    # Store animation settings in options for use by style creators
    options['AnimationSpeed'] = animation_speed
    options['BounceIntensity'] = bounce_intensity
    options['TypewriterSpeed'] = typewriter_speed
    
    # AI-powered features flags
    if caption_properties:
        options['AutoEmoji'] = caption_properties.get("auto_emoji", False)
        options['AutoCapitalization'] = caption_properties.get("auto_capitalization", False)
        options['ConfidenceStyling'] = caption_properties.get("confidence_styling", False)
    
    # Log the final options for debugging
    logger.info(f"Final subtitle styling options with {len(options)} properties: {list(options.keys())}")
    logger.debug(f"Full subtitle styling options: {options}")
    
    return options



async def create_highlight_style_ass_from_timestamps(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    caption_properties: Optional[Dict] = None
) -> None:
    """
    Create an ASS subtitle file with highlight style from word timestamps.
    
    Args:
        word_timestamps: List of word timestamps
        duration: Duration in seconds
        max_words_per_line: Maximum words per line
        output_path: Output file path
        caption_properties: Dictionary of caption styling properties
    """
    try:
        # Prepare styling options
        style_options = prepare_subtitle_styling(caption_properties)
        
        # Get colors from style options
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')  # Default white
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')  # Default yellow
        outline_color = style_options.get('OutlineColour', '&H000000&')  # Default black
        back_color = style_options.get('BackColour', '&H000000&')  # Default black
        
        # Get font properties
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        
        # Get other style properties
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        # Add the last line if there are remaining words
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
"""
        # Add the style with proper colors
        ass_content += f"Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0\n\n"
        
        ass_content += """[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Add dialogue events for each line (base text)  
        event_counter = 0
        # Apply slight timing offset to improve synchronization (150ms earlier)
        timing_offset = -0.15
        
        for line_idx, line_words in enumerate(lines):
            # Create the base text for the line
            line_text = ' '.join([word_data.get("word", "").strip() for word_data in line_words])
            line_start = max(0, line_words[0].get("start", 0) + timing_offset)
            line_end = max(line_start + 0.3, line_words[-1].get("end", duration) + timing_offset)
            
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            # Base text line - layer 0 (bottom layer)
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{line_text}\n"
            
            # Add individual highlighting for each word in the line
            for word_idx, word_data in enumerate(line_words):
                word = word_data.get("word", "").strip()
                if not word:
                    continue
                    
                word_start = max(0, word_data.get("start", 0) + timing_offset)
                word_end = max(word_start + 0.1, word_data.get("end", 0) + timing_offset)
                
                word_start_str = format_ass_timestamp(word_start)
                word_end_str = format_ass_timestamp(word_end)
                
                # Create highlighted version of this word within the line
                highlighted_words = []
                for i, w_data in enumerate(line_words):
                    w = w_data.get("word", "").strip()
                    if i == word_idx:
                        # This is the current word - highlight it using the secondary color
                        # Use the color code without the &H and & parts for the \c tag
                        secondary_color_code = secondary_color.replace('&H', '').replace('&', '')
                        primary_color_code = primary_color.replace('&H', '').replace('&', '')
                        highlighted_words.append(f"{{\\c{secondary_color_code}}}{w}{{\\c{primary_color_code}}}")
                    else:
                        # Regular word
                        highlighted_words.append(w)
                
                highlighted_text = ' '.join(highlighted_words)
                
                # Highlighted word line - layer 1 (top layer)
                ass_content += f"Dialogue: 1,{word_start_str},{word_end_str},Default,,0,0,0,,{highlighted_text}\n"
                
                event_counter += 1
        
        # Write the ASS content to the output file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created highlight style ASS file with {event_counter} events from word timestamps")
    
    except Exception as e:
        logger.error(f"Error creating highlight style ASS from timestamps: {e}")
        raise

async def create_word_by_word_style_ass_from_timestamps(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str
) -> None:
    """
    Create an ASS subtitle file with word-by-word style from word timestamps.
    
    Args:
        word_timestamps: List of word timestamps
        duration: Duration in seconds
        max_words_per_line: Maximum words per line (not used in this style)
        output_path: Output file path
    """
    try:
        # Create ASS header
        ass_content = """[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,Arial,48,&HFFFF00&,&HFFFF00&,&H000000&,&H000000&,0,0,0,0,100,100,0,0,1,2,0,2,20,20,20,0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Add events for each word
        word_count = 0
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if not word:
                continue
                
            start_time = word_data.get("start", 0)
            end_time = word_data.get("end", 0)
            
            start_time_str = format_ass_timestamp(start_time)
            end_time_str = format_ass_timestamp(end_time)
            
            # Add the word as a separate dialogue event
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{word}\n"
            word_count += 1
        
        # Write the ASS content to the output file
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created word-by-word style ASS file with {word_count} words from word timestamps")
    
    except Exception as e:
        logger.error(f"Error creating word-by-word style ASS from timestamps: {e}")
        raise


async def create_enhanced_ass_from_timestamps(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    style: str,
    caption_properties: Optional[Dict] = None
) -> str:
    """
    Create enhanced ASS subtitle file with comprehensive dahopevi-style features.
    
    Args:
        word_timestamps: List of word timestamps
        duration: Duration in seconds
        max_words_per_line: Maximum words per line
        style: Caption style (classic, karaoke, highlight, underline, word_by_word, bounce, viral_bounce, typewriter, fade_in)
        caption_properties: Enhanced styling properties
        
    Returns:
        Path to generated ASS file
    """
    try:
        # Ensure max_words_per_line is not None
        if max_words_per_line is None:
            max_words_per_line = 10
            logger.warning("max_words_per_line was None, defaulting to 10")
        # Create a temp subtitle file
        output_path = os.path.join("temp", f"caption_{uuid.uuid4()}.ass")
        
        # Prepare styling options with all dahopevi features
        style_options = prepare_subtitle_styling(caption_properties)
        
        # Handle different caption styles
        if style == "karaoke":
            await create_karaoke_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options)
        elif style == "highlight":
            await create_highlight_style_ass_from_timestamps(word_timestamps, duration, max_words_per_line, output_path, caption_properties)
        elif style == "word_by_word":
            await create_word_by_word_style_ass_from_timestamps(word_timestamps, duration, max_words_per_line, output_path)
        elif style == "underline":
            await create_underline_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options)
        elif style == "bounce" or style == "viral_bounce":
            await create_bounce_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options, style)
        elif style == "typewriter":
            await create_typewriter_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options)
        elif style == "fade_in":
            await create_fade_in_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options)
        else:
            # Default to classic style - create simple ASS without special effects
            await create_classic_style_ass(word_timestamps, duration, max_words_per_line, output_path, style_options)
        
        logger.info(f"Created enhanced ASS file with style '{style}' at {output_path}")
        return output_path
    
    except Exception as e:
        logger.error(f"Error creating enhanced ASS file: {e}")
        raise

async def create_karaoke_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict
) -> None:
    """
    Create karaoke-style ASS subtitle file with word-by-word highlighting.
    """
    try:
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Create karaoke effects for each line
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            # Build karaoke text with timing
            karaoke_text = ""
            for word_data in line_words:
                word = word_data.get("word", "")
                word_duration = word_data.get("end", 0) - word_data.get("start", 0)
                centiseconds = int(word_duration * 100)
                karaoke_text += f"{{\\k{centiseconds}}}{word} "
            
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,Karaoke,{karaoke_text.strip()}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created karaoke style ASS file with {len(lines)} lines")
    
    except Exception as e:
        logger.error(f"Error creating karaoke style ASS: {e}")
        raise

async def create_underline_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict
) -> None:
    """
    Create underline-style ASS subtitle file.
    """
    try:
        # Similar to highlight but with underline effects
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = 1  # Force underline for this style
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Add underline effects for each line
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            # Base text line
            line_text = ' '.join([word_data.get("word", "").strip() for word_data in line_words])
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{line_text}\n"
            
            # Add individual word underlines
            for word_data in line_words:
                word = word_data.get("word", "").strip()
                if word:
                    word_start = word_data.get("start", 0)
                    word_end = word_data.get("end", 0)
                    word_start_str = format_ass_timestamp(word_start)
                    word_end_str = format_ass_timestamp(word_end)
                    
                    # Underlined word
                    ass_content += f"Dialogue: 1,{word_start_str},{word_end_str},Default,,0,0,0,,{{\\u1}}{word}{{\\u0}}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created underline style ASS file")
    
    except Exception as e:
        logger.error(f"Error creating underline style ASS: {e}")
        raise

async def create_bounce_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict,
    bounce_type: str = "bounce"
) -> None:
    """
    Create bounce-style ASS subtitle file with scaling effects - FIXED to show only bounce effect.
    """
    try:
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Determine bounce intensity
        bounce_scale = 140 if bounce_type == "viral_bounce" else 120
        
        # FIXED: Only show bounce effect, no base text layer
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            # Create bouncing line with all words
            line_text_parts = []
            for word_idx, word_data in enumerate(line_words):
                word = word_data.get("word", "").strip()
                if word:
                    word_start = word_data.get("start", 0)
                    word_end = word_data.get("end", 0)
                    word_duration = word_end - word_start
                    
                    # Calculate bounce timing relative to line start
                    bounce_start_ms = int((word_start - line_start) * 1000)
                    bounce_peak_ms = bounce_start_ms + int(word_duration * 300)  # 30% of word duration
                    bounce_end_ms = bounce_start_ms + int(word_duration * 600)   # 60% of word duration
                    
                    # Create bounce effect for this word
                    bounce_word = f"{{\\t({bounce_start_ms},{bounce_peak_ms},\\fscx{bounce_scale}\\fscy{bounce_scale})\\t({bounce_peak_ms},{bounce_end_ms},\\fscx100\\fscy100)}}{word}"
                    line_text_parts.append(bounce_word)
            
            # Combine all words in the line with bouncing effects
            line_text = ' '.join(line_text_parts)
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            # Single dialogue line with all bounce effects
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{line_text}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created {bounce_type} style ASS file with single-layer bounce effects")
    
    except Exception as e:
        logger.error(f"Error creating {bounce_type} style ASS: {e}")
        raise

async def create_typewriter_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict
) -> None:
    """
    Create typewriter-style ASS subtitle file with character-by-character reveal.
    """
    try:
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Create typewriter effects
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            # Build full text
            full_text = ' '.join([word_data.get("word", "").strip() for word_data in line_words])
            
            # Create character-by-character reveal
            char_count = len(full_text)
            if char_count > 0:
                line_duration = line_end - line_start
                chars_per_second = char_count / line_duration if line_duration > 0 else 10
                
                for i in range(1, char_count + 1):
                    char_time = line_start + (i / chars_per_second)
                    char_end_time = line_end
                    
                    if char_time <= line_end:
                        char_start_str = format_ass_timestamp(char_time)
                        char_end_str = format_ass_timestamp(char_end_time)
                        
                        revealed_text = full_text[:i]
                        ass_content += f"Dialogue: 0,{char_start_str},{char_end_str},Default,,0,0,0,,{revealed_text}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created typewriter style ASS file")
    
    except Exception as e:
        logger.error(f"Error creating typewriter style ASS: {e}")
        raise

async def create_classic_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict
) -> None:
    """
    Create classic/simple ASS subtitle file without special effects.
    """
    try:
        # Ensure max_words_per_line is not None
        if max_words_per_line is None:
            max_words_per_line = 10
            logger.warning("max_words_per_line was None, defaulting to 10")
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Add simple dialogue events for each line
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            line_text = ' '.join([word_data.get("word", "").strip() for word_data in line_words])
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{line_text}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created classic style ASS file with {len(lines)} lines")
    
    except Exception as e:
        logger.error(f"Error creating classic style ASS: {e}")
        raise

async def create_fade_in_style_ass(
    word_timestamps: List[Dict],
    duration: float,
    max_words_per_line: int,
    output_path: str,
    style_options: Dict
) -> None:
    """
    Create fade-in style ASS subtitle file with gradual opacity animation.
    """
    try:
        # Extract style properties
        primary_color = style_options.get('PrimaryColour', '&HFFFFFF&')
        secondary_color = style_options.get('SecondaryColour', '&HFFFF00&')
        outline_color = style_options.get('OutlineColour', '&H000000&')
        back_color = style_options.get('BackColour', '&H000000&')
        font_name = style_options.get('FontName', 'Arial')
        font_size = style_options.get('FontSize', 48)
        bold = style_options.get('Bold', 0)
        italic = style_options.get('Italic', 0)
        underline = style_options.get('Underline', 0)
        strikeout = style_options.get('StrikeOut', 0)
        border_style = style_options.get('BorderStyle', 1)
        outline = style_options.get('Outline', 2)
        shadow = style_options.get('Shadow', 0)
        alignment = style_options.get('Alignment', 2)
        margin_l = style_options.get('MarginL', 20)
        margin_r = style_options.get('MarginR', 20)
        margin_v = style_options.get('MarginV', 20)
        
        # Group words into lines
        lines = []
        current_line = []
        for word_data in word_timestamps:
            word = word_data.get("word", "").strip()
            if word:
                current_line.append(word_data)
                if len(current_line) >= max_words_per_line:
                    lines.append(current_line)
                    current_line = []
        
        if current_line:
            lines.append(current_line)
        
        # Create ASS header
        ass_content = f"""[Script Info]
ScriptType: v4.00+
PlayResX: 1920
PlayResY: 1080
ScaledBorderAndShadow: yes

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},{bold},{italic},{underline},{strikeout},100,100,0,0,{border_style},{outline},{shadow},{alignment},{margin_l},{margin_r},{margin_v},0

[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
"""
        
        # Add fade-in effects for each line
        for line_words in lines:
            line_start = line_words[0].get("start", 0)
            line_end = line_words[-1].get("end", duration)
            
            line_text = ' '.join([word_data.get("word", "").strip() for word_data in line_words])
            start_time_str = format_ass_timestamp(line_start)
            end_time_str = format_ass_timestamp(line_end)
            
            # Fade-in effect (from transparent to opaque)
            fade_duration = min(1000, (line_end - line_start) * 500)  # Fade for up to 1 second or half the line duration
            fade_text = f"{{\\fad({fade_duration},0)}}{line_text}"
            
            ass_content += f"Dialogue: 0,{start_time_str},{end_time_str},Default,,0,0,0,,{fade_text}\n"
        
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(ass_content)
        
        logger.info(f"Created fade-in style ASS file")
    
    except Exception as e:
        logger.error(f"Error creating fade-in style ASS: {e}")
        raise 