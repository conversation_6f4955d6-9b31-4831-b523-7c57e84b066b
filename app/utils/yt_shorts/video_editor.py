"""
Enhanced video editing utility for YouTube Shorts generation.

This module provides comprehensive video editing capabilities including:
- Advanced audio extraction with quality preservation
- Intelligent video segment cropping with fade transitions
- Multi-format support with optimized encoding
- Audio-video synchronization verification
- Quality enhancement and stabilization
"""
import os
import subprocess
import logging
from typing import Tuple, Optional, Dict, Any, List
import json

# Configure logging
logger = logging.getLogger(__name__)

# Test MoviePy import with detailed error logging
try:
    from moviepy.editor import VideoFileClip, AudioFileClip
    logger.info("MoviePy imported successfully in video_editor module")
except ImportError as e:
    logger.error(f"MoviePy import failed in video_editor module: {e}")
    raise
except Exception as e:
    logger.error(f"MoviePy import error in video_editor module: {e}")
    import traceback
    logger.error(f"MoviePy import traceback: {traceback.format_exc()}")
    raise

class EnhancedVideoEditor:
    """Enhanced video editing with professional features."""
    
    def __init__(self):
        """Initialize video editor."""
        self.supported_formats = {
            'video': ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv'],
            'audio': ['.mp3', '.wav', '.aac', '.m4a', '.ogg', '.flac']
        }
        
        # Quality presets
        self.quality_presets = {
            'low': {'crf': 28, 'preset': 'fast', 'bitrate': '1000k'},
            'medium': {'crf': 23, 'preset': 'medium', 'bitrate': '2500k'},
            'high': {'crf': 18, 'preset': 'slow', 'bitrate': '5000k'},
            'ultra': {'crf': 15, 'preset': 'slower', 'bitrate': '8000k'}
        }
        
        logger.info("Enhanced video editor initialized")
    
    def extract_audio_enhanced(self, video_path: str, output_path: str,
                             format: str = 'wav', quality: str = 'high') -> bool:
        """
        Extract audio from video with quality preservation.
        
        Args:
            video_path: Input video path
            output_path: Output audio path
            format: Output audio format
            quality: Audio quality (low, medium, high, ultra)
            
        Returns:
            True if successful
        """
        try:
            # Quality settings
            quality_settings = {
                'low': {'bitrate': '128k', 'sample_rate': 22050},
                'medium': {'bitrate': '192k', 'sample_rate': 44100},
                'high': {'bitrate': '320k', 'sample_rate': 48000},
                'ultra': {'bitrate': '640k', 'sample_rate': 96000}
            }
            
            settings = quality_settings.get(quality, quality_settings['high'])
            
            # Build FFmpeg command
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vn',  # No video
                '-acodec', 'pcm_s16le' if format == 'wav' else 'libmp3lame',
                '-ar', str(settings['sample_rate']),
                '-ac', '1',  # Mono for better VAD performance
            ]
            
            if format != 'wav':
                cmd.extend(['-b:a', settings['bitrate']])
            
            cmd.append(output_path)
            
            # Execute command
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Audio extraction failed: {result.stderr}")
                return False
            
            logger.info(f"Audio extracted successfully: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Audio extraction failed: {e}")
            return False
    
    def crop_video_segment_enhanced(self, video_path: str, output_path: str,
                                   start_time: float, end_time: float,
                                   quality: str = 'high',
                                   add_fade: bool = True) -> bool:
        """
        Crop video segment with enhanced features.
        
        Args:
            video_path: Input video path
            output_path: Output video path
            start_time: Start time in seconds
            end_time: End time in seconds
            quality: Video quality preset
            add_fade: Whether to add fade in/out effects
            
        Returns:
            True if successful
        """
        try:
            duration = end_time - start_time
            quality_preset = self.quality_presets.get(quality, self.quality_presets['high'])
            
            # Build FFmpeg command
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(start_time),
                '-t', str(duration),
                '-c:v', 'libx264',
                '-preset', quality_preset['preset'],
                '-crf', str(quality_preset['crf']),
                '-c:a', 'aac',
                '-b:a', '192k',
                '-movflags', '+faststart',  # Optimize for streaming
            ]
            
            # Add fade effects if requested
            if add_fade:
                fade_duration = min(0.5, duration / 4)  # Max 0.5s or 1/4 of duration
                video_filters = [
                    f'fade=t=in:st={start_time}:d={fade_duration}',
                    f'fade=t=out:st={end_time-fade_duration}:d={fade_duration}'
                ]
                
                audio_filters = [
                    f'afade=t=in:st={start_time}:d={fade_duration}',
                    f'afade=t=out:st={end_time-fade_duration}:d={fade_duration}'
                ]
                
                cmd.extend(['-vf', ','.join(video_filters)])
                cmd.extend(['-af', ','.join(audio_filters)])
            
            cmd.append(output_path)
            
            # Execute command
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Video cropping failed: {result.stderr}")
                return False
            
            logger.info(f"Video segment cropped successfully: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Video cropping failed: {e}")
            return False
    
    def get_video_info(self, video_path: str) -> Dict[str, Any]:
        """
        Get comprehensive video information.
        
        Args:
            video_path: Video file path
            
        Returns:
            Dictionary with video information
        """
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Video info extraction failed: {result.stderr}")
                return {}
            
            data = json.loads(result.stdout)
            
            # Extract relevant information
            video_info = {
                'duration': float(data['format']['duration']),
                'size': int(data['format']['size']),
                'bitrate': int(data['format'].get('bit_rate', 0)),
                'format_name': data['format']['format_name'],
                'streams': []
            }
            
            for stream in data['streams']:
                if stream['codec_type'] == 'video':
                    video_info['streams'].append({
                        'type': 'video',
                        'codec': stream['codec_name'],
                        'width': stream['width'],
                        'height': stream['height'],
                        'fps': eval(stream['r_frame_rate']),
                        'bitrate': stream.get('bit_rate', 'N/A')
                    })
                elif stream['codec_type'] == 'audio':
                    video_info['streams'].append({
                        'type': 'audio',
                        'codec': stream['codec_name'],
                        'sample_rate': stream['sample_rate'],
                        'channels': stream['channels'],
                        'bitrate': stream.get('bit_rate', 'N/A')
                    })
            
            return video_info
            
        except Exception as e:
            logger.error(f"Video info extraction failed: {e}")
            return {}
    
    def optimize_for_shorts(self, video_path: str, output_path: str,
                           target_resolution: Tuple[int, int] = (720, 1280),
                           quality: str = 'high') -> bool:
        """
        Optimize video specifically for YouTube Shorts.
        
        Args:
            video_path: Input video path
            output_path: Output video path
            target_resolution: Target resolution (width, height)
            quality: Quality preset
            
        Returns:
            True if successful
        """
        try:
            quality_preset = self.quality_presets.get(quality, self.quality_presets['high'])
            width, height = target_resolution
            
            # Build optimization command
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-vf', f'scale={width}:{height}:force_original_aspect_ratio=decrease,pad={width}:{height}:(ow-iw)/2:(oh-ih)/2:black',
                '-c:v', 'libx264',
                '-preset', quality_preset['preset'],
                '-crf', str(quality_preset['crf']),
                '-maxrate', quality_preset['bitrate'],
                '-bufsize', str(int(quality_preset['bitrate'].replace('k', '')) * 2) + 'k',
                '-c:a', 'aac',
                '-b:a', '192k',
                '-ar', '48000',
                '-ac', '2',
                '-movflags', '+faststart',
                '-profile:v', 'high',
                '-level', '4.2',
                '-pix_fmt', 'yuv420p',
                output_path
            ]
            
            # Execute command
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Video optimization failed: {result.stderr}")
                return False
            
            logger.info(f"Video optimized for shorts: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Video optimization failed: {e}")
            return False
    
    def enhance_audio_quality(self, audio_path: str, output_path: str,
                            enhance_speech: bool = True) -> bool:
        """
        Enhance audio quality for better clarity.
        
        Args:
            audio_path: Input audio path
            output_path: Output audio path
            enhance_speech: Whether to apply speech enhancement
            
        Returns:
            True if successful
        """
        try:
            # Build audio enhancement command
            cmd = [
                'ffmpeg', '-y',
                '-i', audio_path,
                '-af'
            ]
            
            # Audio filters for enhancement
            filters = []
            
            if enhance_speech:
                # Speech enhancement filters
                filters.extend([
                    'highpass=f=80',  # Remove low-frequency noise
                    'lowpass=f=8000',  # Remove high-frequency noise
                    'compand=0.02,0.2:6:-70,-60,-20',  # Dynamic range compression
                    'volume=1.5'  # Slight volume boost
                ])
            else:
                # General audio enhancement
                filters.extend([
                    'dynaudnorm=f=75:g=25:p=0.95',  # Dynamic audio normalization
                    'volume=1.2'  # Slight volume boost
                ])
            
            cmd.append(','.join(filters))
            cmd.extend([
                '-c:a', 'aac',
                '-b:a', '192k',
                '-ar', '48000',
                output_path
            ])
            
            # Execute command
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Audio enhancement failed: {result.stderr}")
                return False
            
            logger.info(f"Audio enhanced successfully: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Audio enhancement failed: {e}")
            return False
    
    def verify_av_sync(self, video_path: str) -> bool:
        """
        Verify audio-video synchronization.
        
        Args:
            video_path: Video file path
            
        Returns:
            True if sync is good
        """
        try:
            # Get video info
            video_info = self.get_video_info(video_path)
            
            if not video_info:
                return False
            
            # Check if video has both audio and video streams
            has_video = any(s['type'] == 'video' for s in video_info['streams'])
            has_audio = any(s['type'] == 'audio' for s in video_info['streams'])
            
            if not (has_video and has_audio):
                logger.warning("Video missing audio or video stream")
                return False
            
            # Additional sync checks could be added here
            # For now, we assume sync is good if both streams exist
            
            logger.info("Audio-video sync verification passed")
            return True
            
        except Exception as e:
            logger.error(f"AV sync verification failed: {e}")
            return False
    
    def create_preview_thumbnail(self, video_path: str, output_path: str,
                                timestamp: float = None) -> bool:
        """
        Create preview thumbnail from video.
        
        Args:
            video_path: Input video path
            output_path: Output thumbnail path
            timestamp: Time to capture thumbnail (default: middle of video)
            
        Returns:
            True if successful
        """
        try:
            if timestamp is None:
                # Get video duration and use middle frame
                video_info = self.get_video_info(video_path)
                timestamp = video_info.get('duration', 10) / 2
            
            cmd = [
                'ffmpeg', '-y',
                '-i', video_path,
                '-ss', str(timestamp),
                '-vframes', '1',
                '-q:v', '2',  # High quality
                '-vf', 'scale=720:1280:force_original_aspect_ratio=decrease,pad=720:1280:(ow-iw)/2:(oh-ih)/2:black',
                output_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Thumbnail creation failed: {result.stderr}")
                return False
            
            logger.info(f"Thumbnail created successfully: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Thumbnail creation failed: {e}")
            return False

# Lazy loading to avoid import-time failures
_video_editor_instance = None

def get_video_editor():
    """Get video editor instance with lazy loading."""
    global _video_editor_instance
    if _video_editor_instance is None:
        try:
            _video_editor_instance = EnhancedVideoEditor()
            logger.info("Enhanced video editor instance created successfully")
        except Exception as e:
            logger.error(f"Failed to create Enhanced video editor instance: {e}")
            import traceback
            logger.error(f"Video editor creation traceback: {traceback.format_exc()}")
            raise
    return _video_editor_instance

# Create a proxy object that behaves like EnhancedVideoEditor
class VideoEditorProxy:
    """Proxy for lazy-loaded video editor."""
    
    def __getattr__(self, name):
        return getattr(get_video_editor(), name)

# Global instance (proxy)
video_editor = VideoEditorProxy()