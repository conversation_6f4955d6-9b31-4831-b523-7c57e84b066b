from __future__ import annotations

from faster_whisper import WhisperModel


class Transcriber:
    def __init__(self, filename):
        self.filename = filename

    def transcribe(self):
        output_directory = "./"
        model = WhisperModel("base", compute_type="int8")
        segments, info = model.transcribe(self.filename)
        
        # Convert segments to result format for compatibility
        result = {
            "text": "",
            "segments": []
        }
        
        for segment in segments:
            result["text"] += segment.text + " "
            result["segments"].append({
                "start": segment.start,
                "end": segment.end,
                "text": segment.text
            })
        
        result["text"] = result["text"].strip()

        with open("transcription.txt", "w", encoding="utf-8") as txt:
            txt.write(result["text"])

        # Use custom SRT writer with explicit UTF-8 encoding
        self._write_srt_with_utf8(result, output_directory)
    
    def _write_srt_with_utf8(self, result, output_directory):
        """Write SRT file with explicit UTF-8 encoding to prevent character corruption."""
        import os
        
        # Create SRT filename based on the original filename
        base_name = os.path.splitext(os.path.basename(self.filename))[0]
        srt_filename = os.path.join(output_directory, f"{base_name}.srt")
        
        # Write SRT content with explicit UTF-8 encoding
        with open(srt_filename, 'w', encoding='utf-8') as f:
            for i, segment in enumerate(result["segments"], start=1):
                start_time = self._format_timestamp(segment["start"])
                end_time = self._format_timestamp(segment["end"])
                text = segment["text"].strip()
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{text}\n\n")
    
    def _format_timestamp(self, seconds):
        """Format seconds as SRT timestamp: HH:MM:SS,mmm"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        seconds = seconds % 60
        milliseconds = int((seconds - int(seconds)) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{int(seconds):02d},{milliseconds:03d}"