"""
Pollinations.AI API Service

Integrates Pollinations.AI APIs for image, text, and audio generation
with the Ouinhi job queue system and S3 storage.
"""

import aiohttp
import asyncio
import base64
import io
import logging
import urllib.parse
import time
import random
from typing import Any, Union
from datetime import datetime, timedelta

from app.services.s3_service import s3_service
from app.services.redis_service import redis_service

logger = logging.getLogger(__name__)

class APIError(Exception):
    """Custom exception for API errors"""
    def __init__(self, message: str, status_code: int | None = None, error_type: str = "api_error"):
        super().__init__(message)
        self.status_code = status_code
        self.error_type = error_type

class CircuitBreakerError(Exception):
    """Exception raised when circuit breaker is open"""
    pass

class CircuitBreaker:
    """Simple circuit breaker implementation"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
    
    def can_execute(self) -> bool:
        """Check if request can be executed"""
        if self.state == "closed":
            return True
        elif self.state == "open":
            if self.last_failure_time and (time.time() - self.last_failure_time) > self.recovery_timeout:
                self.state = "half-open"
                return True
            return False
        else:  # half-open
            return True
    
    def record_success(self):
        """Record successful request"""
        self.failure_count = 0
        self.state = "closed"
    
    def record_failure(self):
        """Record failed request"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = "open"


class PollinationsService:
    """Service for interacting with Pollinations.AI APIs"""
    
    def __init__(self):
        self.base_image_url = "https://image.pollinations.ai"
        self.base_text_url = "https://text.pollinations.ai"
        # Increase timeout and configure connection settings for stability
        self.timeout = aiohttp.ClientTimeout(
            total=600,      # 10 minutes total
            connect=30,     # 30 seconds to connect
            sock_read=120   # 2 minutes between reads
        )
        
        # Circuit breakers for different endpoints
        self.text_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        self.image_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=60)
        
        # Retry configuration
        self.max_retries = 3
        self.base_delay = 1.0  # Base delay in seconds
        self.max_delay = 60.0  # Maximum delay in seconds
        
        # Get API token from environment (optional but recommended)
        import os
        self.api_token = os.environ.get("POLLINATIONS_API_KEY")
        if self.api_token:
            # Log first few characters for verification (don't log full token for security)
            token_preview = self.api_token[:8] + "..." if len(self.api_token) > 8 else self.api_token
            logger.info(f"Pollinations API token found ({token_preview}) - using authenticated requests with nologo enabled")
        else:
            logger.info("No Pollinations API token found - using anonymous requests (images will have logo)")
        
    async def _exponential_backoff_delay(self, retry_count: int) -> float:
        """Calculate exponential backoff delay with jitter"""
        delay = min(self.base_delay * (2 ** retry_count), self.max_delay)
        # Add jitter to prevent thundering herd problem
        jitter = random.uniform(0.1, 0.3) * delay
        return delay + jitter

    async def _make_request_with_retry(
        self,
        url: str,
        method: str = "GET",
        headers: dict | None = None,
        json_data: dict | None = None,
        params: dict | None = None,
        circuit_breaker: CircuitBreaker | None = None
    ) -> Union[aiohttp.ClientResponse, Any]:
        """Make HTTP request with retry logic and circuit breaker"""
        
        # Check circuit breaker
        if circuit_breaker and not circuit_breaker.can_execute():
            raise CircuitBreakerError("Service temporarily unavailable - circuit breaker is open")
        
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                result = await self._make_request(url, method, headers, json_data, params)
                
                # Record success for circuit breaker
                if circuit_breaker:
                    circuit_breaker.record_success()
                
                if attempt > 0:
                    logger.info(f"Request succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_exception = e
                
                # Log the error with attempt number
                logger.warning(f"Request attempt {attempt + 1} failed for {method} {url}: {str(e)}")
                
                # Determine if we should retry
                should_retry = attempt < self.max_retries
                
                # Check for non-retryable errors
                if isinstance(e, Exception) and "400" in str(e):
                    # Don't retry client errors (400-499)
                    should_retry = False
                    logger.error(f"Client error, not retrying: {e}")
                
                if not should_retry:
                    # Record failure for circuit breaker
                    if circuit_breaker:
                        circuit_breaker.record_failure()
                    break
                
                # Calculate delay for next attempt
                delay = await self._exponential_backoff_delay(attempt)
                logger.info(f"Retrying in {delay:.2f} seconds...")
                await asyncio.sleep(delay)
        
        # Record failure for circuit breaker
        if circuit_breaker:
            circuit_breaker.record_failure()
        
        # If we get here, all retries failed
        error_msg = f"Request failed after {self.max_retries + 1} attempts"
        if last_exception:
            if "502" in str(last_exception) or "503" in str(last_exception):
                error_msg = "Pollinations API is temporarily unavailable. This is usually a temporary issue - please try again in a few minutes."
            elif "timeout" in str(last_exception).lower():
                error_msg = "Request timed out. The API may be experiencing high load - please try again."
            elif "connection" in str(last_exception).lower():
                error_msg = "Connection failed. Please check your internet connection and try again."
            else:
                error_msg = f"{error_msg}: {str(last_exception)}"
        
        raise APIError(error_msg, error_type="retry_exhausted")

    async def _make_request(
        self,
        url: str,
        method: str = "GET",
        headers: dict | None = None,
        json_data: dict | None = None,
        params: dict | None = None
    ) -> Union[aiohttp.ClientResponse, Any]:
        """Make HTTP request with proper error handling and connection management"""
        
        # Create connector with better connection settings
        connector = aiohttp.TCPConnector(
            limit=100,              # Total connection pool size
            limit_per_host=10,      # Connections per host
            ttl_dns_cache=300,      # DNS cache TTL
            use_dns_cache=True,     # Enable DNS caching
            keepalive_timeout=60,   # Keep connections alive
            enable_cleanup_closed=True
        )
        
        async with aiohttp.ClientSession(
            timeout=self.timeout,
            connector=connector
        ) as session:
            # Prepare headers
            request_headers = headers or {}
            
            # Prepare params and add authentication if API token is available
            request_params = params or {}
            if self.api_token:
                # Use query parameter method for authentication (more reliable for nologo)
                request_params["token"] = self.api_token
            
            kwargs = {
                "headers": request_headers,
                "params": request_params
            }
            
            if method.upper() == "POST" and json_data:
                kwargs["json"] = json_data
                
            try:
                async with session.request(
                    method,
                    url,
                    headers=kwargs["headers"],
                    params=kwargs["params"],
                    json=kwargs.get("json")
                ) as response:
                    if response.status >= 400:
                        error_text = await response.text()
                        raise APIError(f"Pollinations API error {response.status}: {error_text}",
                                     status_code=response.status)
                    
                    # For large responses (images), read with streaming to avoid memory issues
                    if method.upper() == "GET" and "image.pollinations.ai" in url:
                        content = await response.read()
                        # Create a temporary response-like object for compatibility
                        class StreamedResponse:
                            def __init__(self, content):
                                self.content = content
                                self.status = response.status
                                self.headers = response.headers
                            
                            async def read(self):
                                return self.content
                            
                            async def text(self):
                                return self.content.decode('utf-8') if isinstance(self.content, bytes) else str(self.content)
                            
                            async def json(self):
                                import json
                                text_content = await self.text()
                                return json.loads(text_content)
                        
                        return StreamedResponse(content)
                    elif "text.pollinations.ai" in url:
                        # For ALL text API requests (both GET and POST), ensure we fully read the response before returning
                        content = await response.read()
                        response_text = content.decode('utf-8') if isinstance(content, bytes) else str(content)
                        
                        class TextResponse:
                            def __init__(self, content, status, headers, response_text):
                                self.content = content
                                self.response_text = response_text
                                self.status = status
                                self.headers = response.headers
                            
                            async def read(self):
                                return self.content
                            
                            async def text(self):
                                return self.response_text
                            
                            async def json(self):
                                import json
                                return json.loads(self.response_text)
                        
                        return TextResponse(content, response.status, response.headers, response_text)
                    else:
                        return response
                        
            except aiohttp.ClientError as e:
                logger.error(f"Connection error for {method} {url}: {e}")
                raise APIError(f"Connection error: {str(e)}", error_type="connection_error")
            except asyncio.TimeoutError as e:
                logger.error(f"Timeout error for {method} {url}: {e}")
                raise APIError(f"Request timeout: {str(e)}", error_type="timeout_error")

    # Image Generation APIs
    
    async def generate_image(
        self,
        prompt: str,
        model: str = "flux",
        width: int = 1024,
        height: int = 1024,
        seed: int | None = None,
        enhance: bool = False,
        nologo: bool = False,
        safe: bool = False,
        transparent: bool = False,
        image_url: str | None = None,
        referrer: str | None = None
    ) -> bytes:
        """
        Generate image using Pollinations Image API
        
        Args:
            prompt: Text description of the image
            model: Model to use (flux, etc.)
            width: Image width in pixels
            height: Image height in pixels
            seed: Seed for reproducible results
            enhance: Enhance prompt using LLM
            nologo: Disable Pollinations logo (requires auth)
            safe: Strict NSFW filtering
            transparent: Generate with transparent background (gptimage only)
            image_url: Input image URL for image-to-image
            referrer: Referrer for authentication
            
        Returns:
            Image bytes
        """
        # URL encode the prompt
        encoded_prompt = urllib.parse.quote(prompt)
        url = f"{self.base_image_url}/prompt/{encoded_prompt}"
        
        # Build parameters
        params = {
            "model": model,
            "width": width,
            "height": height
        }
        
        if seed is not None:
            params["seed"] = seed
        if enhance:
            params["enhance"] = "true"
        # Automatically enable nologo if we have an API token
        if self.api_token or nologo:
            params["nologo"] = "true"
        if safe:
            params["safe"] = "true"
        if transparent:
            params["transparent"] = "true"
        if image_url:
            params["image"] = image_url
        if referrer:
            params["referrer"] = referrer
            
        response = await self._make_request_with_retry(
            url,
            params=params,
            circuit_breaker=self.text_circuit_breaker
        )
        return await response.read()
    
    async def list_image_models(self) -> list[str]:
        """List available image generation models"""
        try:
            url = f"{self.base_image_url}/models"
            logger.info(f"Fetching image models from: {url}")
            
            # Use a simpler approach for model listing (no auth headers needed)
            timeout = aiohttp.ClientTimeout(total=10)  # Shorter timeout for model listing
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Received image models data: {data}")
                        if isinstance(data, list) and len(data) > 0:
                            logger.info(f"Successfully fetched {len(data)} image models")
                            return data
                        else:
                            logger.warning(f"Unexpected image models response format: {type(data)}")
                    else:
                        logger.warning(f"Image models API returned status {response.status}: {await response.text()}")
        except Exception as e:
            logger.warning(f"Failed to fetch image models from API: {e}")
        
        logger.info("Using fallback image models")
        # Return fallback models based on known Pollinations capabilities
        return [
            "flux",
            "flux-realism", 
            "flux-cablyai",
            "flux-anime",
            "any-dark"
        ]

    # Text Generation APIs
    
    async def generate_text(
        self,
        prompt: str,
        model: str = "openai",
        seed: int | None = None,
        temperature: float | None = None,
        top_p: float | None = None,
        presence_penalty: float | None = None,
        frequency_penalty: float | None = None,
        system: str | None = None,
        json_mode: bool = False,
        stream: bool = False,
        referrer: str | None = None
    ) -> str:
        """
        Generate text using Pollinations Text API (GET method)
        
        Args:
            prompt: Text prompt for generation
            model: Model to use (openai, mistral, etc.)
            seed: Seed for reproducible results
            temperature: Controls randomness (0.0-3.0)
            top_p: Nucleus sampling parameter (0.0-1.0)
            presence_penalty: Presence penalty (-2.0 to 2.0)
            frequency_penalty: Frequency penalty (-2.0 to 2.0)
            system: System prompt
            json_mode: Return response as JSON
            stream: Stream response (not supported in this method)
            referrer: Referrer for authentication
            
        Returns:
            Generated text
        """
        encoded_prompt = urllib.parse.quote(prompt)
        url = f"{self.base_text_url}/{encoded_prompt}"
        
        params = {"model": model}
        
        if seed is not None:
            params["seed"] = str(seed)
        if temperature is not None:
            params["temperature"] = str(temperature)
        if top_p is not None:
            params["top_p"] = str(top_p)
        if presence_penalty is not None:
            params["presence_penalty"] = str(presence_penalty)
        if frequency_penalty is not None:
            params["frequency_penalty"] = str(frequency_penalty)
        if system:
            params["system"] = urllib.parse.quote(system)
        if json_mode:
            params["json"] = "true"
        if referrer:
            params["referrer"] = referrer
            
        response = await self._make_request_with_retry(
            url,
            params=params,
            circuit_breaker=self.text_circuit_breaker
        )
        return await response.text()
    
    async def generate_text_chat(
        self,
        messages: list[dict[str, Any]],
        model: str = "openai",
        seed: int | None = None,
        temperature: float | None = None,
        top_p: float | None = None,
        presence_penalty: float | None = None,
        frequency_penalty: float | None = None,
        stream: bool = False,
        json_mode: bool = False,
        tools: list[dict[str, Any]] | None = None,
        tool_choice: str | dict[str, Any] | None = None,
        referrer: str | None = None
    ) -> dict[str, Any]:
        """
        Generate text using Pollinations Chat API (POST method)
        Supports vision, function calling, and advanced features
        
        Args:
            messages: Array of message objects
            model: Model to use
            seed: Seed for reproducible results
            temperature: Controls randomness
            top_p: Nucleus sampling parameter
            presence_penalty: Presence penalty
            frequency_penalty: Frequency penalty
            stream: Stream response
            json_mode: Force JSON output
            tools: Available tools for function calling
            tool_choice: Tool choice strategy
            referrer: Referrer for authentication
            
        Returns:
            Chat completion response
        """
        url = f"{self.base_text_url}/openai"
        
        payload = {
            "model": model,
            "messages": messages
        }
        
        if seed is not None:
            payload["seed"] = seed
        if temperature is not None:
            payload["temperature"] = temperature
        if top_p is not None:
            payload["top_p"] = top_p
        if presence_penalty is not None:
            payload["presence_penalty"] = presence_penalty
        if frequency_penalty is not None:
            payload["frequency_penalty"] = frequency_penalty
        if stream:
            payload["stream"] = stream
        if json_mode:
            payload["response_format"] = {"type": "json_object"}
        if tools:
            payload["tools"] = tools
        if tool_choice:
            payload["tool_choice"] = tool_choice
        if referrer:
            payload["referrer"] = referrer
            
        headers = {"Content-Type": "application/json"}
        response = await self._make_request_with_retry(
            url,
            method="POST",
            headers=headers,
            json_data=payload,
            circuit_breaker=self.text_circuit_breaker
        )
        return await response.json()
    
    async def list_text_models(self) -> dict[str, Any]:
        """List available text generation models and voices"""
        try:
            url = f"{self.base_text_url}/models"
            logger.info(f"Fetching text models from: {url}")
            
            # Use a simpler approach for model listing (no auth headers needed)
            timeout = aiohttp.ClientTimeout(total=10)  # Shorter timeout for model listing
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Received text models data: {data}")
                        if isinstance(data, dict) and data:
                            logger.info(f"Successfully fetched text models")
                            return data
                        elif isinstance(data, list):
                            # Handle case where API returns list instead of dict
                            logger.info(f"Converting list response to dict format")
                            return {
                                "text_models": data,
                                "voices": [
                                    {"name": "alloy", "description": "Balanced voice"},
                                    {"name": "echo", "description": "Clear voice"},
                                    {"name": "fable", "description": "Expressive voice"},
                                    {"name": "onyx", "description": "Deep voice"},
                                    {"name": "nova", "description": "Bright voice"},
                                    {"name": "shimmer", "description": "Gentle voice"}
                                ]
                            }
                        else:
                            logger.warning(f"Unexpected text models response format: {type(data)}")
                    else:
                        logger.warning(f"Text models API returned status {response.status}: {await response.text()}")
        except Exception as e:
            logger.warning(f"Failed to fetch text models from API: {e}")
        
        logger.info("Using fallback text models")
        # Return fallback models based on known Pollinations capabilities
        return {
            "text_models": [
                "openai",
                "mistral", 
                "anthropic"
            ],
            "voices": [
                {"name": "alloy", "description": "Balanced voice"},
                {"name": "echo", "description": "Clear voice"},
                {"name": "fable", "description": "Expressive voice"},
                {"name": "onyx", "description": "Deep voice"},
                {"name": "nova", "description": "Bright voice"},
                {"name": "shimmer", "description": "Gentle voice"}
            ]
        }

    # Audio/TTS APIs
    
    async def generate_audio_tts(
        self,
        text: str,
        voice: str = "alloy",
        model: str = "openai-audio"
    ) -> bytes:
        """
        Generate text-to-speech audio using GET method
        
        Args:
            text: Text to synthesize
            voice: Voice to use (alloy, echo, fable, onyx, nova, shimmer)
            model: Must be "openai-audio" for TTS
            
        Returns:
            Audio bytes (MP3 format)
        """
        encoded_text = urllib.parse.quote(text)
        url = f"{self.base_text_url}/{encoded_text}"
        
        params = {
            "model": model,
            "voice": voice
        }
        
        response = await self._make_request_with_retry(
            url,
            params=params,
            circuit_breaker=self.image_circuit_breaker
        )
        return await response.read()
    
    async def transcribe_audio(
        self,
        audio_data: bytes,
        audio_format: str = "wav",
        question: str = "Transcribe this audio"
    ) -> str:
        """
        Transcribe audio using Pollinations STT
        
        Args:
            audio_data: Audio file bytes
            audio_format: Audio format (wav, mp3)
            question: Optional instruction text
            
        Returns:
            Transcription text
        """
        # Encode audio to base64
        base64_audio = base64.b64encode(audio_data).decode('utf-8')
        
        url = f"{self.base_text_url}/openai"
        payload = {
            "model": "openai-audio",
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {
                            "type": "input_audio",
                            "input_audio": {
                                "data": base64_audio,
                                "format": audio_format
                            }
                        }
                    ]
                }
            ]
        }
        
        headers = {"Content-Type": "application/json"}
        response = await self._make_request_with_retry(
            url,
            method="POST",
            headers=headers,
            json_data=payload,
            circuit_breaker=self.text_circuit_breaker
        )
        result = await response.json()
        
        return result.get('choices', [{}])[0].get('message', {}).get('content', '')

    # Vision API
    
    async def analyze_image(
        self,
        image_data: bytes | None = None,
        image_url: str | None = None,
        question: str = "What's in this image?",
        model: str = "openai"
    ) -> str:
        """
        Analyze image using vision capabilities
        
        Args:
            image_data: Image file bytes
            image_url: URL to image
            question: Question about the image
            model: Vision model to use
            
        Returns:
            Analysis text
        """
        if not image_data and not image_url:
            raise ValueError("Either image_data or image_url must be provided")
            
        content: list[dict[str, Any]] = [{"type": "text", "text": question}]
        
        if image_data:
            # Detect image format (simple approach)
            image_format = "jpeg"  # Default
            if image_data.startswith(b'\x89PNG'):
                image_format = "png"
            elif image_data.startswith(b'GIF'):
                image_format = "gif"
                
            base64_image = base64.b64encode(image_data).decode('utf-8')
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/{image_format};base64,{base64_image}"
                }
            })
        elif image_url:
            content.append({
                "type": "image_url", 
                "image_url": {"url": image_url}
            })
            
        url = f"{self.base_text_url}/openai"
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": content}],
            "max_tokens": 500
        }
        
        headers = {"Content-Type": "application/json"}
        response = await self._make_request_with_retry(
            url,
            method="POST",
            headers=headers,
            json_data=payload,
            circuit_breaker=self.text_circuit_breaker
        )
        result = await response.json()
        
        return result.get('choices', [{}])[0].get('message', {}).get('content', '')

    # Utility methods for job queue integration
    
    async def save_generated_content_to_s3(
        self, 
        content: bytes, 
        filename: str,
        content_type: str = "application/octet-stream"
    ) -> str:
        """Save generated content to S3 and return URL"""
        try:
            # Create temporary file
            import tempfile
            import os
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(content)
                temp_path = temp_file.name
            
            try:
                s3_key = f"pollinations/{datetime.now().strftime('%Y/%m/%d')}/{filename}"
                result = await s3_service.upload_file_with_metadata(
                    temp_path,
                    s3_key,
                    content_type=content_type,
                    public=True
                )
                return result["file_url"]
            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_path)
                except:
                    pass
        except Exception as e:
            logger.error(f"Failed to save content to S3: {e}")
            raise

    async def cache_models_list(self, cache_key: str, models_data: Any, expire_seconds: int = 3600):
        """Cache models list in Redis"""
        try:
            await redis_service.set(cache_key, models_data, expire=expire_seconds)
        except Exception as e:
            logger.warning(f"Failed to cache models list: {e}")

    async def get_cached_models_list(self, cache_key: str) -> Any | None:
        """Get cached models list from Redis"""
        try:
            return await redis_service.get(cache_key)
        except Exception as e:
            logger.warning(f"Failed to get cached models list: {e}")
            return None


# Global service instance
pollinations_service = PollinationsService()