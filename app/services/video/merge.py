"""
Service for video merge operations - combining video concatenation with audio overlay.

This service provides functionality to merge multiple videos into a single video
with optional background audio/music overlay. It combines the features of video
concatenation and audio addition in a single operation.

Features:
- Concatenate multiple videos with transition effects
- Add optional background music/audio overlay
- Control volume levels for both video and audio
- Support for fade effects on background audio
- Multiple audio sync modes (replace, mix, overlay)
- Professional transition effects between segments
"""
import os
import tempfile
import logging
import uuid
import subprocess
import asyncio
from typing import List, Dict, Any, Optional

from app.utils.media import download_media_file, SUPPORTED_VIDEO_FORMATS
from app.services.s3 import s3_service
from app.services.video.concatenate import concatenate_videos, check_s3_configuration
from app.models import VideoMergeResult

# Configure logging
logger = logging.getLogger(__name__)

class VideoMergeService:
    """Service for video merge operations."""
    
    async def process_job(self, job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a video merge job that combines concatenation with audio overlay.
        
        Args:
            job_id: The ID of the job.
            data: The job data containing video URLs, audio URL, and merge settings.
            
        Returns:
            Dictionary with the result of the merge, following VideoMergeResult structure.
            
        Raises:
            Exception: If the process fails.
        """
        try:
            # Extract job parameters
            video_urls = data.get("video_urls", [])
            background_audio_url = data.get("background_audio_url")
            output_format = data.get("output_format", ".mp4")
            
            # Video concatenation settings
            transition = data.get("transition", "none")
            transition_duration = data.get("transition_duration", 1.0)
            max_segment_duration = data.get("max_segment_duration")
            total_duration_limit = data.get("total_duration_limit")
            
            # Audio settings (only used if background_audio_url is provided)
            video_volume = data.get("video_volume", 100)
            audio_volume = data.get("audio_volume", 20)
            sync_mode = data.get("sync_mode", "overlay")
            fade_in_duration = data.get("fade_in_duration")
            fade_out_duration = data.get("fade_out_duration")
            
            # Add progress logging
            logger.info(f"Starting video merge job {job_id} with {len(video_urls)} videos")
            if background_audio_url:
                logger.info(f"Job {job_id} includes background audio: {background_audio_url}")
            
            # Check S3 configuration before starting
            s3_status = await check_s3_configuration()
            if not s3_status["initialized"]:
                logger.warning(f"S3 storage not properly initialized: {s3_status['details']}")
                
                # Log all environment variables for debugging
                for var, value in s3_status["env_vars"].items():
                    logger.debug(f"S3 config - {var}: {value}")
                    
                # Log that we'll continue with local storage fallback
                logger.warning("S3 not available. Videos will be stored locally as fallback.")
            else:
                logger.info(f"S3 properly configured with bucket: {s3_status['bucket']}")
            
            # Step 1: Concatenate videos first
            logger.info(f"Job {job_id}: Step 1 - Concatenating {len(video_urls)} videos")
            concatenation_data = {
                "video_urls": video_urls,
                "output_format": output_format,
                "transition": transition,
                "transition_duration": transition_duration,
                "max_segment_duration": max_segment_duration,
                "total_duration_limit": total_duration_limit
            }
            
            # Call the concatenation function directly
            concatenated_result = await concatenate_videos(job_id, video_urls, output_format, concatenation_data)
            logger.info(f"Job {job_id}: Video concatenation completed successfully")
            
            # Step 2: Add background audio if provided
            if background_audio_url:
                logger.info(f"Job {job_id}: Step 2 - Adding background audio")
                
                # The concatenated video should be available as a URL or local file
                concatenated_video_url = concatenated_result.get("url")
                if not concatenated_video_url:
                    raise RuntimeError("Concatenation did not produce a valid video URL")
                
                # Add audio to the concatenated video
                audio_result = await self._add_background_audio(
                    job_id=job_id,
                    video_url=concatenated_video_url,
                    audio_url=background_audio_url,
                    video_volume=video_volume,
                    audio_volume=audio_volume,
                    sync_mode=sync_mode,
                    fade_in_duration=fade_in_duration,
                    fade_out_duration=fade_out_duration,
                    output_format=output_format
                )
                
                logger.info(f"Job {job_id}: Background audio addition completed successfully")
                
                # Use the audio result as the final result
                final_result = {
                    "url": audio_result["url"],
                    "path": audio_result["path"],
                    "duration": audio_result.get("duration", concatenated_result.get("duration", 0)),
                    "segments_processed": len(video_urls),
                    "has_background_audio": True
                }
            else:
                # No background audio, use concatenation result directly
                logger.info(f"Job {job_id}: No background audio specified, merge completed")
                final_result = {
                    "url": concatenated_result["url"],
                    "path": concatenated_result["path"],
                    "duration": concatenated_result.get("duration", 0),
                    "segments_processed": len(video_urls),
                    "has_background_audio": False
                }
            
            # Check if the result has a proper S3 URL or a local file path
            if "url" in final_result and final_result["url"].startswith("file://"):
                logger.warning(f"Job {job_id} completed but video stored locally, not in S3: {final_result['path']}")
            else:
                logger.info(f"Successfully completed video merge job {job_id} with S3 upload: {final_result['url']}")
            
            return final_result
            
        except Exception as e:
            logger.error(f"Error in video merge job {job_id}: {e}")
            raise
    
    async def _add_background_audio(
        self,
        job_id: str,
        video_url: str,
        audio_url: str,
        video_volume: int = 100,
        audio_volume: int = 20,
        sync_mode: str = "overlay",
        fade_in_duration: Optional[float] = None,
        fade_out_duration: Optional[float] = None,
        output_format: str = ".mp4"
    ) -> Dict[str, Any]:
        """
        Add background audio to a video.
        
        Args:
            job_id: The ID of the job
            video_url: URL of the video file
            audio_url: URL of the audio file
            video_volume: Volume level for the video track (0-100)
            audio_volume: Volume level for the audio track (0-100)
            sync_mode: Audio synchronization mode ('replace', 'mix', 'overlay')
            fade_in_duration: Duration for audio fade-in effect
            fade_out_duration: Duration for audio fade-out effect
            output_format: Output video format
            
        Returns:
            Dictionary with the result of the audio addition
        """
        # Create temp directory for processing
        temp_dir = os.path.join("temp", f"merge_audio_{job_id}")
        os.makedirs(temp_dir, exist_ok=True)
        
        try:
            # Download video and audio files
            logger.info(f"Job {job_id}: Downloading video and audio files")
            
            # Download video file
            if video_url.startswith("file://"):
                # Local file from concatenation
                video_path = video_url[7:]  # Remove "file://" prefix
            else:
                # Remote file, download it
                video_path, _ = await download_media_file(video_url, temp_dir)
            
            # Download audio file
            audio_path, _ = await download_media_file(audio_url, temp_dir)
            
            # Create output filename
            output_filename = f"merged_with_audio_{job_id}{output_format}"
            output_path = os.path.join(temp_dir, output_filename)
            
            # Build FFmpeg command based on sync mode
            logger.info(f"Job {job_id}: Mixing audio with sync mode: {sync_mode}")
            
            # Convert volume percentages to FFmpeg volume format
            video_vol = video_volume / 100.0
            audio_vol = audio_volume / 100.0
            
            # Build audio filter for fade effects
            audio_filters = []
            if fade_in_duration:
                audio_filters.append(f"afade=t=in:d={fade_in_duration}")
            if fade_out_duration:
                audio_filters.append(f"afade=t=out:d={fade_out_duration}")
            
            audio_filter_str = ",".join(audio_filters) if audio_filters else ""
            
            if sync_mode == "replace":
                # Replace video audio with new audio
                cmd = [
                    "ffmpeg", "-y",
                    "-i", video_path,
                    "-i", audio_path,
                    "-c:v", "copy",
                    "-map", "0:v:0",
                    "-map", "1:a:0",
                    "-shortest"
                ]
                if audio_filter_str:
                    cmd.extend(["-af", f"volume={audio_vol},{audio_filter_str}"])
                else:
                    cmd.extend(["-af", f"volume={audio_vol}"])
                cmd.append(output_path)
                
            elif sync_mode == "mix":
                # Mix video audio with new audio
                if audio_filter_str:
                    audio_filter = f"[1:a]volume={audio_vol},{audio_filter_str}[a1];[0:a]volume={video_vol}[a0];[a0][a1]amix=inputs=2[aout]"
                else:
                    audio_filter = f"[1:a]volume={audio_vol}[a1];[0:a]volume={video_vol}[a0];[a0][a1]amix=inputs=2[aout]"
                
                cmd = [
                    "ffmpeg", "-y",
                    "-i", video_path,
                    "-i", audio_path,
                    "-filter_complex", audio_filter,
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-shortest",
                    output_path
                ]
                
            else:  # overlay mode (default)
                # Overlay new audio over video audio
                if audio_filter_str:
                    audio_filter = f"[1:a]volume={audio_vol},{audio_filter_str}[a1];[0:a][a1]amix=inputs=2:weights='{video_vol} {audio_vol}'[aout]"
                else:
                    audio_filter = f"[1:a]volume={audio_vol}[a1];[0:a][a1]amix=inputs=2:weights='{video_vol} {audio_vol}'[aout]"
                
                cmd = [
                    "ffmpeg", "-y",
                    "-i", video_path,
                    "-i", audio_path,
                    "-filter_complex", audio_filter,
                    "-map", "0:v:0",
                    "-map", "[aout]",
                    "-c:v", "copy",
                    "-shortest",
                    output_path
                ]
            
            # Execute FFmpeg command
            logger.info(f"Job {job_id}: Executing FFmpeg command for audio mixing")
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            
            if result.returncode != 0:
                logger.error(f"Job {job_id}: FFmpeg failed with error: {result.stderr}")
                raise RuntimeError(f"FFmpeg audio mixing failed: {result.stderr}")
            
            # Verify output file was created
            if not os.path.exists(output_path):
                raise RuntimeError(f"Output file was not created: {output_path}")
            
            # Get video duration
            duration = await self._get_video_duration(output_path)
            
            # Upload to S3
            logger.info(f"Job {job_id}: Uploading merged video with audio to S3")
            s3_object_name = f"videos/merged/{output_filename}"
            video_url = await s3_service.upload_file(output_path, s3_object_name)
            
            return {
                "url": video_url,
                "path": s3_object_name,
                "duration": duration
            }
            
        finally:
            # Clean up temp directory
            try:
                import shutil
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    logger.debug(f"Job {job_id}: Cleaned up temp directory: {temp_dir}")
            except Exception as e:
                logger.warning(f"Job {job_id}: Failed to clean up temp directory {temp_dir}: {e}")
    
    async def _get_video_duration(self, video_path: str) -> float:
        """
        Get the duration of a video file using FFprobe.
        
        Args:
            video_path: Path to the video file
            
        Returns:
            Duration in seconds as a float
        """
        try:
            cmd = [
                "ffprobe", "-v", "quiet", "-print_format", "json",
                "-show_format", video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            import json
            probe_data = json.loads(result.stdout)
            duration = float(probe_data["format"]["duration"])
            
            return duration
            
        except Exception as e:
            logger.warning(f"Failed to get video duration: {e}")
            return 0.0

# Create a singleton instance
merge_service = VideoMergeService()