"""
Service for adding text overlays to videos using FFmpeg.
"""
import os
import re
import uuid
import logging
import tempfile
import subprocess
from typing import Dict, Any, Tuple
from urllib.parse import urlparse

from app.utils.media import download_media_file
from app.services.s3 import s3_service

# Configure logging
logger = logging.getLogger(__name__)

class TextOverlayService:
    """Service for adding text overlays to videos using FFmpeg."""
    
    def __init__(self):
        """Initialize the text overlay service with presets."""
        self.presets = {
            "title_overlay": {
                "description": "Large title text at the top, optimized for short phrases",
                "options": {
                    "duration": 5,
                    "font_size": 60,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.85,
                    "boxborderw": 80,
                    "position": "top-center",
                    "y_offset": 80,
                    "line_spacing": 18,
                    "auto_wrap": True
                }
            },
            "subtitle": {
                "description": "Subtitle text at the bottom, with good readability",
                "options": {
                    "duration": 10,
                    "font_size": 42,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.8,
                    "boxborderw": 60,
                    "position": "bottom-center",
                    "y_offset": 100,
                    "line_spacing": 15,
                    "auto_wrap": True
                }
            },
            "watermark": {
                "description": "Small, subtle watermark text",
                "options": {
                    "duration": 999999,
                    "font_size": 28,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.6,
                    "boxborderw": 45,
                    "position": "bottom-right",
                    "y_offset": 40,
                    "line_spacing": 10,
                    "auto_wrap": True
                }
            },
            "alert": {
                "description": "Alert/notification style overlay, prominent and attention-grabbing",
                "options": {
                    "duration": 4,
                    "font_size": 56,
                    "font_color": "white",
                    "box_color": "red",
                    "box_opacity": 0.9,
                    "boxborderw": 75,
                    "position": "center",
                    "y_offset": 0,
                    "line_spacing": 16,
                    "auto_wrap": True
                }
            },
            "modern_caption": {
                "description": "Modern caption style with solid background and good padding",
                "options": {
                    "duration": 5,
                    "font_size": 50,
                    "font_color": "black",
                    "box_color": "white",
                    "box_opacity": 0.85,
                    "boxborderw": 70,
                    "position": "top-center",
                    "y_offset": 100,
                    "line_spacing": 16,
                    "auto_wrap": True
                }
            },
            "social_post": {
                "description": "Instagram/TikTok style caption, trendy and engaging",
                "options": {
                    "duration": 6,
                    "font_size": 48,
                    "font_color": "white",
                    "box_color": "black",
                    "box_opacity": 0.7,
                    "boxborderw": 65,
                    "position": "bottom-center",
                    "y_offset": 120,
                    "line_spacing": 15,
                    "auto_wrap": True
                }
            },
            "quote": {
                "description": "Quote or testimonial style with elegant presentation",
                "options": {
                    "duration": 8,
                    "font_size": 44,
                    "font_color": "white",
                    "box_color": "navy",
                    "box_opacity": 0.8,
                    "boxborderw": 70,
                    "position": "center",
                    "y_offset": 0,
                    "line_spacing": 17,
                    "auto_wrap": True
                }
            },
            "news_ticker": {
                "description": "News ticker style for breaking news or updates",
                "options": {
                    "duration": 15,
                    "font_size": 36,
                    "font_color": "white",
                    "box_color": "darkred",
                    "box_opacity": 0.95,
                    "boxborderw": 50,
                    "position": "bottom-center",
                    "y_offset": 50,
                    "line_spacing": 12,
                    "auto_wrap": True
                }
            }
        }
        logger.info("Text overlay service initialized")
    
    def get_available_presets(self) -> Dict[str, Any]:
        """Get all available text overlay presets."""
        return self.presets
    
    def wrap_text(self, text: str, max_width: int = 25) -> str:
        """
        Improved text wrapping function with proper spacing for video overlays.
        
        Args:
            text: Text to wrap
            max_width: Maximum characters per line
            
        Returns:
            Wrapped text with newlines
        """
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            # Calculate space needed (word length + space if not first word)
            space_needed = len(word) + (1 if current_line else 0)
            
            if current_length + space_needed > max_width and current_line:
                # Finalize current line and start new one
                lines.append(' '.join(current_line))
                current_line = [word]
                current_length = len(word)
            else:
                current_line.append(word)
                current_length += space_needed
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return '\\n'.join(lines)  # Use actual newline, not escaped
    
    def escape_text_for_ffmpeg(self, text: str) -> str:
        """
        Escape special characters for FFmpeg drawtext filter.
        
        Args:
            text: Text to escape
            
        Returns:
            Escaped text safe for FFmpeg
        """
        # First, handle the text encoding
        if isinstance(text, str):
            text = text.encode('utf-8', errors='ignore').decode('utf-8')
        
        # FFmpeg drawtext filter requires these escapes:
        # Order is important - escape backslash first
        text = text.replace('\\\\', '\\\\\\\\\\\\\\\\')  # More aggressive backslash escaping
        text = text.replace(':', '\\\\\\\\:')
        text = text.replace("'", "\\\\\\\\'")
        text = text.replace('"', '\\\\\\\\"')
        text = text.replace('[', '\\\\\\\\[')
        text = text.replace(']', '\\\\\\\\]')
        text = text.replace(',', '\\\\\\\\,')
        text = text.replace(';', '\\\\\\\\;')
        text = text.replace('=', '\\\\\\\\=')
        text = text.replace('%', '\\\\\\\\%')
        text = text.replace('{', '\\\\\\\\{')
        text = text.replace('}', '\\\\\\\\}')
        
        # Handle French accented characters and other special characters
        text = text.replace('à', 'a')
        text = text.replace('á', 'a')
        text = text.replace('â', 'a')
        text = text.replace('ã', 'a')
        text = text.replace('ä', 'a')
        text = text.replace('å', 'a')
        text = text.replace('é', 'e')
        text = text.replace('è', 'e')
        text = text.replace('ê', 'e')
        text = text.replace('ë', 'e')
        text = text.replace('í', 'i')
        text = text.replace('ì', 'i')
        text = text.replace('î', 'i')
        text = text.replace('ï', 'i')
        text = text.replace('ó', 'o')
        text = text.replace('ò', 'o')
        text = text.replace('ô', 'o')
        text = text.replace('õ', 'o')
        text = text.replace('ö', 'o')
        text = text.replace('ú', 'u')
        text = text.replace('ù', 'u')
        text = text.replace('û', 'u')
        text = text.replace('ü', 'u')
        text = text.replace('ç', 'c')
        text = text.replace('ñ', 'n')
        text = text.replace('ÿ', 'y')
        
        # Handle special punctuation and symbols that might cause issues
        text = text.replace('—', '-')  # em dash
        text = text.replace('–', '-')  # en dash
        text = text.replace('"', '"')  # smart quotes
        text = text.replace('"', '"')
        text = text.replace(''', "'")
        text = text.replace(''', "'")
        text = text.replace('…', '...')  # ellipsis
        
        # Handle newlines properly for FFmpeg
        text = text.replace('\\n', '\\\\\\\\n')
        text = text.replace('\\r', '\\\\\\\\r')
        
        # Handle special spaces that might get mangled
        text = text.replace('  ', ' ')  # Normalize double spaces
        
        return text
    
    def get_font_file(self, text: str) -> str:
        """
        Get appropriate font file for the text.
        
        Args:
            text: Text content to check for emojis
            
        Returns:
            Path to the appropriate font file
        """
        # Font directory path
        font_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'fonts')
        
        # Check if text contains emojis
        has_emoji = bool(re.search(r'[\U0001F600-\U0001F64F]|[\U0001F300-\U0001F5FF]|[\U0001F680-\U0001F6FF]|[\U0001F1E0-\U0001F1FF]|[\*********-\U000027BF]|[\U0001F900-\U0001F9FF]', text))
        
        # Font files with priority order - local fonts first
        if has_emoji:
            font_files = [
                os.path.join(font_dir, "OpenSansEmoji.ttf"),
                os.path.join(font_dir, "DejaVuSans.ttf"),
                os.path.join(font_dir, "Roboto-Regular.ttf"),
                os.path.join(font_dir, "Arial.ttf"),
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
        else:
            font_files = [
                os.path.join(font_dir, "Roboto-Bold.ttf"),
                os.path.join(font_dir, "Arial.ttf"),
                os.path.join(font_dir, "DejaVuSans-Bold.ttf"),
                os.path.join(font_dir, "DejaVuSans.ttf"),
                "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
        
        # Find first existing font file
        for font in font_files:
            if os.path.exists(font):
                return font
        
        # Final fallback
        fallback_font = os.path.join(font_dir, "DejaVuSans.ttf")
        if not os.path.exists(fallback_font):
            fallback_font = "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"
        
        return fallback_font
    
    def get_position_coordinates(self, position: str, y_offset: int) -> str:
        """
        Get FFmpeg position coordinates for text placement.
        
        Args:
            position: Position name (e.g., 'top-center', 'bottom-left')
            y_offset: Vertical offset in pixels
            
        Returns:
            FFmpeg position coordinates string
        """
        position_map = {
            'top-left': 'x=30:y=30',
            'top-center': 'x=(w-text_w)/2:y=30',
            'top-right': 'x=w-text_w-30:y=30',
            'center-left': 'x=30:y=(h-text_h)/2',
            'center': 'x=(w-text_w)/2:y=(h-text_h)/2',
            'center-right': 'x=w-text_w-30:y=(h-text_h)/2',
            'bottom-left': 'x=30:y=h-text_h-30',
            'bottom-center': 'x=(w-text_w)/2:y=h-text_h-30',
            'bottom-right': 'x=w-text_w-30:y=h-text_h-30',
        }
        
        position_coords = position_map.get(position, 'x=(w-text_w)/2:y=50')
        
        # Adjust y_offset based on position
        if 'top' in position and y_offset != 30:
            position_coords = position_coords.replace('y=30', f'y={y_offset}')
        elif 'bottom' in position and y_offset != 30:
            position_coords = position_coords.replace('y=h-text_h-30', f'y=h-text_h-{y_offset}')
        elif 'center' in position and 'top' not in position and 'bottom' not in position and y_offset != 0:
            position_coords = position_coords.replace('y=(h-text_h)/2', f'y=(h-text_h)/2+{y_offset}')
        
        return position_coords
    
    async def add_text_overlay(
        self,
        video_url: str,
        text: str,
        options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Add text overlay to a video using FFmpeg.
        
        Args:
            video_url: URL of the video to add text overlay to
            text: Text content to overlay
            options: Text overlay options (duration, font_size, colors, etc.)
            
        Returns:
            Dictionary with result information
        """
        logger.info(f"Starting text overlay for video: {video_url}")
        
        # Extract options with defaults
        duration = options.get('duration', 5)
        font_size = options.get('font_size', 48)
        font_color = options.get('font_color', 'white')
        box_color = options.get('box_color', 'black')
        box_opacity = options.get('box_opacity', 0.8)
        boxborderw = options.get('boxborderw', 60)
        position = options.get('position', 'bottom-center')
        y_offset = options.get('y_offset', 50)
        line_spacing = options.get('line_spacing', 8)
        auto_wrap = options.get('auto_wrap', True)
        
        # Apply text wrapping before escaping
        if auto_wrap:
            text = self.wrap_text(text)
        
        # Now escape the text for FFmpeg
        escaped_text = self.escape_text_for_ffmpeg(text)
        
        # Get position coordinates
        position_coords = self.get_position_coordinates(position, y_offset)
        
        # Get appropriate font file
        font_file = self.get_font_file(text)
        
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Create temporary text file for complex text handling
        text_file_path = os.path.join(tempfile.gettempdir(), f"{request_id}_text.txt")
        
        input_path = None
        output_path = None
        
        try:
            # Write text to temporary file
            with open(text_file_path, 'w', encoding='utf-8') as f:
                f.write(text)  # Write the unescaped text
            
            # Download video file
            input_path, file_ext = await download_media_file(video_url)
            
            # Generate output filename
            output_filename = f"{request_id}_output.mp4"
            output_path = os.path.join(tempfile.gettempdir(), output_filename)
            
            # Build drawtext filter using textfile instead of text parameter
            drawtext_filter = (
                f"drawtext=fontfile={font_file}:"
                f"textfile={text_file_path}:"
                f"fontcolor={font_color}:"
                f"fontsize={font_size}:"
                f"box=1:"
                f"boxcolor={box_color}@{box_opacity}:"
                f"boxborderw={boxborderw}:"
                f"line_spacing={line_spacing}:"
                f"{position_coords}:"
                f"enable='between(t,0,{duration})'"
            )
            
            # Build FFmpeg command
            ffmpeg_command = [
                "ffmpeg",
                "-i", input_path,
                "-vf", drawtext_filter,
                "-c:v", "libx264",
                "-crf", "23",
                "-preset", "medium",
                "-c:a", "copy",  # Copy audio without re-encoding
                "-y",
                output_path
            ]
            
            # Ensure UTF-8 environment for subprocess
            env = os.environ.copy()
            env['LC_ALL'] = 'C.UTF-8'
            env['LANG'] = 'C.UTF-8'
            env['PYTHONIOENCODING'] = 'utf-8'
            
            # Execute FFmpeg command
            result = subprocess.run(
                ffmpeg_command, 
                check=True, 
                capture_output=True, 
                text=True, 
                encoding='utf-8', 
                env=env
            )
            
            logger.info("FFmpeg processing completed successfully")
            
            # Upload the processed video to S3
            object_name = f"text-overlay/{output_filename}"
            video_url = await s3_service.upload_file(output_path, object_name)
            
            # Get video duration
            duration_cmd = [
                "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                "-of", "csv=p=0", output_path
            ]
            duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)
            video_duration = float(duration_result.stdout.strip()) if duration_result.stdout.strip() else 0.0
            
            logger.info(f"Text overlay completed successfully. Output URL: {video_url}")
            
            return {
                "video_url": video_url,
                "duration": video_duration
            }
            
        except subprocess.CalledProcessError as e:
            error_msg = f"FFmpeg command failed: {e.stderr}"
            logger.error(error_msg)
            
            # Try fallback strategies
            try:
                # Strategy 1: Try with text parameter instead of textfile
                logger.info("Trying fallback strategy with direct text parameter")
                drawtext_filter = (
                    f"drawtext=fontfile={font_file}:"
                    f"text='{escaped_text}':"
                    f"fontcolor={font_color}:"
                    f"fontsize={font_size}:"
                    f"box=1:"
                    f"boxcolor={box_color}@{box_opacity}:"
                    f"boxborderw={boxborderw}:"
                    f"line_spacing={line_spacing}:"
                    f"{position_coords}:"
                    f"enable='between(t,0,{duration})'"
                )
                
                ffmpeg_command[ffmpeg_command.index("-vf") + 1] = drawtext_filter
                result = subprocess.run(
                    ffmpeg_command, 
                    check=True, 
                    capture_output=True, 
                    text=True, 
                    encoding='utf-8', 
                    env=env
                )
                
                # Upload the processed video to S3
                object_name = f"text-overlay/{output_filename}"
                video_url = await s3_service.upload_file(output_path, object_name)
                
                # Get video duration
                duration_cmd = [
                    "ffprobe", "-v", "quiet", "-show_entries", "format=duration",
                    "-of", "csv=p=0", output_path
                ]
                duration_result = subprocess.run(duration_cmd, capture_output=True, text=True)
                video_duration = float(duration_result.stdout.strip()) if duration_result.stdout.strip() else 0.0
                
                logger.info(f"Text overlay completed with fallback strategy. Output URL: {video_url}")
                
                return {
                    "video_url": video_url,
                    "duration": video_duration
                }
                
            except subprocess.CalledProcessError:
                logger.error("Fallback strategy also failed")
                raise Exception(f"FFmpeg failed with all fallback strategies. Last error: {e.stderr}")
                
        except Exception as e:
            logger.error(f"Text overlay failed: {str(e)}")
            raise Exception(f"Text overlay failed: {str(e)}")
            
        finally:
            # Clean up temporary files
            for temp_file in [input_path, output_path, text_file_path]:
                if temp_file and os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                        logger.info(f"Deleted temporary file: {temp_file}")
                    except Exception as e:
                        logger.warning(f"Failed to delete temporary file {temp_file}: {e}")
    
    async def process_text_overlay_job(self, job_id: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process text overlay job.
        
        Args:
            job_id: The job ID (unused but required for job queue signature)
            data: Dictionary containing text overlay parameters
            
        Returns:
            Dictionary with text overlay results
        """
        try:
            video_url = data.get('video_url')
            text = data.get('text')
            options = data.get('options', {})
            preset_used = data.get('preset_used')
            
            if not video_url:
                raise ValueError("video_url is required")
            
            if not text:
                raise ValueError("text is required")
            
            logger.info(f"Starting text overlay job {job_id}: {video_url}")
            
            # Add text overlay to video
            result = await self.add_text_overlay(video_url, text, options)
            
            # Add preset information if used
            if preset_used:
                result["preset_used"] = preset_used
            
            logger.info(f"Text overlay job {job_id} completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Text overlay job {job_id} failed: {str(e)}", exc_info=True)
            raise Exception(f"Text overlay failed: {str(e)}")


# Create a singleton instance
text_overlay_service = TextOverlayService()