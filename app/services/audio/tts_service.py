"""
Unified TTS service supporting multiple providers (Kokoro and Edge TTS).
"""
import os
import uuid
import asyncio
import logging
import json
import aiohttp
from typing import Optional, Dict, Any, List
from app.services.s3 import s3_service
from app.services.audio.edge_tts_service import edge_tts_service
from app.services.audio.kokoro_tts_service import kokoro_tts_service, DEFAULT_LANGUAGE

# Configure logging
logger = logging.getLogger(__name__)

# TTS Provider settings
DEFAULT_TTS_PROVIDER = os.environ.get("TTS_PROVIDER", "kokoro").lower()

class TTSService:
    """Unified Text-to-Speech service supporting multiple providers."""
    
    def __init__(self):
        """Initialize TTS service."""
        self.default_provider = DEFAULT_TTS_PROVIDER
        logger.info(f"TTS Service initialized with default provider: {self.default_provider}")
    
    async def generate_speech_kokoro(
        self,
        text: str,
        voice: str = "af_alloy",
        volume_multiplier: float = 1.0,
        normalization_options: Optional[Dict] = None,
        return_timestamps: bool = False,
        lang_code: Optional[str] = None,
        response_format: str = "wav"
    ) -> bytes:
        """
        Generate speech using internal Kokoro ONNX TTS.
        
        Args:
            text: Text to convert to speech
            voice: Kokoro voice name
            
        Returns:
            Audio data bytes
            
        Raises:
            ValueError: If TTS generation fails
        """
        try:
            logger.info(f"Generating speech with internal Kokoro TTS using voice: {voice}")
            
            # Initialize Kokoro service if needed
            if not kokoro_tts_service.is_available():
                logger.info("Initializing Kokoro TTS service...")
                success = await kokoro_tts_service.initialize_model()
                if not success:
                    raise ValueError("Failed to initialize Kokoro TTS service")
            
            # Generate speech using internal service with advanced features
            audio_file_path = await kokoro_tts_service.generate_speech(
                text=text,
                voice=voice,
                response_format=response_format,
                volume_multiplier=volume_multiplier,
                normalization_options=normalization_options,
                return_timestamps=return_timestamps,
                language=lang_code or DEFAULT_LANGUAGE
            )
            
            # Read the generated audio file
            try:
                with open(audio_file_path, 'rb') as f:
                    audio_data = f.read()
                
                # Clean up the temporary file
                try:
                    os.unlink(audio_file_path)
                except OSError:
                    pass
                
                logger.info(f"Generated {len(audio_data)} bytes of audio data with internal Kokoro")
                return audio_data
                
            except Exception as read_error:
                # Clean up on error
                if os.path.exists(audio_file_path):
                    os.unlink(audio_file_path)
                raise ValueError(f"Failed to read Kokoro TTS output: {read_error}")
                    
        except Exception as e:
            logger.error(f"Error generating speech with internal Kokoro: {e}")
            raise ValueError(f"Failed to generate speech with Kokoro: {e}")
    
    async def generate_speech(
        self,
        text: str,
        voice: str = "af_alloy",
        provider: Optional[str] = None,
        response_format: str = "mp3",
        speed: float = 1.0,
        volume_multiplier: float = 1.0,
        normalization_options: Optional[Dict] = None,
        return_timestamps: bool = False,
        lang_code: Optional[str] = None
    ) -> tuple[bytes, str]:
        """
        Generate speech using the specified or default TTS provider.
        
        Args:
            text: Text to convert to speech
            voice: Voice name (provider-specific)
            provider: TTS provider ("kokoro" or "edge"), defaults to configured default
            response_format: Audio format for Edge TTS (mp3, wav, etc.)
            speed: Playback speed for Edge TTS (0.25 to 4.0)
            
        Returns:
            Tuple of (audio_data_bytes, actual_provider_used)
            
        Raises:
            ValueError: If generation fails or provider is unsupported
        """
        # Use default provider if not specified
        if not provider:
            provider = self.default_provider
        
        provider = provider.lower()
        logger.info(f"Generating speech using provider: {provider}")
        
        if provider == "kokoro":
            try:
                audio_data = await self.generate_speech_kokoro(
                    text=text,
                    voice=voice,
                    volume_multiplier=volume_multiplier,
                    normalization_options=normalization_options,
                    return_timestamps=return_timestamps,
                    lang_code=lang_code,
                    response_format=response_format
                )
                return audio_data, "kokoro"
            except Exception as e:
                logger.warning(f"Kokoro TTS failed: {e}. Falling back to Edge TTS")
                # Fallback to Edge TTS with equivalent voice
                fallback_voice = self._get_edge_fallback_voice(voice)
                audio_file_path = await edge_tts_service.generate_speech(
                    text=text,
                    voice=fallback_voice,
                    response_format="mp3",
                    speed=1.0
                )
                
                # Read the generated audio file
                try:
                    with open(audio_file_path, 'rb') as f:
                        audio_data = f.read()
                    
                    # Clean up the temporary file
                    try:
                        os.unlink(audio_file_path)
                    except OSError:
                        pass
                    
                    return audio_data, "edge_fallback"
                    
                except Exception as edge_error:
                    logger.error(f"Edge TTS fallback also failed: {edge_error}")
                    raise ValueError(f"Both Kokoro and Edge TTS failed: Kokoro={e}, Edge={edge_error}")
            
        elif provider == "edge":
            # Generate speech using Edge TTS
            audio_file_path = await edge_tts_service.generate_speech(
                text=text,
                voice=voice,
                response_format=response_format,
                speed=speed
            )
            
            # Read the generated audio file
            try:
                with open(audio_file_path, 'rb') as f:
                    audio_data = f.read()
                
                # Clean up the temporary file
                os.unlink(audio_file_path)
                
                logger.info(f"Generated {len(audio_data)} bytes of audio data using Edge TTS")
                return audio_data, "edge"
                
            except Exception as e:
                # Clean up on error
                if os.path.exists(audio_file_path):
                    os.unlink(audio_file_path)
                raise ValueError(f"Failed to read Edge TTS output: {e}")
        
        else:
            raise ValueError(f"Unsupported TTS provider: {provider}. Supported providers: kokoro, edge")
    
    async def generate_speech_stream(
        self,
        text: str,
        voice: str = "af_alloy",
        provider: Optional[str] = None,
        speed: float = 1.0,
        remove_filter: bool = False
    ):
        """
        Generate streaming speech using the specified TTS provider.
        
        Args:
            text: Text to convert to speech
            voice: Voice name (provider-specific)
            provider: TTS provider ("edge" only for streaming)
            speed: Playback speed
            remove_filter: Skip text preprocessing
            
        Yields:
            Audio chunk bytes
            
        Raises:
            ValueError: If provider doesn't support streaming or generation fails
        """
        # Use default provider if not specified
        if not provider:
            provider = self.default_provider
        
        provider = provider.lower()
        logger.info(f"Generating streaming speech using provider: {provider}")
        
        if provider == "edge":
            try:
                # Use Edge TTS streaming
                async for chunk in edge_tts_service.generate_speech_stream(
                    text=text,
                    voice=voice,
                    speed=speed,
                    remove_filter=remove_filter
                ):
                    yield chunk
            except Exception as e:
                logger.error(f"Edge TTS streaming failed: {e}")
                raise ValueError(f"Failed to generate streaming speech with Edge TTS: {e}")
        
        elif provider == "kokoro":
            # Kokoro doesn't support streaming yet
            raise ValueError("Kokoro TTS does not support streaming. Use regular generation instead.")
        
        else:
            raise ValueError(f"Unsupported TTS provider for streaming: {provider}. Only 'edge' supports streaming.")
    
    def _get_edge_fallback_voice(self, kokoro_voice: str) -> str:
        """Map Kokoro voice names to equivalent Edge TTS voices."""
        voice_mapping = {
            # Kokoro voices -> Edge TTS equivalents
            "af_alloy": "en-US-AriaNeural",
            "af_heart": "en-US-JennyNeural", 
            "af_sky": "en-US-GuyNeural",
            "af_nova": "en-US-SaraNeural",
            "af_shimmer": "en-US-AmberNeural",
            "af_echo": "en-US-BrandonNeural",
            "af_fable": "en-US-ChristopherNeural",
            "af_onyx": "en-US-EricNeural",
            # Add more mappings as needed
        }
        
        # Return mapped voice or default if not found
        return voice_mapping.get(kokoro_voice, "en-US-AriaNeural")
    
    async def process_text_to_speech(self, job_id: str, params: dict) -> dict:
        """
        Process text to speech conversion as a job with advanced features.
        
        Args:
            job_id: Unique job identifier
            params: Job parameters
                - text: Text to convert to speech
                - voice: Voice name (supports combinations for Kokoro)
                - provider: TTS provider ("kokoro" or "edge") [optional]
                - response_format: Audio format [optional, default: mp3]
                - speed: Playback speed [optional, default: 1.0]
                - volume_multiplier: Volume adjustment [Kokoro only, optional]
                - normalization_options: Text processing options [Kokoro only, optional]
                - return_timestamps: Word-level timestamps [Kokoro only, optional]
                - lang_code: Language override [Kokoro only, optional]
                
        Returns:
            Dictionary with the result including advanced features
        """
        text = params.get("text")
        voice = params.get("voice", "af_heart")
        provider = params.get("provider")
        response_format = params.get("response_format", "mp3")
        speed = params.get("speed", 1.0)
        
        # Advanced Kokoro features
        volume_multiplier = params.get("volume_multiplier", 1.0)
        normalization_options = params.get("normalization_options")
        return_timestamps = params.get("return_timestamps", False)
        lang_code = params.get("lang_code")
        
        if not text:
            raise ValueError("Text parameter is required")
        
        # Track created files for cleanup
        created_files = []
        audio_url = None
        
        try:
            # Generate speech using the specified provider with advanced features
            audio_data, actual_provider = await self.generate_speech(
                text=text,
                voice=voice,
                provider=provider,
                response_format=response_format,
                speed=speed,
                volume_multiplier=volume_multiplier,
                normalization_options=normalization_options,
                return_timestamps=return_timestamps,
                lang_code=lang_code
            )
            
            # Generate a unique output filename with appropriate extension
            file_extension = response_format.lower()
            if file_extension == "pcm":
                file_extension = "wav"  # PCM is typically in WAV container
            
            audio_filename = f"{uuid.uuid4()}.{file_extension}"
            audio_output_path = f"temp/output/{audio_filename}"
            
            # Make sure output directory exists
            os.makedirs(os.path.dirname(audio_output_path), exist_ok=True)
            
            # Validate audio data before saving
            if not audio_data or len(audio_data) < 100:  # Less than 100 bytes
                raise ValueError(f"Generated audio data too small: {len(audio_data) if audio_data else 0} bytes")
            
            # Save the audio data to file
            with open(audio_output_path, 'wb') as f:
                f.write(audio_data)
            
            created_files.append(audio_output_path)
            
            # Validate saved file size
            file_size = os.path.getsize(audio_output_path)
            if file_size < 100:
                raise ValueError(f"Generated audio file too small: {file_size} bytes")
            
            logger.info(f"Saved audio data to {audio_output_path} ({file_size} bytes)")
            
            # Upload to S3
            s3_key = f"audio/{audio_filename}"
            audio_url = await s3_service.upload_file(audio_output_path, s3_key)
            
            logger.info(f"Audio file uploaded to S3 with URL: {audio_url}")

            # Remove signed URL parameters from S3 URL
            audio_url = audio_url.split("?")[0]
            
            # Estimate audio duration based on text length
            # Average speaking rate: ~2.5 words per second for most TTS engines
            word_count = len(text.split())
            estimated_duration = max(1.0, word_count / 2.5)  # Minimum 1 second
            
            logger.info(f"Generated {word_count} words → {estimated_duration:.1f}s audio")
            
            # Return the enhanced result
            result = {
                "audio_url": audio_url,
                "audio_path": s3_key,
                "tts_engine": actual_provider,
                "voice": voice,
                "response_format": response_format,
                "speed": speed,
                "estimated_duration": estimated_duration,
                "word_count": word_count
            }
            
            # Add Kokoro-specific results
            if actual_provider == "kokoro":
                result["volume_multiplier"] = volume_multiplier
                result["lang_code"] = lang_code
                if return_timestamps:
                    # TODO: Extract timestamps from Kokoro output
                    result["word_timestamps"] = []
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing text to speech: {e}")
            raise
        finally:
            # Clean up temporary files if we've successfully uploaded to S3
            if audio_url:
                logger.info("Cleaning up temporary files")
                for path in created_files:
                    if os.path.exists(path):
                        try:
                            os.remove(path)
                            logger.info(f"Removed temporary file: {path}")
                        except Exception as e:
                            logger.warning(f"Failed to remove temporary file {path}: {e}")
            else:
                logger.warning("Keeping temporary files for debugging as upload failed")
    
    async def get_available_voices(self, provider: Optional[str] = None) -> Dict[str, List[Dict[str, Any]]]:
        """
        Get available voices for the specified provider.
        
        Args:
            provider: TTS provider ("kokoro", "edge", or None for all)
            
        Returns:
            Dictionary mapping provider names to lists of voice information
        """
        voices = {}
        
        if not provider or provider.lower() == "kokoro":
            try:
                # Get Kokoro voices from internal service
                kokoro_voices = await kokoro_tts_service.get_available_voices()
                voices["kokoro"] = kokoro_voices
            except Exception as e:
                logger.error(f"Failed to get Kokoro voices from internal service: {e}")
                # Fallback to basic list
                voices["kokoro"] = [
                    {"name": "af_heart", "language": "en-US", "description": "American Female - Heart (Grade A)", "gender": "female", "grade": "A"},
                    {"name": "af_alloy", "language": "en-US", "description": "American Female - Alloy (Grade C)", "gender": "female", "grade": "C"},
                    {"name": "af_bella", "language": "en-US", "description": "American Female - Bella (Grade A-)", "gender": "female", "grade": "A-"},
                    {"name": "am_michael", "language": "en-US", "description": "American Male - Michael (Grade C+)", "gender": "male", "grade": "C+"},
                    {"name": "bf_emma", "language": "en-GB", "description": "British Female - Emma (Grade B-)", "gender": "female", "grade": "B-"},
                    {"name": "bm_george", "language": "en-GB", "description": "British Male - George (Grade C)", "gender": "male", "grade": "C"},
                ]
        
        if not provider or provider.lower() == "edge":
            try:
                # Get Edge TTS voices
                edge_voices = await edge_tts_service.get_available_voices()
                voices["edge"] = edge_voices
            except Exception as e:
                logger.error(f"Failed to get Edge TTS voices: {e}")
                voices["edge"] = []
        
        return voices
    
    def get_supported_providers(self) -> List[str]:
        """
        Get list of supported TTS providers.
        
        Returns:
            List of provider names
        """
        return ["kokoro", "edge"]
    
    def get_supported_formats(self, provider: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Get supported audio formats for each provider.
        
        Args:
            provider: Specific provider to get formats for
            
        Returns:
            Dictionary mapping provider names to supported format lists
        """
        formats = {
            "kokoro": ["wav"],  # Kokoro supports WAV
            "edge": ["mp3", "wav", "opus", "aac", "flac", "pcm"]  # Edge TTS supports multiple formats
        }
        
        if provider:
            return {provider: formats.get(provider.lower(), [])}
        return formats
    
    def get_models(self, provider: Optional[str] = None) -> Dict[str, List[Dict[str, str]]]:
        """
        Get available models for each provider.
        
        Args:
            provider: Specific provider to get models for
            
        Returns:
            Dictionary mapping provider names to model lists
        """
        models = {
            "edge": edge_tts_service.get_supported_models(),
            "kokoro": [{"id": "kokoro-v1.0", "name": "Kokoro ONNX v1.0"}]
        }
        
        if provider:
            return {provider: models.get(provider.lower(), [])}
        return models
    
    def get_models_formatted(self, provider: Optional[str] = None) -> List[Dict[str, str]]:
        """
        Get formatted list of all models.
        
        Args:
            provider: Specific provider to get models for
            
        Returns:
            List of model information
        """
        if provider and provider.lower() == "edge":
            return edge_tts_service.get_models_formatted()
        elif provider and provider.lower() == "kokoro":
            return [{"id": "kokoro-v1.0"}]
        else:
            # Return all models
            all_models = []
            all_models.extend(edge_tts_service.get_models_formatted())
            all_models.extend([{"id": "kokoro-v1.0"}])
            return all_models
    
    def get_voices_formatted(self, provider: Optional[str] = None) -> Dict[str, List[Dict[str, str]]]:
        """
        Get formatted list of voices by provider.
        
        Args:
            provider: Specific provider to get voices for
            
        Returns:
            Dictionary mapping provider names to voice lists
        """
        voices = {}
        
        if not provider or provider.lower() == "edge":
            voices["edge"] = edge_tts_service.get_voices_formatted()
        
        if not provider or provider.lower() == "kokoro":
            # Basic Kokoro voice list
            voices["kokoro"] = [
                {"id": "af_heart", "name": "American Female - Heart"},
                {"id": "af_bella", "name": "American Female - Bella"},
                {"id": "af_alloy", "name": "American Female - Alloy"},
                {"id": "am_michael", "name": "American Male - Michael"},
                {"id": "bf_emma", "name": "British Female - Emma"},
                {"id": "bm_george", "name": "British Male - George"}
            ]
        
        return voices

# Global service instance
tts_service = TTSService()