import logging
from typing import Dict, Any, List, Optional
from app.services.ai.media_generation_strategy import MediaGenerationStrategy
from app.services.video.ltx_video_service import ltx_video_service
from app.services.video.wavespeed_service import wavespeed_service

logger = logging.getLogger(__name__)


class AIVideoStrategy(MediaGenerationStrategy):
    """Strategy for generating video segments using AI video generation."""
    
    def get_strategy_name(self) -> str:
        return "AI Video Strategy (LTX-Video/WaveSpeed)"
    
    async def generate_media_segments(
        self, 
        video_queries: List[Dict], 
        orientation: str,
        params: Dict[str, Any]
    ) -> List[Optional[Dict[str, Any]]]:
        """Generate video segments using AI video generation."""
        
        ai_video_provider = params.get('ai_video_provider', 'ltx_video')
        footage_quality = params.get('footage_quality', 'high')
        
        logger.info(f"Generating AI videos using provider: {ai_video_provider}")
        
        background_videos: List[Optional[Dict[str, Any]]] = []
        
        for i, query_data in enumerate(video_queries):
            query = query_data['query']
            
            try:
                video_url = await self._generate_ai_background_video(
                    query,
                    orientation,
                    query_data['duration'],
                    footage_quality,
                    i,  # Use scene index as seed variation
                    ai_video_provider
                )
                
                if video_url:
                    background_videos.append({
                        'download_url': video_url,
                        'start_time': query_data['start_time'],
                        'end_time': query_data['end_time'],
                        'duration': query_data['duration'],
                        'query': query,
                        'provider': f"ai_generated_{ai_video_provider}"
                    })
                else:
                    logger.warning(f"Failed to generate AI video for query: {query}")
                    background_videos.append(None)
                    
            except Exception as e:
                logger.error(f"Error generating AI video for query '{query}': {e}")
                background_videos.append(None)
        
        return background_videos
    
    async def _generate_ai_background_video(
        self,
        query: str,
        orientation: str,
        duration: float,
        quality: str,
        seed_variation: int,
        provider: str = "ltx_video"
    ) -> Optional[str]:
        """Generate AI video using specified provider."""
        
        try:
            # Determine video dimensions
            if orientation == 'portrait':
                width, height = 720, 1280
            elif orientation == 'square':
                width, height = 720, 720
            else:  # landscape
                width, height = 1280, 720
            
            # Generate unique seed based on prompt and variation
            import hashlib
            seed_base = int(hashlib.md5(query.encode()).hexdigest()[:8], 16)
            seed = (seed_base + seed_variation) % (2**32 - 1)
            
            logger.info(f"Generating AI video: {width}x{height}, duration: {duration}s, seed: {seed}")
            
            if provider == "ltx_video":
                return await self._generate_ltx_video(query, width, height, duration, seed)
            elif provider == "wavespeed":
                return await self._generate_wavespeed_video(query, width, height, duration, seed)
            else:
                logger.error(f"Unknown AI video provider: {provider}")
                return None
                
        except Exception as e:
            logger.error(f"Error generating AI video for query '{query}': {e}")
            return None
    
    async def _generate_ltx_video(
        self, 
        prompt: str, 
        width: int, 
        height: int, 
        duration: float, 
        seed: int
    ) -> Optional[str]:
        """Generate video using LTX Video service."""
        try:
            # Calculate frames from duration (assuming ~15 fps)
            num_frames = min(257, max(1, int(duration * 15)))
            
            video_data = await ltx_video_service.generate_video(
                prompt=prompt,
                width=width,
                height=height,
                num_frames=num_frames,
                seed=seed
            )
            
            if video_data:
                # Upload video data to S3 and return URL
                import uuid
                import tempfile
                import os
                
                # Save video data to temporary file
                with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                    temp_file.write(video_data)
                    temp_video_path = temp_file.name
                
                try:
                    from app.services.s3 import s3_service
                    
                    # Upload to S3
                    video_filename = f"ltx_video_{uuid.uuid4()}.mp4"
                    video_url = await s3_service.upload_file(temp_video_path, f"videos/{video_filename}")
                    
                    if video_url:
                        logger.info(f"Successfully generated and uploaded LTX video for prompt: {prompt}")
                        return video_url
                    else:
                        logger.error("Failed to upload LTX video to S3")
                        return None
                        
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_video_path):
                        os.unlink(temp_video_path)
            else:
                logger.error("No video data returned from LTX Video service")
                return None
                
        except Exception as e:
            logger.error(f"Failed to generate video with LTX Video: {e}")
            return None
    
    async def _generate_wavespeed_video(
        self, 
        prompt: str, 
        width: int, 
        height: int, 
        duration: float, 
        seed: int
    ) -> Optional[str]:
        """Generate video using WaveSpeed service."""
        try:
            # WaveSpeed service returns bytes directly like LTX Video
            # Convert width/height to size format
            size = f"{width}*{height}"
            duration_int = max(5, min(8, int(duration)))  # WaveSpeed supports 5-8 seconds
            
            video_data = await wavespeed_service.text_to_video(
                prompt=prompt,
                model="wan-2.2",  # Default model
                size=size,
                duration=duration_int,
                seed=seed if seed >= 0 else -1  # WaveSpeed uses -1 for random
            )
            
            if video_data:
                # Upload video data to S3 and return URL
                import uuid
                import tempfile
                import os
                
                # Save video data to temporary file
                with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as temp_file:
                    temp_file.write(video_data)
                    temp_video_path = temp_file.name
                
                try:
                    from app.services.s3 import s3_service
                    
                    # Upload to S3
                    video_filename = f"wavespeed_video_{uuid.uuid4()}.mp4"
                    video_url = await s3_service.upload_file(temp_video_path, f"videos/{video_filename}")
                    
                    if video_url:
                        logger.info(f"Successfully generated and uploaded WaveSpeed video for prompt: {prompt}")
                        return video_url
                    else:
                        logger.error("Failed to upload WaveSpeed video to S3")
                        return None
                        
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_video_path):
                        os.unlink(temp_video_path)
            else:
                logger.error("No video data returned from WaveSpeed service")
                return None
                
        except Exception as e:
            logger.error(f"Failed to generate video with WaveSpeed: {e}")
            return None