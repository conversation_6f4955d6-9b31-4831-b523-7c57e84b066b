import os
import time
import logging
import asyncio
import uuid
import tempfile
import base64
from typing import Dict, Any, List, Set
from app.services.ai.script_generator import script_generator
from app.services.ai.image_prompt_generator import image_prompt_generator
from app.services.ai.together_ai_service import together_ai_service
from app.services.ai.flux_service import flux_service
from app.services.pollinations_service import pollinations_service
from app.services.audio.tts_service import tts_service as text_to_speech_service
from app.services.audio.music_generation import music_generation_service
from app.services.media.transcription import transcription_service
from app.services.video.add_captions import add_captions_service
from app.services.video.concatenate import concatenation_service
from app.services.video.add_audio import add_audio_service
from app.services.s3 import s3_service
from app.services.media.metadata import metadata_service
from app.utils.video.moviepy_video_composer import MoviePyVideoComposer

logger = logging.getLogger(__name__)


class AiimageToVideoPipeline:
    """
    Complete end-to-end pipeline for generating videos from scripts using AI-generated images.
    
    This service orchestrates the entire process:
    1. Generate script from topic using AI
    2. Create TTS audio from script
    3. Get actual audio duration from metadata
    4. Transcribe audio to get segments with timing
    5. Merge short segments
    6. Generate image prompts for each segment using AI
    7. Generate images using Together.ai FLUX model
    8. Upload images and create video segments
    9. Generate background music if requested
    10. Create individual video segments from images
    11. Concatenate all video segments
    12. Add narration audio and background music
    13. Add captions if requested
    """
    
    def __init__(self):
        self.moviepy_composer = MoviePyVideoComposer()
    
    async def process_aiimage_to_video(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process complete aiimage-to-video pipeline.
        
        Args:
            params: Pipeline parameters including topic, voice settings, image generation, etc.
        
        Returns:
            Dictionary containing final video URL and processing metadata
        """
        start_time = time.time()
        
        try:
            # Handle video orientation first to ensure dimensions are set
            orientation = params.get('video_orientation', 'portrait')
            
            if orientation == 'portrait':
                output_width = params.get('output_width') or 1080
                output_height = params.get('output_height') or 1920
            elif orientation == 'square':
                output_width = params.get('output_width') or 1080
                output_height = params.get('output_height') or 1080
            else:  # landscape
                output_width = params.get('output_width') or 1920
                output_height = params.get('output_height') or 1080
            
            logger.info(f"Video orientation: {orientation}, dimensions: {output_width}x{output_height}")
            
            # Step 1: Handle auto topic discovery and generate script
            logger.info("Step 1: Generating script from topic")
            
            # Handle auto-topic discovery if enabled
            topic = params.get('topic')
            if params.get('auto_topic', False) and not topic:
                logger.info("Auto-topic discovery enabled, discovering trending topic")
                from app.services.ai.topic_discovery_service import topic_discovery_service
                script_type = params.get('script_type', 'facts')
                topic_result = await topic_discovery_service.discover_topic(
                    script_type=script_type,
                    use_trending=True
                )
                topic = topic_result['topic']
                logger.info(f"Discovered topic: {topic}")
            
            if not topic:
                raise Exception("No topic provided and auto_topic is disabled")
            
            script_params = {
                'topic': topic,
                'provider': params.get('script_provider', 'auto'),
                'script_type': params.get('script_type', 'facts'),
                'max_duration': params.get('max_duration', 120),
                'target_words': self._calculate_target_words(params.get('max_duration', 120)),
                'language': params.get('language', 'en')
            }
            
            script_result = await script_generator.generate_script(script_params)
            script_text = script_result['script']
            estimated_duration = script_result['estimated_duration']
            
            logger.info(f"Generated script: {len(script_text)} characters, estimated duration: {estimated_duration}s")
            
            # Step 2: Generate TTS audio from script
            logger.info("Step 2: Generating TTS audio")
            tts_params = {
                'text': script_text,
                'voice': params.get('voice', 'af_alloy'),
                'provider': params.get('tts_provider'),
                'speed': params.get('tts_speed', 1.0),
                'response_format': 'wav',
                'language': params.get('language', 'en')
            }
            
            tts_result = await text_to_speech_service.process_text_to_speech(f"pipeline_tts_{uuid.uuid4().hex[:8]}", tts_params)
            audio_url = tts_result['audio_url']
            
            logger.info(f"Generated TTS audio: {audio_url}")
            
            # Step 3: Get actual audio duration
            logger.info("Step 3: Getting actual audio duration from metadata")
            try:
                metadata_result = await metadata_service.get_metadata(audio_url, f"pipeline_metadata_{uuid.uuid4().hex[:8]}")
                actual_duration = metadata_result.get('duration', estimated_duration)
                logger.info(f"Actual audio duration: {actual_duration}s")
            except Exception as e:
                logger.warning(f"Could not get metadata, using estimated duration: {e}")
                actual_duration = estimated_duration
            
            # Step 4: Transcribe audio to get segments with timing
            logger.info("Step 4: Transcribing audio to get segments")
            transcription_params = {
                'media_url': audio_url,
                'include_text': True,
                'word_timestamps': True,
                'include_segments': True,
                'max_words_per_line': 8,  # Shorter segments for better visuals
                'language': params.get('language', 'en')
            }
            
            transcription_result = await transcription_service.process_media_transcription(f"pipeline_transcription_{uuid.uuid4().hex[:8]}", transcription_params)
            segments = transcription_result.get('segments', [])
            
            if not segments:
                raise Exception("No segments found in transcription")
            
            # Check if transcription covers the full audio duration
            last_segment_end = max(seg.get('end', 0) for seg in segments) if segments else 0
            coverage_ratio = last_segment_end / actual_duration if actual_duration > 0 else 0
            
            logger.info(f"Found {len(segments)} segments from transcription")
            logger.info(f"Transcription covers {last_segment_end:.2f}s of {actual_duration:.2f}s audio ({coverage_ratio:.1%} coverage)")
            
            # Log transcription coverage but don't force extension to prevent timing issues
            if coverage_ratio < 0.95 and segments:  # Less than 95% coverage
                gap_duration = actual_duration - last_segment_end
                logger.info(f"Transcription covers {coverage_ratio:.1%} of audio. Gap of {gap_duration:.2f}s will be handled naturally.")
                logger.info("Not extending last segment to prevent extra content generation and timing mismatches.")
            
            # Step 5: Use segments directly from transcription to preserve natural speech flow
            logger.info(f"Step 5: Using {len(segments)} segments directly from transcription")
            # Respect transcription timing - each segment will get its own image/video
            
            # Step 6: Generate image prompts for transcription segments
            logger.info("Step 6: Generating image prompts for segments")
            if not image_prompt_generator.is_available():
                raise Exception("Image prompt generator not available (OpenAI API key required)")
            
            image_prompts = await image_prompt_generator.generate_multiple_image_prompts(
                segments=segments,
                full_script=script_text
            )
            
            logger.info(f"Generated {len(image_prompts)} image prompts")
            
            # Step 7: Generate images with selected provider
            image_provider = params.get('image_provider', 'together')
            logger.info(f"Step 7: Generating images with {image_provider}")
            
            image_width = params.get('image_width') or 1024  # Default width
            image_height = params.get('image_height') or 1024  # Default height
            image_steps = params.get('image_steps') or 4  # Default steps
            
            if image_provider == 'together':
                if not together_ai_service.is_available():
                    raise Exception("Together.ai service not available (API key required)")
                
                model = params.get('image_model') or 'black-forest-labs/FLUX.1-schnell'  # Default model
                
                generated_images = await together_ai_service.generate_multiple_images(
                    prompts=image_prompts,
                    model=model,
                    width=image_width,
                    height=image_height,
                    steps=image_steps
                    # max_concurrent is now configured via environment variables
                )
            elif image_provider == 'flux':
                if not flux_service.is_available():
                    raise Exception("Flux service not available (API key or URL required)")
                
                # Flux uses different parameters
                guidance_scale = params.get('guidance_scale', 3.5)
                num_inference_steps = image_steps  # Use the same parameter
                
                generated_images = await flux_service.generate_multiple_images(
                    prompts=image_prompts,
                    width=image_width,
                    height=image_height,
                    guidance_scale=guidance_scale,
                    num_inference_steps=num_inference_steps
                )
            elif image_provider == 'pollinations':
                # Use Pollinations.AI for image generation
                logger.info("Using Pollinations.AI for image generation")
                
                generated_images = []
                for i, prompt in enumerate(image_prompts):
                    try:
                        # Generate image bytes using Pollinations
                        image_bytes = await pollinations_service.generate_image(
                            prompt=prompt,
                            width=params.get('image_width', 1024),
                            height=params.get('image_height', 1024),
                            model='flux',  # Use flux model on Pollinations
                            enhance=True,
                            seed=None
                        )
                        
                        if image_bytes:
                            # Upload image bytes to S3
                            filename = f"pollinations-image-{uuid.uuid4()}.jpg"
                            s3_url = await s3_service.upload_bytes(
                                file_bytes=image_bytes,
                                filename=filename,
                                content_type="image/jpeg"
                            )
                            
                            generated_images.append({
                                'url': s3_url,
                                'prompt': prompt,
                                'index': i
                            })
                        else:
                            logger.warning(f"Failed to generate image {i+1} with Pollinations")
                    except Exception as e:
                        logger.error(f"Error generating image {i+1} with Pollinations: {e}")
                        continue
            else:
                raise Exception(f"Unsupported image provider: {image_provider}. Supported providers: 'together', 'flux', 'pollinations'")
            
            if len(generated_images) == 0:
                raise Exception("Failed to generate any images")
            
            logger.info(f"Successfully generated {len(generated_images)} images")
            
            # Step 8: Upload images to S3 and create video segments
            logger.info("Step 8: Uploading images and creating video segments")
            image_urls = []
            # Initialize video_segments_data as a dictionary to maintain segment order
            video_segments_dict = {}
            
            for i, img_result in enumerate(generated_images):
                if not img_result.get('success'):
                    logger.warning(f"Skipping failed image {i}")
                    continue
                
                # Get the original segment index from the image result
                segment_index = img_result.get('index')
                if segment_index is None:
                    logger.error(f"Image result {i} missing 'index' field, skipping")
                    continue
                
                # Validate segment index is within bounds
                if segment_index >= len(segments):
                    logger.error(f"Image result {i} has invalid segment_index {segment_index} (max: {len(segments)-1}), skipping")
                    continue
                
                try:
                    # Extract base64 image data
                    img_data = img_result['data']['data'][0]['b64_json']
                    image_bytes = base64.b64decode(img_data)
                    
                    # Create temporary file for upload
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                        temp_file.write(image_bytes)
                        temp_file_path = temp_file.name
                    
                    # Upload to S3
                    s3_path = f"aiimage-to-video/{uuid.uuid4()}/image_{segment_index}.png"
                    image_url = await s3_service.upload_file(
                        file_path=temp_file_path,
                        object_name=s3_path,
                        content_type="image/png"
                    )
                    
                    # Clean up temporary file
                    os.unlink(temp_file_path)
                    
                    image_urls.append(image_url)
                    
                    # Get the corresponding segment data
                    segment = segments[segment_index]
                    video_segments_dict[segment_index] = {
                        'image_url': image_url,
                        'start_time': segment.get('start', 0),
                        'end_time': segment.get('end', 3),
                        'duration': segment.get('duration', 3.0),  # Use duration directly from transcription
                        'text': segment.get('text', ''),
                        'prompt': img_result.get('prompt', ''),
                        'segment_index': segment_index  # Track original segment index
                    }
                    
                    logger.info(f"Successfully processed image for segment {segment_index}: {segment.get('text', '')[:50]}...")
                    
                except Exception as e:
                    logger.error(f"Failed to process image for segment {segment_index}: {str(e)}")
                    continue
            
            # Convert dictionary to ordered list based on segment indices
            video_segments_data = [video_segments_dict[i] for i in sorted(video_segments_dict.keys())]
            
            if not video_segments_data:
                raise Exception("No video segments could be created")
            
            # Validate that we have segments for all expected segments
            expected_segments = len(segments)
            actual_segments = len(video_segments_data)
            
            if actual_segments != expected_segments:
                logger.warning(f"Segment count mismatch: Expected {expected_segments} segments but created {actual_segments}")
                logger.warning(f"This may cause timing issues. Missing segments: {expected_segments - actual_segments}")
            
            logger.info(f"Created {len(video_segments_data)} video segments from {len(segments)} transcription segments")
            
            # Step 9: Process background music if requested
            background_music_url = None
            music_prompt_generated = None
            
            # Check if we should add background music (AI generated or mood-based)
            should_add_music = (
                params.get('generate_background_music', False) or 
                params.get('background_music')
            )
            
            if should_add_music:
                logger.info("Step 9: Processing background music")
                try:
                    # If mood is specified, use mood-based music; otherwise use AI generation
                    if params.get('background_music') and params.get('background_music') != 'ai_generate':
                        # Use mood-based background music (similar to footage_to_video_pipeline)
                        logger.info(f"Using mood-based background music: {params['background_music']}")
                        audio_with_music_url = await self._add_background_music(
                            audio_url,
                            'mood',  # Use mood-based selection
                            params.get('background_music', 'chill'),
                            params.get('background_music_volume', 0.3),
                            actual_duration
                        )
                        audio_url = audio_with_music_url  # Update audio_url for final video
                        
                    elif params.get('generate_background_music', False):
                        # Generate AI music
                        logger.info("Generating AI background music")
                        # Generate music prompt if not provided
                        if not params.get('music_prompt'):
                            music_prompt_generated = await image_prompt_generator.generate_music_prompt(
                                script_text=script_text,
                                script_type=params.get('script_type', 'facts'),
                                video_duration=actual_duration
                            )
                        else:
                            music_prompt_generated = params['music_prompt']
                        
                        music_params = {
                            'description': music_prompt_generated,
                            'duration': params.get('music_duration') or 10,  # Default to 10s for faster generation
                            'model_size': 'small',  # Use small model for faster generation
                            'output_format': 'wav'
                        }
                        
                        music_result = await music_generation_service.process_music_generation(f"pipeline_music_{uuid.uuid4().hex[:8]}", music_params)
                        background_music_url = music_result.get('audio_url')
                        
                        logger.info(f"Generated background music: {background_music_url}")
                        
                except Exception as e:
                    logger.warning(f"Background music processing failed, continuing without: {e}")
            
            # Step 10: Use continuous composition like footage-to-video pipeline
            logger.info("Step 10: Creating continuous video composition from images")
            
            # Convert video_segments_data to the format expected by MoviePy composer
            video_segments_for_composition = []
            for i, segment_data in enumerate(video_segments_data):
                # Create individual video segment first
                segment_duration = segment_data.get('duration', 3.0)
                if segment_duration <= 0:
                    segment_duration = 3.0
                
                logger.info(f"Creating image video segment {i+1}/{len(video_segments_data)}: {segment_duration:.2f}s")
                
                video_params = {
                    'image_url': segment_data['image_url'],
                    'video_length': segment_duration,
                    'frame_rate': params.get('frame_rate', 30),
                    'effect_type': params.get('effect_type', 'zoom'),
                    'zoom_speed': params.get('zoom_speed', 25),
                    'output_width': output_width,
                    'output_height': output_height,
                    'narrator_speech_text': segment_data.get('text', ''),
                    'add_captions': False
                }
                
                # Generate video segment
                segment_uuid = str(uuid.uuid4())
                segment_job_id = f"segment_{i}_{segment_uuid[:8]}"
                
                video_result = await self.moviepy_composer.create_video_from_image(
                    job_id=segment_job_id,
                    params=video_params
                )
                
                # Add to composition list with timing info
                video_segments_for_composition.append({
                    'download_url': video_result['video_url'],
                    'start_time': segment_data.get('start_time', 0),
                    'end_time': segment_data.get('end_time', segment_duration),
                    'duration': segment_duration,  # Actual duration from merged transcription segments
                    'query': segment_data.get('text', '')[:50] + '...'
                })
                
                logger.info(f"✅ Created segment {i+1}: {video_result['video_url']}")
            
            # Step 11: Use MoviePy continuous composition like footage-to-video
            logger.info(f"Step 11: Composing {len(video_segments_for_composition)} segments with continuous timing")
            
            # Use the same composition method as footage-to-video pipeline
            video_with_visuals_url = await self.moviepy_composer.compose_timed_videos(
                video_segments=video_segments_for_composition,
                target_duration=actual_duration,  # Use actual audio duration
                output_width=output_width,
                output_height=output_height,
                frame_rate=params.get('frame_rate', 30)
            )
            
            logger.info(f"Successfully composed continuous video: {video_with_visuals_url}")
            
            # Step 12: Add narration audio to video
            logger.info("Step 12: Adding narration audio to video")
            add_audio_params = {
                'video_url': video_with_visuals_url,
                'audio_url': audio_url,
                'video_volume': 0,  # Mute original video audio
                'audio_volume': 100,  # Full narration volume
                'sync_mode': 'replace',
                'match_length': 'video'  # Match video length (now synchronized with audio)
            }
            
            add_audio_result = await add_audio_service.process_job(f"pipeline_audio_{uuid.uuid4().hex[:8]}", add_audio_params)
            video_with_audio_url = add_audio_result['url']
            
            logger.info(f"Video with narration: {video_with_audio_url}")
            
            # Step 12: Add background music if available
            final_video_url = video_with_audio_url
            
            if background_music_url:
                logger.info("Step 12: Adding background music (continued)")
                try:
                    bg_music_params = {
                        'video_url': video_with_audio_url,
                        'audio_url': background_music_url,
                        'video_volume': 100,  # Keep narration
                        'audio_volume': int(params.get('background_music_volume', 0.2) * 100),
                        'sync_mode': 'mix',
                        'match_length': 'video'
                    }
                    
                    bg_music_result = await add_audio_service.process_job(f"pipeline_bg_music_{uuid.uuid4().hex[:8]}", bg_music_params)
                    final_video_url = bg_music_result['url']
                    
                    logger.info(f"Video with background music: {final_video_url}")
                    
                except Exception as e:
                    logger.warning(f"Failed to add background music, using video without: {e}")
            
            # Step 13: Add captions if requested
            srt_url = None
            
            if params.get('add_captions', True):
                logger.info("Step 13: Adding captions")
                try:
                    # Use the original script_text directly for captions (consistent with footage-to-video)
                    caption_style = params.get('caption_style', 'viral_bounce')
                    
                    # Get responsive caption properties based on video dimensions
                    responsive_properties = params.get('caption_properties') or self._get_responsive_caption_properties(caption_style, output_width, output_height)
                    
                    # Ensure the style is set in the properties
                    responsive_properties["style"] = caption_style
                    
                    caption_params = {
                        'video_url': final_video_url,
                        'captions': script_text,  # Use original script_text directly like footage-to-video
                        'caption_properties': responsive_properties,  # Use same structure as footage-to-video
                        'language': params.get('language', 'auto')
                    }
                    
                    caption_result = await add_captions_service.process_job(f"pipeline_captions_{uuid.uuid4().hex[:8]}", caption_params)
                    final_video_url = caption_result['url']
                    srt_url = caption_result.get('srt_url')
                    
                    logger.info(f"Final video with captions: {final_video_url}")
                    
                except Exception as e:
                    logger.warning(f"Failed to add captions, using video without: {e}")
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Prepare final result
            result = {
                'final_video_url': final_video_url,
                'video_with_audio_url': video_with_audio_url,
                'title': script_result.get('title', 'Generated Video'),
                'description': script_result.get('description', 'AI-generated content'),
                'script_generated': script_text,
                'audio_url': audio_url,
                'generated_images': [
                    {
                        'url': img_url,
                        'prompt': video_segments_data[i].get('prompt', '') if i < len(video_segments_data) else '',
                        'index': i
                    }
                    for i, img_url in enumerate(image_urls)
                ],
                'background_music_url': background_music_url,
                'music_prompt_generated': music_prompt_generated,
                'srt_url': srt_url,
                'video_duration': actual_duration,
                'processing_time': processing_time,
                'word_count': len(script_text.split()),
                'segments_count': len(video_segments_data),
                'segments_data': video_segments_data
            }
            
            logger.info(f"Script-to-video pipeline completed in {processing_time:.2f}s")
            
            return result
            
        except Exception as e:
            logger.error(f"Script-to-video pipeline failed: {str(e)}")
            raise Exception(f"Pipeline failed: {str(e)}")
    
    def _calculate_target_words(self, max_duration: int) -> int:
        """Calculate target word count based on duration."""
        # Assume ~150 words per minute for natural speech
        words_per_minute = 150
        target_words = int((max_duration / 60) * words_per_minute)
        return max(target_words, 50)  # Minimum 50 words
    
    def _get_responsive_caption_properties(self, style: str, width: int, height: int) -> Dict:
        """Get responsive caption properties based on style and video dimensions."""
        # Calculate responsive values based on video dimensions
        is_portrait = height > width
        is_square = abs(width - height) < 100
        
        # Smaller base font size calculation for better mobile experience
        if is_portrait:
            base_font_size = max(28, int(height * 0.028))  # Reduced to 2.8% of height (20% smaller)
            max_words_per_line = 3  # Reduced for better sync and readability
            margin_bottom = max(100, int(height * 0.08))   # Increased margin for mobile safe area
        elif is_square:
            base_font_size = max(24, int(height * 0.024))  # Reduced to 2.4% of height (20% smaller)
            max_words_per_line = 3
            margin_bottom = max(60, int(height * 0.04))    # 4% of height
        else:  # landscape
            base_font_size = max(42, int(height * 0.045))  # Increased to 4.5% of height for better readability
            max_words_per_line = 4
            margin_bottom = max(50, int(height * 0.035))   # 3.5% of height
        
        # Outline width relative to font size
        outline_width = max(2, int(base_font_size * 0.1))  # 10% of font size for better visibility
        
        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': base_font_size,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.3 if is_portrait else 1.2,  # Optimized spacing
                'margin_bottom': margin_bottom,
                'text_align': 'center',
                'word_wrap': True,
                'max_chars_per_line': 30 if is_portrait else 45  # Reduced for better mobile fit
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'animation_speed': 1.0,
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.2,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            },
            'classic': {
                'style': 'classic',
                'font_size': base_font_size,  # Removed multiplier for mobile optimization
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': outline_width,
                'position': 'bottom_center',
                'max_words_per_line': max_words_per_line,
                'line_spacing': 1.1,
                'margin_bottom': margin_bottom,
                'text_align': 'center'
            }
        }
        
        return style_presets.get(style, style_presets['classic'])

    def _get_default_caption_properties(self, style: str) -> Dict:
        """Get default caption properties based on style."""
        style_presets = {
            'viral_bounce': {
                'style': 'viral_bounce',
                'font_size': 60,
                'line_color': 'white',
                'word_color': 'yellow',
                'outline_color': 'black',
                'outline_width': 3,
                'position': 'center',
                'all_caps': True,
                'bounce_intensity': 1.5,
                'animation_speed': 1.2
            },
            'typewriter': {
                'style': 'typewriter',
                'font_size': 42,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 3,
                'position': 'bottom_center',
                'typewriter_speed': 3.0,
                'max_words_per_line': 4,
                'line_spacing': 1.2,
                'margin_bottom': 80,
                'text_align': 'center'
            },
            'fade_in': {
                'style': 'fade_in',
                'font_size': 52,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 2,
                'position': 'bottom_center',
                'animation_speed': 1.0
            },
            'classic': {
                'style': 'classic',
                'font_size': 48,
                'line_color': 'white',
                'outline_color': 'black',
                'outline_width': 2,
                'position': 'bottom_center'
            }
        }
        
        return style_presets.get(style, style_presets['classic'])

    async def _add_background_music(
        self, 
        voice_audio_url: str, 
        background_music: str, 
        mood: str, 
        volume: float, 
        duration: float
    ) -> str:
        """
        Add background music to voice audio.
        
        Args:
            voice_audio_url: URL of the voice audio
            background_music: Either 'mood' for mood-based music or 'generate' for AI music
            mood: Music mood for selection/generation
            volume: Background music volume (0.0 to 1.0)
            duration: Target duration for the audio
            
        Returns:
            URL of the final audio with background music
        """
        try:
            import tempfile
            import uuid
            import subprocess
            import os
            from app.services.music_service import MusicService
            music_service = MusicService()
            
            # Download voice audio
            voice_audio_path = await self._download_audio_file(voice_audio_url)
            
            # Get background music file
            background_music_path = None
            
            if background_music == 'mood':
                # Use mood-based background music
                tracks = music_service.get_tracks_by_mood(mood)
                if tracks:
                    selected_track = tracks[0]  # Use first matching track
                    background_music_path = music_service.get_track_path(selected_track['file'])
                    logger.info(f"Using mood-based track: {selected_track['title']} for mood: {mood}")
                else:
                    logger.warning(f"No tracks found for mood: {mood}, skipping background music")
                    return voice_audio_url
            else:
                logger.warning(f"Unsupported background music type: {background_music}")
                return voice_audio_url
            
            if not background_music_path or not os.path.exists(background_music_path):
                logger.warning("Background music file not accessible, skipping")
                return voice_audio_url
            
            # Create output path
            output_path = f"/tmp/audio_with_music_{uuid.uuid4()}.mp3"
            
            # Mix audio using FFmpeg
            cmd = [
                'ffmpeg', '-y',
                '-i', voice_audio_path,
                '-i', background_music_path,
                '-filter_complex', 
                f'[0:a]volume=1.0[voice];[1:a]volume={volume}[music];[voice][music]amix=inputs=2:duration=first:dropout_transition=2',
                '-t', str(duration),
                '-c:a', 'libmp3lame',
                '-b:a', '192k',
                output_path
            ]
            
            logger.info(f"Mixing audio with background music: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"FFmpeg audio mixing failed: {result.stderr}")
                return voice_audio_url
            
            # Upload mixed audio to S3
            mixed_audio_url = await s3_service.upload_file(
                output_path, 
                f"audio/mixed_audio_{uuid.uuid4()}.mp3"
            )
            
            # Clean up local files
            os.unlink(voice_audio_path)
            os.unlink(output_path)
            
            return mixed_audio_url
            
        except Exception as e:
            logger.error(f"Failed to add background music: {str(e)}")
            return voice_audio_url

    async def _download_audio_file(self, audio_url: str) -> str:
        """Download audio file from URL to local temp file."""
        import tempfile
        import requests
        import uuid
        
        # Create temp file
        temp_path = f"/tmp/audio_{uuid.uuid4()}.mp3"
        
        # Download file
        response = requests.get(audio_url)
        response.raise_for_status()
        
        with open(temp_path, 'wb') as f:
            f.write(response.content)
        
        return temp_path



# Global instance
aiimage_to_video_pipeline = AiimageToVideoPipeline()