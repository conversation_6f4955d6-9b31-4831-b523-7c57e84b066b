"""
News research service using Google Search and Perplexity APIs for daily news content.
"""
import os
import logging
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from app.utils.ai_context import get_current_context

logger = logging.getLogger(__name__)


class NewsResearchService:
    """Service for researching current news and events."""
    
    def __init__(self):
        self.google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
        self.google_search_engine_id = os.getenv('GOOGLE_SEARCH_ENGINE_ID')
        self.perplexity_api_key = os.getenv('PERPLEXITY_API_KEY')
        
        # Validate at least one service is available
        if not (self.google_api_key and self.google_search_engine_id) and not self.perplexity_api_key:
            logger.warning("No news research APIs configured. Google Search or Perplexity API required for daily news.")
    
    async def research_topic(self, topic: str, max_results: int = 5) -> Dict[str, Any]:
        """
        Research a topic using available news APIs.
        
        Args:
            topic: Topic to research
            max_results: Maximum number of news articles to fetch
            
        Returns:
            Dictionary containing research results and summary
        """
        try:
            # Try Google Search first
            if self.google_api_key and self.google_search_engine_id:
                try:
                    google_results = await self._search_google_news(topic, max_results)
                    if google_results['articles']:
                        logger.info(f"Found {len(google_results['articles'])} articles via Google Search")
                        return google_results
                except Exception as e:
                    logger.warning(f"Google Search failed: {e}")
            
            # Fallback to Perplexity
            if self.perplexity_api_key:
                try:
                    perplexity_results = await self._search_perplexity_news(topic)
                    logger.info("Used Perplexity API for news research")
                    return perplexity_results
                except Exception as e:
                    logger.warning(f"Perplexity API failed: {e}")
            
            # If both fail, return empty results
            logger.warning("All news research APIs failed, returning empty results")
            return {
                'articles': [],
                'summary': f"Recent developments about {topic}",
                'source': 'fallback'
            }
            
        except Exception as e:
            logger.error(f"News research failed: {e}")
            return {
                'articles': [],
                'summary': f"Latest updates on {topic}",
                'source': 'error'
            }
    
    async def _search_google_news(self, topic: str, max_results: int) -> Dict[str, Any]:
        """Search Google News for recent articles."""
        # Calculate date range for recent news (last 7 days)
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        date_restrict = f"d7"  # Last 7 days
        
        # Construct search query
        query = f"{topic} news"
        
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            'key': self.google_api_key,
            'cx': self.google_search_engine_id,
            'q': query,
            'num': min(max_results, 10),  # Google allows max 10 per request
            'dateRestrict': date_restrict,
            'sort': 'date',
            'siteSearch': 'news.google.com OR reuters.com OR bbc.com OR cnn.com OR apnews.com OR npr.org',
            'lr': 'lang_en'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    articles = []
                    for item in data.get('items', []):
                        articles.append({
                            'title': item.get('title', ''),
                            'snippet': item.get('snippet', ''),
                            'link': item.get('link', ''),
                            'source': item.get('displayLink', ''),
                            'date': item.get('htmlFormattedUrl', '')
                        })
                    
                    # Generate summary from articles
                    summary = self._generate_summary_from_articles(articles, topic)
                    
                    return {
                        'articles': articles,
                        'summary': summary,
                        'source': 'google_search'
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Google Search API error {response.status}: {error_text}")
    
    async def _search_perplexity_news(self, topic: str) -> Dict[str, Any]:
        """Search using Perplexity API for current news."""
        # Updated model names based on current Perplexity API
        perplexity_model = os.getenv('PERPLEXITY_MODEL', 'sonar')
        
        url = "https://api.perplexity.ai/chat/completions"
        
        # Create a comprehensive research prompt that can handle both current events and general topics
        prompt = f"""Research and explain {topic} comprehensively. If this is a current events topic, include the latest developments from the past week. If this is a general topic (like organizations, concepts, historical events, etc.), provide a thorough explanation of what it is, its background, purpose, and key information.

        For general topics: Explain what it is, how it works, its history, importance, and key facts.
        For current events: Include recent developments, key facts, current status, and specific details with dates.
        
        Always provide factual, accurate information with credible sources when available."""
        
        headers = {
            'Authorization': f'Bearer {self.perplexity_api_key}',
            'Content-Type': 'application/json'
        }
        
        payload = {
            'model': perplexity_model,
            'messages': [
                {
                    'role': 'system',
                    'content': f'You are a news researcher. Provide factual, up-to-date information about current events. {get_current_context()}'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ],
            'max_tokens': 1000,
            'temperature': 0.1,
            'stream': False
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url, headers=headers, json=payload) as response:
                if response.status == 200:
                    data = await response.json()
                    content = data['choices'][0]['message']['content']
                    
                    return {
                        'articles': [{
                            'title': f"Latest on {topic}",
                            'snippet': content[:200] + "...",
                            'content': content,
                            'source': 'perplexity',
                            'date': datetime.now().strftime('%Y-%m-%d')
                        }],
                        'summary': content,
                        'source': 'perplexity'
                    }
                else:
                    error_text = await response.text()
                    raise Exception(f"Perplexity API error {response.status}: {error_text}")
    
    def _generate_summary_from_articles(self, articles: List[Dict], topic: str) -> str:
        """Generate a summary from Google Search articles."""
        if not articles:
            return f"Recent developments about {topic}"
        
        # Combine snippets to create a summary
        snippets = [article.get('snippet', '') for article in articles if article.get('snippet')]
        
        if snippets:
            # Take first few sentences from snippets
            summary_parts = []
            for snippet in snippets[:3]:  # Use top 3 articles
                # Clean up snippet and take first sentence
                clean_snippet = snippet.replace('...', '').strip()
                if clean_snippet:
                    summary_parts.append(clean_snippet)
            
            return ' '.join(summary_parts)
        else:
            return f"Latest news and updates about {topic}"
    
    def get_news_keywords(self, topic: str) -> List[str]:
        """Generate keywords for news-related video search."""
        base_keywords = [
            "breaking news", "news report", "journalism", "news anchor",
            "newsroom", "headlines", "current events", "media coverage",
            "press conference", "news bulletin", "live news"
        ]
        
        # Add topic-specific keywords
        topic_words = topic.lower().split()
        extended_keywords = base_keywords + topic_words
        
        return extended_keywords


# Singleton instance
news_research_service = NewsResearchService()