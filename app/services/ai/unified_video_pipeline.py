import os
import time
import logging
import uuid
from typing import Dict, Any, List, Optional, Union
from app.services.ai.media_generation_strategy import MediaGenerationStrategy
from app.services.ai.script_generator import script_generator
from app.services.ai.video_search_query_generator import video_search_query_generator
from app.services.audio.tts_service import tts_service as text_to_speech_service
from app.services.video.add_captions import add_captions_service
from app.services.video.concatenate import concatenation_service
from app.services.video.add_audio import add_audio_service
from app.services.s3 import s3_service
from app.config import get_caption_style, apply_caption_style_preset, get_available_caption_styles
from app.services.media.metadata import metadata_service
from app.utils.video.background_video_composer import BackgroundVideoComposer
from app.services.music_service import music_service
from app.utils.video.moviepy_video_composer import MoviePyVideoComposer
from app.services.ai.topic_discovery_service import topic_discovery_service

logger = logging.getLogger(__name__)


class UnifiedVideoPipeline:
    """
    Unified pipeline for generating videos from topics using different media sources.
    
    This service orchestrates the entire process:
    1. Generate script from topic using AI
    2. Create TTS audio from script
    3. Generate media search queries from script
    4. Use appropriate strategy to generate media (stock videos, stock images, AI images, AI videos)
    5. Compose videos with timing
    6. Add captions if requested
    7. Final rendering and upload to S3
    
    Supports multiple media generation strategies:
    - Stock videos from Pexels/Pixabay
    - Stock images from Pexels/Pixabay with motion effects
    - AI-generated images from Together.ai/Flux with motion effects
    - AI-generated videos from LTX-Video/WaveSpeed
    """
    
    def __init__(self):
        self.video_composer = BackgroundVideoComposer()  # FFmpeg fallback composer
        self.moviepy_composer = MoviePyVideoComposer()   # Primary MoviePy composer
        
        # Initialize media generation strategies
        self.media_strategies: Dict[str, MediaGenerationStrategy] = {}
        self._register_strategies()
    
    def _register_strategies(self):
        """Register all available media generation strategies."""
        from app.services.ai.strategies.stock_video_strategy import StockVideoStrategy
        from app.services.ai.strategies.stock_image_strategy import StockImageStrategy
        from app.services.ai.strategies.ai_image_strategy import AIImageStrategy
        from app.services.ai.strategies.ai_video_strategy import AIVideoStrategy
        
        self.media_strategies = {
            'stock_video': StockVideoStrategy(),
            'stock_image': StockImageStrategy(),
            'ai_image': AIImageStrategy(),
            'ai_video': AIVideoStrategy()
        }
        
        logger.info(f"Registered {len(self.media_strategies)} media generation strategies")
    
    def _determine_media_strategy(self, params: Dict[str, Any]) -> str:
        """
        Determine which media generation strategy to use based on parameters.
        
        Args:
            params: Pipeline parameters
            
        Returns:
            Strategy key to use
        """
        footage_provider = params.get('footage_provider', 'pexels')
        media_type = params.get('media_type', 'video')
        
        # Map provider + media_type to strategy
        if footage_provider == 'ai_generated':
            if media_type == 'image':
                return 'ai_image'
            else:  # video
                return 'ai_video'
        else:  # pexels, pixabay
            if media_type == 'image':
                return 'stock_image'
            else:  # video
                return 'stock_video'
    
    async def process_unified_video_generation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process complete unified video generation pipeline.
        
        Args:
            params: Pipeline parameters including topic, voice settings, caption options, etc.
        
        Returns:
            Dictionary containing final video URL and processing metadata
        """
        start_time = time.time()
        
        # Log the media type and provider being used
        media_type = params.get('media_type', 'video')
        footage_provider = params.get('footage_provider', 'pexels')
        strategy_key = self._determine_media_strategy(params)
        
        logger.info(f"Processing unified video generation with media_type: '{media_type}', footage_provider: '{footage_provider}', strategy: '{strategy_key}'")
        
        try:
            # Handle video orientation first to ensure dimensions are set
            orientation = params.get('video_orientation', 'landscape')
            
            if orientation == 'portrait':
                params.setdefault('width', 720)
                params.setdefault('height', 1280)
            elif orientation == 'square':
                params.setdefault('width', 720)
                params.setdefault('height', 720)
            else:  # landscape
                params.setdefault('width', 1280)
                params.setdefault('height', 720)
            
            # Step 1: Handle auto-topic discovery if enabled
            if params.get('auto_topic', False) and not params.get('topic'):
                logger.info("Auto-topic discovery enabled, discovering trending topic...")
                script_type = params.get('script_type', 'facts')
                discovered_topic = await topic_discovery_service.discover_topic(script_type, use_trending=True)
                params['topic'] = discovered_topic
                logger.info(f"Discovered trending topic: {discovered_topic}")
            
            # Step 2: Generate script
            if params.get('custom_script'):
                script_content = params['custom_script']
                logger.info("Using provided custom script")
            else:
                topic = params.get('topic')
                if not topic:
                    raise ValueError("Either 'topic' must be provided or 'auto_topic' must be set to true or 'custom_script' must be provided")
                
                logger.info(f"Generating script for topic: {topic}")
                script_params = {
                    'topic': topic,
                    'script_type': params.get('script_type', 'facts'),
                    'duration': params.get('duration', 60),
                    'tone': params.get('tone', 'engaging'),
                    'target_audience': params.get('target_audience', 'general'),
                    'script_structure': params.get('script_structure', 'hook_body_cta'),
                    'hook_type': params.get('hook_type', 'question'),
                    'cta_type': params.get('cta_type', 'subscribe'),
                    'include_transitions': params.get('include_transitions', True),
                    'max_words': params.get('max_words', 150)
                }
                
                script_result = await script_generator.generate_script(script_params)
                script_content = script_result.get('script', '')
                
                if not script_content:
                    raise ValueError("Failed to generate script content")
                
                logger.info(f"Generated script ({len(script_content)} characters)")
            
            # Step 3: Generate TTS audio
            logger.info("Generating TTS audio...")
            voice_provider = params.get('voice_provider', 'kokoro')
            voice_name = params.get('voice_name', 'af_sarah')
            
            audio_result = await text_to_speech_service.generate_speech(
                text=script_content,
                voice=voice_name,
                provider=voice_provider,
                speed=params.get('voice_speed', 1.0)
            )
            
            # TTS service returns tuple (bytes, filename) not dict
            if isinstance(audio_result, tuple):
                audio_bytes, audio_filename = audio_result
                # Upload to S3 to get URL
                import tempfile
                import os as file_os
                
                with tempfile.NamedTemporaryFile(suffix='.mp3', delete=False) as temp_file:
                    temp_file.write(audio_bytes)
                    temp_audio_path = temp_file.name
                
                try:
                    audio_url = await s3_service.upload_file(temp_audio_path, f"audio/{audio_filename}")
                finally:
                    if file_os.path.exists(temp_audio_path):
                        file_os.unlink(temp_audio_path)
            else:
                audio_url = audio_result.get('audio_url') if isinstance(audio_result, dict) else None
            
            if not audio_url:
                raise ValueError("Failed to generate TTS audio")
                
            logger.info(f"Generated TTS audio: {audio_url}")
            
            # Step 4: Get audio metadata for timing
            logger.info("Getting audio metadata for timing...")
            audio_metadata = await metadata_service.get_metadata(audio_url)
            audio_duration = audio_metadata.get('duration', 60.0)
            logger.info(f"Audio duration: {audio_duration:.2f} seconds")
            
            # Step 5: Generate video search queries
            logger.info("Generating video search queries...")
            query_params = {
                'script': script_content,
                'num_queries': max(3, int(audio_duration / 3)),  # ~3 seconds per segment
                'audio_duration': audio_duration,
                'script_type': params.get('script_type', 'facts'),
                'topic': params.get('topic', ''),
                'target_segments': params.get('target_segments', 5)
            }
            
            queries_result = await video_search_query_generator.generate_video_search_queries(query_params)
            video_queries = queries_result.get('queries', [])
            
            if not video_queries:
                raise ValueError("Failed to generate video search queries")
                
            logger.info(f"Generated {len(video_queries)} video queries")
            
            # Step 6: Use appropriate strategy to generate media with fallback
            strategy = self.media_strategies.get(strategy_key)
            if not strategy:
                raise ValueError(f"Unknown media generation strategy: {strategy_key}")

            logger.info(f"Using {strategy.get_strategy_name()} for media generation")

            background_media = await strategy.generate_media_segments(
                video_queries=video_queries,
                orientation=orientation,
                params=params
            )

            # Filter out None results
            valid_media = [media for media in background_media if media is not None]

            # If no valid media generated, try fallback strategies
            if not valid_media:
                logger.warning(f"No valid media generated with {strategy.get_strategy_name()}, trying fallback strategies")
                valid_media = await self._try_fallback_strategies(
                    strategy_key, video_queries, orientation, params
                )

            if not valid_media:
                raise ValueError("No valid background media generated after trying all fallback strategies")
                
            logger.info(f"Generated {len(valid_media)} valid media segments")
            
            # Step 7: Create video composition
            logger.info("Creating video composition...")
            composition_params = {
                'background_videos': valid_media,
                'audio_url': audio_url,
                'width': params['width'],
                'height': params['height'],
                'output_format': params.get('output_format', 'mp4'),
                'frame_rate': params.get('frame_rate', 30),
                'video_codec': params.get('video_codec', 'libx264'),
                'audio_codec': params.get('audio_codec', 'aac'),
                'quality': params.get('video_quality', 'high'),
                'fade_duration': params.get('fade_duration', 0.5),
                'crossfade_duration': params.get('crossfade_duration', 0.3)
            }
            
            # Use MoviePy composer (more reliable)
            video_with_audio_path = await self.moviepy_composer.compose_timed_videos(
                video_segments=valid_media,
                target_duration=audio_duration, 
                output_width=composition_params['output_width'],
                output_height=composition_params['output_height'],
                frame_rate=composition_params.get('fps', 30)
            )
            
            if not video_with_audio_path:
                raise ValueError("Failed to create video composition")
                
            logger.info(f"Created video composition: {video_with_audio_path}")
            
            # Step 8: Add captions if requested
            final_video_path = video_with_audio_path
            srt_url = None
            
            if params.get('add_captions', False):
                logger.info("Adding captions to video...")
                
                # Get or apply caption style
                caption_style_name = params.get('caption_style', 'viral_bounce')
                caption_style = get_caption_style(caption_style_name)
                
                # Apply preset if available
                if caption_style:
                    caption_params = apply_caption_style_preset(caption_style_name, params)
                else:
                    caption_params = params
                
                # Generate simple SRT captions file
                import tempfile
                
                # Create simple SRT content - split script into timed segments
                words = script_content.split()
                words_per_segment = 5
                segment_duration = audio_duration / max(1, len(words) // words_per_segment)
                
                srt_content = ""
                for i in range(0, len(words), words_per_segment):
                    segment_words = words[i:i+words_per_segment]
                    start_time = i // words_per_segment * segment_duration
                    end_time = min(start_time + segment_duration, audio_duration)
                    
                    srt_content += f"{i//words_per_segment + 1}\n"
                    srt_content += f"{self._format_srt_time(start_time)} --> {self._format_srt_time(end_time)}\n"
                    srt_content += f"{' '.join(segment_words)}\n\n"
                
                with tempfile.NamedTemporaryFile(mode='w', suffix='.srt', delete=False) as srt_file:
                    srt_file.write(srt_content)
                    srt_path = srt_file.name
                
                try:
                    caption_properties = {
                        'font_size': caption_params.get('caption_font_size', 24),
                        'font_color': caption_params.get('caption_font_color', 'white'),
                        'font_family': caption_params.get('caption_font_family', 'Arial-Bold'),
                        'background_color': caption_params.get('caption_background_color', 'black'),
                        'background_opacity': caption_params.get('caption_background_opacity', 0.7),
                        'position': caption_params.get('caption_position', 'bottom'),
                        'margin': caption_params.get('caption_margin', 50),
                        'max_width': caption_params.get('caption_max_width', 80),
                        'line_spacing': caption_params.get('caption_line_spacing', 1.2)
                    }
                    
                    final_video_path = await add_captions_service.add_captions_to_video(
                        video_path=video_with_audio_path,
                        captions_path=srt_path,
                        caption_properties=caption_properties
                    )
                finally:
                    # Clean up temporary SRT file
                    import os as temp_os
                    if temp_os.path.exists(srt_path):
                        temp_os.unlink(srt_path)
                
                srt_url = None  # SRT file URL if saved to S3
                
                if final_video_path:
                    logger.info(f"Added captions to video: {final_video_path}")
                else:
                    logger.warning("Failed to add captions, using video without captions")
                    final_video_path = video_with_audio_path
            
            # Step 9: Upload final video to S3
            logger.info("Uploading final video to S3...")
            final_video_filename = f"final_{uuid.uuid4()}.mp4"
            final_video_url = await s3_service.upload_file(final_video_path, f"videos/{final_video_filename}")
            
            # Also upload video without captions for clean version
            video_no_captions_filename = f"no_captions_{uuid.uuid4()}.mp4"
            video_no_captions_url = await s3_service.upload_file(video_with_audio_path, f"videos/{video_no_captions_filename}")
            
            # Calculate processing time
            processing_time = time.time() - start_time
            
            # Prepare result
            result = {
                'final_video_url': final_video_url,
                'video_with_audio_url': video_no_captions_url,
                'script_generated': script_content,
                'audio_url': audio_url,
                'video_duration': audio_duration,
                'processing_time': processing_time,
                'word_count': len(script_content.split()),
                'segments_count': len(valid_media),
                'media_strategy_used': strategy.get_strategy_name(),
                'background_media_used': [media.get('download_url', 'N/A') for media in valid_media],
                'orientation': orientation,
                'dimensions': f"{params['width']}x{params['height']}"
            }
            
            # Add caption-specific results if captions were added
            if srt_url:
                result['srt_url'] = srt_url
                result['captions_added'] = True
            else:
                result['captions_added'] = False
            
            logger.info(f"Video generation completed successfully in {processing_time:.2f} seconds")
            return result
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error in unified video pipeline after {processing_time:.2f}s: {str(e)}", exc_info=True)
            raise
    
    def _format_srt_time(self, seconds: float) -> str:
        """Format time in seconds to SRT time format (HH:MM:SS,mmm)."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"


# Global instance
unified_video_pipeline = UnifiedVideoPipeline()