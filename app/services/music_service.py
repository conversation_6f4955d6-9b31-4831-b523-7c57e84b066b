"""
Music service for handling background music selection and management.
"""
import os
from typing import List, Dict, Any, Optional
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class MusicMood(str, Enum):
    """Music mood categories."""
    sad = "sad"
    melancholic = "melancholic"
    happy = "happy"
    euphoric = "euphoric"
    excited = "excited"
    chill = "chill"
    uneasy = "uneasy"
    angry = "angry"
    dark = "dark"
    hopeful = "hopeful"
    contemplative = "contemplative"
    funny = "funny"

class MusicTrack:
    """Represents a music track with metadata."""
    def __init__(self, file: str, start: int, end: int, mood: MusicMood, title: str | None = None):
        self.file = file
        self.start = start
        self.end = end
        self.mood = mood
        self.title = title or file.replace('.mp3', '')
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "file": self.file,
            "title": self.title,
            "start": self.start,
            "end": self.end,
            "mood": self.mood.value,
            "duration": self.end - self.start
        }

class MusicService:
    """Service for managing background music tracks."""
    
    def __init__(self):
        # Try multiple possible paths for music directory
        possible_paths = [
            "/app/static/music",  # Docker environment
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "app", "static", "music"),  # Local development
            os.path.join(os.getcwd(), "app", "static", "music"),  # Alternative local path
        ]
        
        self.music_dir = None
        for path in possible_paths:
            if os.path.exists(path):
                self.music_dir = path
                break
        
        if not self.music_dir:
            # Default to the first path if none exist
            self.music_dir = possible_paths[0]
        
        logger.info(f"Music service initialized with directory: {self.music_dir}")
        logger.info(f"Music directory exists: {os.path.exists(self.music_dir)}")
        if os.path.exists(self.music_dir):
            files = os.listdir(self.music_dir)
            logger.info(f"Found {len(files)} music files in directory")
        else:
            logger.warning(f"Music directory not found: {self.music_dir}")
        self.tracks = self._initialize_tracks()
    
    def _initialize_tracks(self) -> List[MusicTrack]:
        """Initialize the music track list."""
        return [
            MusicTrack("Sly Sky - Telecasted.mp3", 0, 152, MusicMood.melancholic, "Sly Sky"),
            MusicTrack("No.2 Remembering Her - Esther Abrami.mp3", 2, 134, MusicMood.melancholic, "Remembering Her"),
            MusicTrack("Champion - Telecasted.mp3", 0, 142, MusicMood.chill, "Champion"),
            MusicTrack("Oh Please - Telecasted.mp3", 0, 154, MusicMood.chill, "Oh Please"),
            MusicTrack("Jetski - Telecasted.mp3", 0, 142, MusicMood.uneasy, "Jetski"),
            MusicTrack("Phantom - Density & Time.mp3", 0, 178, MusicMood.uneasy, "Phantom"),
            MusicTrack("On The Hunt - Andrew Langdon.mp3", 0, 95, MusicMood.uneasy, "On The Hunt"),
            MusicTrack("Name The Time And Place - Telecasted.mp3", 0, 142, MusicMood.excited, "Name The Time And Place"),
            MusicTrack("Delayed Baggage - Ryan Stasik.mp3", 3, 108, MusicMood.euphoric, "Delayed Baggage"),
            MusicTrack("Like It Loud - Dyalla.mp3", 4, 160, MusicMood.euphoric, "Like It Loud"),
            MusicTrack("Organic Guitar House - Dyalla.mp3", 2, 160, MusicMood.euphoric, "Organic Guitar House"),
            MusicTrack("Honey, I Dismembered The Kids - Ezra Lipp.mp3", 2, 144, MusicMood.dark, "Honey, I Dismembered The Kids"),
            MusicTrack("Night Hunt - Jimena Contreras.mp3", 0, 88, MusicMood.dark, "Night Hunt"),
            MusicTrack("Curse of the Witches - Jimena Contreras.mp3", 0, 102, MusicMood.dark, "Curse of the Witches"),
            MusicTrack("Restless Heart - Jimena Contreras.mp3", 0, 94, MusicMood.sad, "Restless Heart"),
            MusicTrack("Heartbeat Of The Wind - Asher Fulero.mp3", 0, 124, MusicMood.sad, "Heartbeat Of The Wind"),
            MusicTrack("Hopeless - Jimena Contreras.mp3", 0, 250, MusicMood.sad, "Hopeless"),
            MusicTrack("Touch - Anno Domini Beats.mp3", 0, 165, MusicMood.happy, "Touch"),
            MusicTrack("Cafecito por la Manana - Cumbia Deli.mp3", 0, 184, MusicMood.happy, "Cafecito por la Manana"),
            MusicTrack("Aurora on the Boulevard - National Sweetheart.mp3", 0, 130, MusicMood.happy, "Aurora on the Boulevard"),
            MusicTrack("Buckle Up - Jeremy Korpas.mp3", 0, 128, MusicMood.angry, "Buckle Up"),
            MusicTrack("Twin Engines - Jeremy Korpas.mp3", 0, 120, MusicMood.angry, "Twin Engines"),
            MusicTrack("Hopeful - Nat Keefe.mp3", 0, 175, MusicMood.hopeful, "Hopeful"),
            MusicTrack("Hopeful Freedom - Asher Fulero.mp3", 1, 172, MusicMood.hopeful, "Hopeful Freedom"),
            MusicTrack("Crystaline - Quincas Moreira.mp3", 0, 140, MusicMood.contemplative, "Crystaline"),
            MusicTrack("Final Soliloquy - Asher Fulero.mp3", 1, 178, MusicMood.contemplative, "Final Soliloquy"),
            MusicTrack("Seagull - Telecasted.mp3", 0, 123, MusicMood.funny, "Seagull"),
            MusicTrack("Banjo Doops - Joel Cummins.mp3", 0, 98, MusicMood.funny, "Banjo Doops"),
            MusicTrack("Baby Animals Playing - Joel Cummins.mp3", 0, 124, MusicMood.funny, "Baby Animals Playing"),
            MusicTrack("Sinister - Anno Domini Beats.mp3", 0, 215, MusicMood.dark, "Sinister"),
            MusicTrack("Traversing - Godmode.mp3", 0, 95, MusicMood.dark, "Traversing"),
        ]
    
    def get_all_tracks(self) -> List[Dict[str, Any]]:
        """Get all available music tracks."""
        return [track.to_dict() for track in self.tracks]
    
    def get_tracks_by_mood(self, mood: str) -> List[Dict[str, Any]]:
        """Get tracks filtered by mood."""
        try:
            mood_enum = MusicMood(mood.lower())
            return [track.to_dict() for track in self.tracks if track.mood == mood_enum]
        except ValueError:
            logger.warning(f"Invalid mood: {mood}")
            return []
    
    def get_track_by_file(self, filename: str) -> Optional[Dict[str, Any]]:
        """Get a specific track by filename."""
        for track in self.tracks:
            if track.file == filename:
                return track.to_dict()
        return None
    
    def get_track_path(self, filename: str) -> Optional[str]:
        """Get the full path to a music file."""
        if self.music_dir is None:
            return None
            
        track_path = os.path.join(self.music_dir, filename)
        if os.path.exists(track_path):
            return track_path
        
        # Check for case sensitivity issues
        if os.path.exists(self.music_dir):
            files_in_dir = os.listdir(self.music_dir)
            matching_files = [f for f in files_in_dir if f.lower() == filename.lower()]
            if matching_files:
                actual_path = os.path.join(self.music_dir, matching_files[0])
                if os.path.exists(actual_path):
                    return actual_path
        
        return None
    
    def get_available_moods(self) -> List[str]:
        """Get list of available music moods."""
        return [mood.value for mood in MusicMood]
    
    def validate_track_exists(self, filename: str) -> bool:
        """Check if a music track file exists."""
        if not filename or self.music_dir is None:
            return False
        return os.path.exists(os.path.join(self.music_dir, filename))

# Global music service instance
music_service = MusicService()
