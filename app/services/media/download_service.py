"""
Service for handling media downloads using yt-dlp.
"""
from app.services.job_queue import job_queue, JobType
from app.services.s3 import s3_service
import logging
import os
import yt_dlp
import tempfile
import requests

logger = logging.getLogger(__name__)

class DownloadService:
    """
    Service for handling media downloads.
    """

    async def download_media(self, job_id: str, url: str, file_name: str, cookies_url: str) -> dict:
        """
        Download media from a URL.
        """
        try:
            params = {"url": url, "file_name": file_name, "cookies_url": cookies_url}

            await job_queue.add_job(
                job_id=job_id,
                job_type=JobType.MEDIA_DOWNLOAD,
                process_func=self.process_media_download,
                data=params,
            )

            return {"job_id": job_id}
        except Exception as e:
            logger.error(f"Error creating media download job: {e}")
            raise

    async def process_media_download(self, job_id: str, params: dict) -> dict:
        """
        Process the media download.
        
        Args:
            job_id: The ID of the job being processed
            params: Dictionary containing download parameters
        """
        cookies_file_path = None
        try:
            url = params["url"]
            file_name = params.get("file_name")
            cookies_url = params.get("cookies_url")

            ydl_opts = {
                "outtmpl": f"temp/{file_name or '%(title)s.%(ext)s'}",
                "quiet": True,
                # Enhanced YouTube handling for Coolify/server environments
                "http_headers": {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                },
                "extractor_retries": 3,
                "retries": 3,
                "fragment_retries": 3,
                "skip_unavailable_fragments": True,
                # Use best available format for server environments
                "format": "best[height<=1080][ext=mp4]/best[height<=720][ext=mp4]/best[ext=mp4]/best",
            }

            if cookies_url:
                logger.info(f"Downloading cookies from {cookies_url}")
                response = requests.get(cookies_url)
                response.raise_for_status() # Raise an exception for HTTP errors
                
                cookies_file_path = tempfile.NamedTemporaryFile(delete=False).name
                with open(cookies_file_path, "w") as f:
                    f.write(response.text)
                ydl_opts["cookiefile"] = cookies_file_path
                logger.info(f"Using cookies from {cookies_file_path}")

            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(url, download=True)
                file_path = ydl.prepare_filename(info)

            # Upload the downloaded file to S3
            file_url = await s3_service.upload_file(
                file_path=file_path,
                object_name=file_path.split("/")[-1],
            )

            # Return both local path and S3 URL for compatibility
            return {
                "file_url": file_url,
                "path": file_path,
                "title": info.get("title", "Downloaded Media") if info else "Downloaded Media",
                "duration": info.get("duration") if info else None,
                "uploader": info.get("uploader") if info else None,
                "upload_date": info.get("upload_date") if info else None
            }
        except Exception as e:
            logger.error(f"Error processing media download: {e}")
            raise
        finally:
            if cookies_file_path and os.path.exists(cookies_file_path):
                os.remove(cookies_file_path)
                logger.info(f"Removed temporary cookies file: {cookies_file_path}")

download_service = DownloadService()
