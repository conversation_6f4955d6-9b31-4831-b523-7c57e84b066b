"""
Service for generating YouTube transcripts.
"""
import logging
from typing import List, Optional
from app.services.job_queue import job_queue, JobType
from app.services.s3 import s3_service
import os
import uuid

# Optional dependency - YouTube transcript functionality
try:
    from youtube_transcript_api import YouTubeTranscriptApi
    from youtube_transcript_api._errors import NoTranscriptFound, TranscriptsDisabled, VideoUnavailable
    YOUTUBE_TRANSCRIPT_AVAILABLE = True
except ImportError:
    logger.warning("youtube_transcript_api not available. YouTube transcript features will be disabled.")
    YOUTUBE_TRANSCRIPT_AVAILABLE = False
    # Create placeholder classes for type checking
    class YouTubeTranscriptApi:
        pass
    class NoTranscriptFound(Exception):
        pass
    class TranscriptsDisabled(Exception):
        pass
    class VideoUnavailable(Exception):
        pass

logger = logging.getLogger(__name__)

class YouTubeTranscriptService:
    """
    Service for generating YouTube transcripts.
    """

    async def generate_transcript(
        self,
        job_id: str,
        video_url: str,
        languages: Optional[List[str]],
        translate_to: Optional[str],
        format: Optional[str],
    ) -> dict:
        """
        Generate a transcript for a YouTube video.
        """
        if not YOUTUBE_TRANSCRIPT_AVAILABLE:
            raise RuntimeError("YouTube transcript functionality is not available. Please install youtube_transcript_api.")
            
        try:
            video_id = self._extract_video_id(video_url)
            if not video_id:
                raise ValueError("Invalid YouTube URL provided.")

            params = {
                "video_id": video_id,
                "languages": languages,
                "translate_to": translate_to,
                "format": format,
            }

            await job_queue.add_job(
                job_id=job_id,
                job_type=JobType.YOUTUBE_TRANSCRIPT,
                process_func=self.process_transcript_generation,
                data=params,
            )

            return {"job_id": job_id}
        except Exception as e:
            logger.error(f"Error creating YouTube transcript job: {e}")
            raise

    async def process_transcript_generation(self, params: dict) -> dict:
        """
        Process the YouTube transcript generation.
        """
        if not YOUTUBE_TRANSCRIPT_AVAILABLE:
            raise RuntimeError("YouTube transcript functionality is not available. Please install youtube_transcript_api.")
            
        video_id = params["video_id"]
        languages = params.get("languages", ["en"])
        translate_to = params.get("translate_to")
        output_format = params.get("format", "json")

        try:
            transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
            transcript = transcript_list.find_transcript(languages)

            if translate_to:
                if not transcript.is_translatable:
                    raise ValueError("Selected transcript cannot be translated.")
                transcript = transcript.translate(translate_to)

            fetched_transcript = transcript.fetch()

            # Format the transcript
            if output_format == "json":
                formatter = JSONFormatter()
                formatted_transcript = formatter.format_transcript(fetched_transcript)
            else:
                # Default to JSON if format is not supported or specified
                formatter = JSONFormatter()
                formatted_transcript = formatter.format_transcript(fetched_transcript)
                logger.warning(f"Unsupported format '{output_format}'. Defaulting to JSON.")

            # Save to a temporary file
            temp_dir = "temp"
            os.makedirs(temp_dir, exist_ok=True)
            transcript_filename = f"transcript_{uuid.uuid4()}.json"
            transcript_path = os.path.join(temp_dir, transcript_filename)

            with open(transcript_path, "w", encoding="utf-8") as f:
                f.write(formatted_transcript)

            # Upload to S3
            s3_key = f"transcripts/{transcript_filename}"
            transcript_url = await s3_service.upload_file(transcript_path, s3_key)

            # Clean up temporary file
            os.remove(transcript_path)

            return {"transcript_url": transcript_url}

        except NoTranscriptFound:
            raise ValueError(f"No transcript found for video ID: {video_id} in languages: {languages}")
        except TranscriptsDisabled:
            raise ValueError(f"Transcripts are disabled for video ID: {video_id}")
        except VideoUnavailable:
            raise ValueError(f"Video with ID: {video_id} is unavailable.")
        except Exception as e:
            logger.error(f"Error processing YouTube transcript: {e}")
            raise

    def _extract_video_id(self, url: str) -> Optional[str]:
        """
        Extract video ID from various forms of YouTube URLs.
        """
        import re
        from urllib.parse import urlparse, parse_qs

        parsed_url = urlparse(url)

        if parsed_url.hostname in ('www.youtube.com', 'youtube.com'):
            if parsed_url.path == '/watch':
                return parse_qs(parsed_url.query).get('v', [None])[0]
            elif parsed_url.path.startswith('/embed/'):
                return parsed_url.path.split('/')[2]
            elif parsed_url.path.startswith('/v/'):
                return parsed_url.path.split('/')[2]
        elif parsed_url.hostname == 'youtu.be':
            return parsed_url.path[1:]

        return None

youtube_transcript_service = YouTubeTranscriptService()
