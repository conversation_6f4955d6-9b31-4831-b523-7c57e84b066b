import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Switch,
  FormControlLabel,
  FormGroup,
  Slider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider
} from '@mui/material';
import {
  Article as BlogIcon,
  TrendingUp as ViralIcon,
  YouTube as YouTubeIcon,
  Twitter as TwitterIcon,
  LinkedIn as LinkedInIcon,
  Instagram as InstagramIcon,
  Topic as TopicIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckIcon,
  Image as ImageIcon,
  Subtitles as TranscriptIcon,
  Share as ShareIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';
import { PostizScheduleDialog } from '../components/PostizScheduleDialog';
import { JobStatus } from '../types/ouinhi';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simone-tabpanel-${index}`}
      aria-labelledby={`simone-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface TopicData {
  topic: string;
  confidence: number;
  category: string;
  extracted_from: string;
  relevance_score?: number;
  key_points?: string[];
}

interface ThreadPost {
  post_number: number;
  content: string;
  character_count: number;
}

interface ViralContentPackage {
  content: {
    topics?: {
      topics: TopicData[];
    };
    x_thread?: {
      thread: ThreadPost[];
    };
    posts: {
      x?: string;
      linkedin?: string;
      instagram?: string;
      facebook?: string;
    };
  };
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    blog_post_content?: string;
    blog_post_url?: string;
    screenshots?: string[];
    social_media_post_content?: string;
    transcription_content?: string;
    transcription_url?: string;
    viral_content_package?: ViralContentPackage;
    content_package_url?: string;
    enhanced_features?: {
      topics_included: boolean;
      x_thread_included: boolean;
      platforms_processed: string[];
      thread_config: {
        max_posts: number;
        character_limit: number;
        thread_style: string;
      };
    };
    processing_summary?: {
      total_topics?: number;
      thread_posts?: number;
      platforms_generated?: string[];
      screenshots_count?: number;
    };
    [key: string]: string | number | boolean | string[] | ViralContentPackage | object | undefined;
  };
  error?: string | null;
}

const Simone: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, JobResult | null>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});
  const [jobStatuses, setJobStatuses] = useState<Record<string, string>>({});
  
  // Postiz integration
  const [postizDialogOpen, setPostizDialogOpen] = useState(false);
  const [selectedJobForScheduling, setSelectedJobForScheduling] = useState<{
    id: string;
    job_id: string;
    operation: string;
    status: JobStatus;
    result: {
      scheduling: {
        available: boolean;
        content_type: string;
        suggested_content: string;
      };
      [key: string]: unknown;
    };
  } | null>(null);

  // Basic Blog Form
  const [blogForm, setBlogForm] = useState({
    url: '',
    platform: '',
    cookies_content: '',
    cookies_url: ''
  });

  // Viral Content Form
  const [viralForm, setViralForm] = useState({
    url: '',
    include_topics: true,
    include_x_thread: true,
    platforms: ['x', 'linkedin', 'instagram'],
    thread_config: {
      max_posts: 8,
      character_limit: 280,
      thread_style: 'viral'
    },
    cookies_content: '',
    cookies_url: ''
  });

  // Generic job polling function
  const pollJobStatus = async (jobId: string, endpoint: string, toolName: string) => {
    const maxAttempts = 120; // 10 minutes max for video processing
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatuses(prev => ({ ...prev, [toolName]: status }));

        if (status === 'completed') {
          setResults(prev => ({ ...prev, [toolName]: { job_id: jobId, result: jobResult, status: 'completed' } }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        } else if (status === 'failed') {
          setErrors(prev => ({ ...prev, [toolName]: jobError || 'Job failed' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Job polling timeout' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Failed to check job status' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      }
    };

    poll();
  };

  const handleBlogSubmit = async () => {
    if (!blogForm.url.trim()) {
      setErrors(prev => ({ ...prev, blog: 'Video URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, blog: true }));
    setErrors(prev => ({ ...prev, blog: null }));
    setResults(prev => ({ ...prev, blog: null }));

    try {
      const payload = {
        url: blogForm.url,
        ...(blogForm.platform && { platform: blogForm.platform }),
        ...(blogForm.cookies_content && { cookies_content: blogForm.cookies_content }),
        ...(blogForm.cookies_url && { cookies_url: blogForm.cookies_url })
      };

      const response = await directApi.post('/api/v1/simone/video-to-blog', payload);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/simone/video-to-blog', 'blog');
      } else {
        setErrors(prev => ({ ...prev, blog: 'Failed to create blog processing job' }));
        setLoading(prev => ({ ...prev, blog: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, blog: errorMessage }));
      setLoading(prev => ({ ...prev, blog: false }));
    }
  };

  const handleViralSubmit = async () => {
    if (!viralForm.url.trim()) {
      setErrors(prev => ({ ...prev, viral: 'Video URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, viral: true }));
    setErrors(prev => ({ ...prev, viral: null }));
    setResults(prev => ({ ...prev, viral: null }));

    try {
      const payload = {
        url: viralForm.url,
        include_topics: viralForm.include_topics,
        include_x_thread: viralForm.include_x_thread,
        platforms: viralForm.platforms,
        thread_config: viralForm.thread_config,
        ...(viralForm.cookies_content && { cookies_content: viralForm.cookies_content }),
        ...(viralForm.cookies_url && { cookies_url: viralForm.cookies_url })
      };

      const response = await directApi.post('/api/v1/simone/viral-content', payload);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/simone/viral-content', 'viral');
      } else {
        setErrors(prev => ({ ...prev, viral: 'Failed to create viral content job' }));
        setLoading(prev => ({ ...prev, viral: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, viral: errorMessage }));
      setLoading(prev => ({ ...prev, viral: false }));
    }
  };

  const handlePlatformToggle = (platform: string) => {
    setViralForm(prev => ({
      ...prev,
      platforms: prev.platforms.includes(platform)
        ? prev.platforms.filter(p => p !== platform)
        : [...prev.platforms, platform]
    }));
  };

  // Postiz scheduling handlers
  const handleScheduleClick = (toolName: string, result: JobResult) => {
    // Create a job object compatible with PostizScheduleDialog
    const jobForScheduling = {
      id: result.job_id,
      job_id: result.job_id,
      operation: toolName === 'blog' ? 'Video to Blog' : 'Viral Content Generation',
      status: JobStatus.COMPLETED,
      result: {
        scheduling: {
          available: true,
          content_type: 'text',
          suggested_content: generateSuggestedContent(toolName, result)
        },
        ...result.result
      }
    };

    setSelectedJobForScheduling(jobForScheduling);
    setPostizDialogOpen(true);
  };

  const generateSuggestedContent = (toolName: string, result: JobResult): string => {
    if (toolName === 'viral' && result.result?.viral_content_package?.content.posts) {
      // Use the generated X (Twitter) post if available
      const xPost = result.result.viral_content_package.content.posts.x;
      if (xPost) {
        return xPost;
      }
    }

    if (result.result?.social_media_post_content) {
      return result.result.social_media_post_content;
    }

    // Fallback content
    if (toolName === 'viral') {
      return `🤖 Just generated amazing viral content with Simone AI! Multi-platform social media posts, X threads, and blog content all from a single video. #AI #ContentCreation #ViralMarketing #Automation`;
    } else {
      return `📝 Just converted a video into a comprehensive blog post using Simone AI! Complete with transcription, screenshots, and social media content. #AI #ContentCreation #BlogPost #Automation`;
    }
  };

  const handlePostizSchedule = async (scheduleData: {
    jobId: string;
    content: string;
    integrations: string[];
    postType: string;
    scheduleDate?: Date;
    tags: string[];
  }) => {
    const apiKey = localStorage.getItem('ouinhi_api_key');
    if (!apiKey) {
      throw new Error('API key not found');
    }

    const response = await fetch('/api/v1/postiz/schedule-job', {
      method: 'POST',
      headers: {
        'X-API-Key': apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        job_id: scheduleData.jobId,
        content: scheduleData.content,
        integrations: scheduleData.integrations,
        post_type: scheduleData.postType,
        schedule_date: scheduleData.scheduleDate,
        tags: scheduleData.tags
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to schedule post');
    }

    // Post scheduled successfully - could show a toast notification here
  };

  const renderJobResult = (toolName: string, result: JobResult | null, icon: React.ReactNode) => {
    if (!result && !loading[toolName] && !errors[toolName]) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {toolName === 'blog' ? 'Blog Processing' : 'Viral Content'} Result
            {loading[toolName] && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading[toolName] && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatuses[toolName] || 'Processing...'}
              </Typography>
            </Box>
          )}

          {errors[toolName] && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors[toolName]}
            </Alert>
          )}

          {result && jobStatuses[toolName] === 'completed' && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 Content generated successfully!
              </Alert>

              {/* Schedule to Postiz Button */}
              <Box sx={{ mb: 2 }}>
                <Button
                  startIcon={<ScheduleIcon />}
                  onClick={() => handleScheduleClick(toolName, result)}
                  variant="contained"
                  color="secondary"
                  size="medium"
                >
                  Schedule to Social Media
                </Button>
              </Box>

              {/* Blog Post Content */}
              {result.result.blog_post_content && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BlogIcon /> Blog Post Content
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                        {result.result.blog_post_content.substring(0, 1000)}
                        {result.result.blog_post_content.length > 1000 && '...'}
                      </Typography>
                    </Paper>
                    {result.result.blog_post_url && (
                      <Button
                        startIcon={<DownloadIcon />}
                        href={result.result.blog_post_url.startsWith('http') ? result.result.blog_post_url : `${window.location.origin}${result.result.blog_post_url}`}
                        target="_blank"
                        variant="outlined"
                        size="small"
                        sx={{ mt: 1 }}
                      >
                        Download Full Blog Post
                      </Button>
                    )}
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Social Media Posts */}
              {(result.result.social_media_post_content || result.result.viral_content_package?.content.posts) && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShareIcon /> Social Media Posts
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {result.result.social_media_post_content && (
                      <Paper sx={{ p: 2, bgcolor: '#f8fafc', mb: 2 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                          Generated Post:
                        </Typography>
                        <Typography variant="body2">{result.result.social_media_post_content}</Typography>
                      </Paper>
                    )}

                    {result.result.viral_content_package?.content.posts && (
                      <Grid container spacing={2}>
                        {Object.entries(result.result.viral_content_package.content.posts).map(([platform, content]) => (
                          <Grid item xs={12} md={6} key={platform}>
                            <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {platform === 'x' && <TwitterIcon fontSize="small" />}
                                  {platform === 'linkedin' && <LinkedInIcon fontSize="small" />}
                                  {platform === 'instagram' && <InstagramIcon fontSize="small" />}
                                  <Typography variant="subtitle2" sx={{ fontWeight: 600, textTransform: 'capitalize' }}>
                                    {platform === 'x' ? 'X (Twitter)' : platform}
                                  </Typography>
                                </Box>
                                <Button
                                  size="small"
                                  startIcon={<ScheduleIcon />}
                                  onClick={() => {
                                    const platformResult = { ...result, result: { ...result.result, social_media_post_content: content } };
                                    handleScheduleClick(toolName, platformResult);
                                  }}
                                  variant="outlined"
                                >
                                  Schedule
                                </Button>
                              </Box>
                              <Typography variant="body2">{content}</Typography>
                            </Paper>
                          </Grid>
                        ))}
                      </Grid>
                    )}
                  </AccordionDetails>
                </Accordion>
              )}

              {/* X Thread */}
              {result.result.viral_content_package?.content.x_thread?.thread && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TwitterIcon /> X Thread ({result.result.viral_content_package.content.x_thread.thread.length} posts)
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Box sx={{ maxHeight: 400, overflow: 'auto' }}>
                      {result.result.viral_content_package.content.x_thread.thread.map((post, index) => (
                        <Paper key={index} sx={{ p: 2, mb: 1, bgcolor: index % 2 === 0 ? '#f8fafc' : '#ffffff' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="caption" sx={{ fontWeight: 600 }}>
                              Post {post.post_number || index + 1}
                            </Typography>
                            {post.character_count && (
                              <Typography variant="caption" color="text.secondary">
                                {post.character_count} chars
                              </Typography>
                            )}
                          </Box>
                          <Typography variant="body2">{post.content}</Typography>
                        </Paper>
                      ))}
                    </Box>
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Topics */}
              {result.result.viral_content_package?.content.topics && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TopicIcon /> Identified Topics ({result.result.viral_content_package.content.topics.topics?.length || 0})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      {(result.result.viral_content_package.content.topics.topics || []).map((topic, index) => (
                        <Grid item xs={12} md={6} key={index}>
                          <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                              <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                {topic.topic}
                              </Typography>
                              {topic.confidence && (
                                <Chip 
                                  label={`${(topic.confidence * 100).toFixed(0)}%`} 
                                  size="small" 
                                  color="primary" 
                                />
                              )}
                            </Box>
                            {topic.key_points && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {topic.key_points.map((point, pointIndex) => (
                                  <Chip key={pointIndex} label={point} size="small" variant="outlined" />
                                ))}
                              </Box>
                            )}
                          </Paper>
                        </Grid>
                      ))}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Screenshots */}
              {result.result.screenshots && result.result.screenshots.length > 0 && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ImageIcon /> Screenshots ({result.result.screenshots.length})
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      {result.result.screenshots.map((screenshot, index) => {
                        // Handle both S3 URLs and local paths
                        const imageUrl = screenshot.startsWith('http') ? screenshot : `${window.location.origin}${screenshot}`;
                        return (
                          <Grid item xs={12} md={4} key={index}>
                            <Card variant="outlined">
                              <img
                                src={imageUrl}
                                alt={`Screenshot ${index + 1}`}
                                style={{
                                  width: '100%',
                                  height: 150,
                                  objectFit: 'cover'
                                }}
                                onError={(e) => {
                                  console.error('Failed to load image:', imageUrl);
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                              <CardContent sx={{ p: 1 }}>
                                <Button
                                  fullWidth
                                  size="small"
                                  startIcon={<DownloadIcon />}
                                  href={imageUrl}
                                  target="_blank"
                                >
                                  Download
                                </Button>
                              </CardContent>
                            </Card>
                          </Grid>
                        );
                      })}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Transcription */}
              {result.result.transcription_content && (
                <Accordion sx={{ mb: 2 }}>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TranscriptIcon /> Transcription
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 300, overflow: 'auto' }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                        {result.result.transcription_content.substring(0, 2000)}
                        {result.result.transcription_content.length > 2000 && '...'}
                      </Typography>
                    </Paper>
                    {result.result.transcription_url && (
                      <Button
                        startIcon={<DownloadIcon />}
                        href={result.result.transcription_url.startsWith('http') ? result.result.transcription_url : `${window.location.origin}${result.result.transcription_url}`}
                        target="_blank"
                        variant="outlined"
                        size="small"
                        sx={{ mt: 1 }}
                      >
                        Download Full Transcription
                      </Button>
                    )}
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Processing Summary */}
              {result.result.processing_summary && (
                <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                  <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>📊 Processing Summary:</Typography>
                  <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                    {result.result.processing_summary.total_topics && (
                      <Grid item xs={6} md={3}>
                        <strong>Topics:</strong> {result.result.processing_summary.total_topics}
                      </Grid>
                    )}
                    {result.result.processing_summary.thread_posts && (
                      <Grid item xs={6} md={3}>
                        <strong>Thread Posts:</strong> {result.result.processing_summary.thread_posts}
                      </Grid>
                    )}
                    {result.result.processing_summary.screenshots_count && (
                      <Grid item xs={6} md={3}>
                        <strong>Screenshots:</strong> {result.result.processing_summary.screenshots_count}
                      </Grid>
                    )}
                    {result.result.processing_summary.platforms_generated && (
                      <Grid item xs={6} md={3}>
                        <strong>Platforms:</strong> {result.result.processing_summary.platforms_generated.join(', ')}
                      </Grid>
                    )}
                  </Grid>
                </Paper>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Simone AI 🤖
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Transform videos into blog posts, social media content, and viral threads using AI.
        </Typography>
      </Box>

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flexGrow: 1 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ px: 3 }}
          >
            <Tab icon={<BlogIcon />} label="Video to Blog" />
            <Tab icon={<ViralIcon />} label="Viral Content Generator" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Video to Blog Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BlogIcon color="primary" />
                      Convert Video to Blog Post
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Video URL"
                          placeholder="https://www.youtube.com/watch?v=..."
                          value={blogForm.url}
                          onChange={(e) => setBlogForm({ ...blogForm, url: e.target.value })}
                          helperText="YouTube, TikTok, Instagram, Twitter, or direct video URL"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Social Media Platform (Optional)</InputLabel>
                          <Select
                            value={blogForm.platform}
                            label="Social Media Platform (Optional)"
                            onChange={(e) => setBlogForm({ ...blogForm, platform: e.target.value })}
                          >
                            <MenuItem value="">None</MenuItem>
                            <MenuItem value="x">X (Twitter)</MenuItem>
                            <MenuItem value="linkedin">LinkedIn</MenuItem>
                            <MenuItem value="instagram">Instagram</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Cookies URL (Optional)"
                          placeholder="https://example.com"
                          value={blogForm.cookies_url}
                          onChange={(e) => setBlogForm({ ...blogForm, cookies_url: e.target.value })}
                          helperText="For private/authenticated content"
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={2}
                          label="Cookies Content (Optional)"
                          placeholder="session_token=abc123; user_id=456789"
                          value={blogForm.cookies_content}
                          onChange={(e) => setBlogForm({ ...blogForm, cookies_content: e.target.value })}
                          helperText="Authentication cookies for private content"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.blog ? <CircularProgress size={20} /> : <BlogIcon />}
                      onClick={handleBlogSubmit}
                      disabled={loading.blog || !blogForm.url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.blog ? 'Processing...' : 'Generate Blog Post'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      What You'll Get
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="AI-Generated Blog Post"
                          secondary="Full article with SEO optimization"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Video Transcription"
                          secondary="Complete text transcript"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Key Screenshots"
                          secondary="AI-selected video frames"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Social Media Post"
                          secondary="Platform-optimized content"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('blog', results.blog, <BlogIcon />)}
          </TabPanel>

          {/* Viral Content Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ViralIcon color="primary" />
                      Viral Content Generator
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Video URL"
                          placeholder="https://www.youtube.com/watch?v=..."
                          value={viralForm.url}
                          onChange={(e) => setViralForm({ ...viralForm, url: e.target.value })}
                          helperText="YouTube, TikTok, Instagram, Twitter, or direct video URL"
                        />
                      </Grid>

                      {/* Platform Selection */}
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                          Target Platforms:
                        </Typography>
                        <FormGroup row>
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={viralForm.platforms.includes('x')}
                                onChange={() => handlePlatformToggle('x')}
                              />
                            }
                            label={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <TwitterIcon fontSize="small" />
                                X (Twitter)
                              </Box>
                            }
                          />
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={viralForm.platforms.includes('linkedin')}
                                onChange={() => handlePlatformToggle('linkedin')}
                              />
                            }
                            label={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <LinkedInIcon fontSize="small" />
                                LinkedIn
                              </Box>
                            }
                          />
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={viralForm.platforms.includes('instagram')}
                                onChange={() => handlePlatformToggle('instagram')}
                              />
                            }
                            label={
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <InstagramIcon fontSize="small" />
                                Instagram
                              </Box>
                            }
                          />
                        </FormGroup>
                      </Grid>

                      {/* Content Options */}
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                          Content Options:
                        </Typography>
                        <FormGroup>
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={viralForm.include_topics}
                                onChange={(e) => setViralForm({ ...viralForm, include_topics: e.target.checked })}
                              />
                            }
                            label="Include Topic Identification"
                          />
                          <FormControlLabel
                            control={
                              <Switch 
                                checked={viralForm.include_x_thread}
                                onChange={(e) => setViralForm({ ...viralForm, include_x_thread: e.target.checked })}
                              />
                            }
                            label="Generate X Thread"
                          />
                        </FormGroup>
                      </Grid>

                      {/* Thread Configuration */}
                      {viralForm.include_x_thread && (
                        <Grid item xs={12}>
                          <Accordion>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                              <Typography variant="h6">Thread Configuration</Typography>
                            </AccordionSummary>
                            <AccordionDetails>
                              <Grid container spacing={2}>
                                <Grid item xs={12} md={4}>
                                  <Typography gutterBottom>Max Posts: {viralForm.thread_config.max_posts}</Typography>
                                  <Slider
                                    value={viralForm.thread_config.max_posts}
                                    onChange={(_e, value) => setViralForm({ 
                                      ...viralForm, 
                                      thread_config: { 
                                        ...viralForm.thread_config, 
                                        max_posts: Array.isArray(value) ? value[0] : value 
                                      } 
                                    })}
                                    min={5}
                                    max={20}
                                    step={1}
                                    marks={[
                                      { value: 5, label: '5' },
                                      { value: 10, label: '10' },
                                      { value: 15, label: '15' },
                                      { value: 20, label: '20' }
                                    ]}
                                  />
                                </Grid>

                                <Grid item xs={12} md={4}>
                                  <Typography gutterBottom>Character Limit: {viralForm.thread_config.character_limit}</Typography>
                                  <Slider
                                    value={viralForm.thread_config.character_limit}
                                    onChange={(_e, value) => setViralForm({ 
                                      ...viralForm, 
                                      thread_config: { 
                                        ...viralForm.thread_config, 
                                        character_limit: Array.isArray(value) ? value[0] : value 
                                      } 
                                    })}
                                    min={200}
                                    max={400}
                                    step={20}
                                    marks={[
                                      { value: 200, label: '200' },
                                      { value: 280, label: '280' },
                                      { value: 400, label: '400' }
                                    ]}
                                  />
                                </Grid>

                                <Grid item xs={12} md={4}>
                                  <FormControl fullWidth>
                                    <InputLabel>Thread Style</InputLabel>
                                    <Select
                                      value={viralForm.thread_config.thread_style}
                                      label="Thread Style"
                                      onChange={(e) => setViralForm({ 
                                        ...viralForm, 
                                        thread_config: { 
                                          ...viralForm.thread_config, 
                                          thread_style: e.target.value 
                                        } 
                                      })}
                                    >
                                      <MenuItem value="viral">Viral</MenuItem>
                                      <MenuItem value="professional">Professional</MenuItem>
                                      <MenuItem value="casual">Casual</MenuItem>
                                    </Select>
                                  </FormControl>
                                </Grid>
                              </Grid>
                            </AccordionDetails>
                          </Accordion>
                        </Grid>
                      )}

                      {/* Authentication (Optional) */}
                      <Grid item xs={12}>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="h6">Authentication (Optional)</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid container spacing={2}>
                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  label="Cookies URL"
                                  placeholder="https://example.com"
                                  value={viralForm.cookies_url}
                                  onChange={(e) => setViralForm({ ...viralForm, cookies_url: e.target.value })}
                                  helperText="For private/authenticated content"
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  multiline
                                  rows={2}
                                  label="Cookies Content"
                                  placeholder="session_token=abc123; user_id=456789"
                                  value={viralForm.cookies_content}
                                  onChange={(e) => setViralForm({ ...viralForm, cookies_content: e.target.value })}
                                  helperText="Authentication cookies for private content"
                                />
                              </Grid>
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.viral ? <CircularProgress size={20} /> : <ViralIcon />}
                      onClick={handleViralSubmit}
                      disabled={loading.viral || !viralForm.url.trim() || viralForm.platforms.length === 0}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.viral ? 'Processing...' : 'Generate Viral Content'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Enhanced Features
                    </Typography>
                    <List dense>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Multi-Platform Content"
                          secondary="X, LinkedIn, Instagram posts"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Viral X Threads"
                          secondary="Engagement-optimized threads"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Topic Analysis"
                          secondary="AI-identified themes & subjects"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <CheckIcon color="success" fontSize="small" />
                        </ListItemIcon>
                        <ListItemText 
                          primary="Complete Package"
                          secondary="Blog + Screenshots + Transcripts"
                        />
                      </ListItem>
                    </List>

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Supported Platforms:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      <Chip icon={<YouTubeIcon />} label="YouTube" size="small" variant="outlined" />
                      <Chip icon={<TwitterIcon />} label="Twitter" size="small" variant="outlined" />
                      <Chip icon={<InstagramIcon />} label="Instagram" size="small" variant="outlined" />
                      <Chip label="TikTok" size="small" variant="outlined" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('viral', results.viral, <ViralIcon />)}
          </TabPanel>
        </Box>
      </Paper>

      {/* Postiz Schedule Dialog */}
      <PostizScheduleDialog
        open={postizDialogOpen}
        onClose={() => setPostizDialogOpen(false)}
        job={selectedJobForScheduling}
        onSchedule={handlePostizSchedule}
      />
    </Box>
  );
};

export default Simone;