import React, { useState } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper
} from '@mui/material';
import {
  Add as CreateIcon,
  AutoAwesome
} from '@mui/icons-material';

// Import modular content creation components
import { VideoCreatorTab, VideoGeneratorTab } from '../components/contentCreation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`content-creation-tabpanel-${index}`}
    aria-labelledby={`content-creation-tab-${index}`}
  >
    {value === index && <Box>{children}</Box>}
  </div>
);

const ContentCreation: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 2
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Content Creation 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Create engaging videos with AI-powered script generation and research tools.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, mb: 3, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          aria-label="content creation tabs"
          sx={{ borderBottom: 1, borderColor: 'divider', flexShrink: 0 }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab
            label="Video Creator"
            icon={<CreateIcon />}
            id="content-creation-tab-0"
            iconPosition="start"
            sx={{ minWidth: 160 }}
          />
          <Tab
            label="AI Video Generator"
            icon={<AutoAwesome />}
            id="content-creation-tab-1"
            iconPosition="start"
            sx={{ minWidth: 160 }}
          />
        </Tabs>

        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <TabPanel value={tabValue} index={0}>
            <VideoCreatorTab />
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            <VideoGeneratorTab />
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default ContentCreation;