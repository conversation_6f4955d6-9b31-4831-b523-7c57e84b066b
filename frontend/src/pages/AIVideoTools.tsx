import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider
} from '@mui/material';
import {
  AutoAwesome as AIIcon,
  Search as SearchIcon,
  Movie as ScenesIcon,
  Newspaper as NewsIcon,
  Chat as ChatIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  PlayArrow as PlayIcon,
  ContentCopy as CopyIcon,
  VideoLibrary as VideoIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tools-tabpanel-${index}`}
      aria-labelledby={`ai-tools-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    script?: string;
    script_content?: string;
    final_video_url?: string;
    video_url?: string;
    search_queries?: string[];
    videos?: Array<{ 
      id: string; 
      url: string; 
      image: string; 
      duration: number;
      width: number;
      height: number;
      user: { name: string };
    }>;
    news_articles?: Array<{
      title: string;
      description: string;
      url: string;
      source: string;
      publishedAt: string;
    }>;
    content?: string;
    [key: string]: unknown;
  };
  error?: string | null;
}

const AIVideoTools: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, JobResult | null>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});
  const [jobStatuses, setJobStatuses] = useState<Record<string, string>>({});

  // Form states for different tools
  const [scriptGenForm, setScriptGenForm] = useState({
    topic: '',
    script_type: 'facts',
    language: 'en',
    target_duration: 60,
    style: 'engaging'
  });

  const [videoSearchForm, setVideoSearchForm] = useState({
    topic: '',
    language: 'en',
    orientation: 'portrait',
    per_page: 15,
    page: 1
  });

  const [manualSearchForm, setManualSearchForm] = useState({
    query: '',
    orientation: 'landscape',
    per_page: 15,
    page: 1
  });

  const [scenesForm, setScenesForm] = useState({
    scenes: [
      { text: '', searchTerms: [''], duration: 3.0 }
    ],
    voice_provider: 'kokoro',
    voice_name: 'af_bella',
    language: 'en',
    resolution: '1080x1920'
  });

  const [newsResearchForm, setNewsResearchForm] = useState({
    searchTerm: '',
    targetLanguage: 'en',
    maxResults: 5
  });

  const [aiChatForm, setAiChatForm] = useState({
    prompt: '',
    max_tokens: 1000,
    temperature: 0.7,
    provider: 'auto'
  });

  // Generic job polling function
  const pollJobStatus = async (jobId: string, endpoint: string, toolName: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatuses(prev => ({ ...prev, [toolName]: `${status} (${attempts}/${maxAttempts})` }));

        if (status === 'completed') {
          setResults(prev => ({ ...prev, [toolName]: { job_id: jobId, result: jobResult, status: 'completed' } }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        } else if (status === 'failed') {
          setErrors(prev => ({ ...prev, [toolName]: jobError || 'Job failed' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Job polling timeout' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Failed to check job status' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      }
    };

    poll();
  };

  const handleScriptGeneration = async () => {
    if (!scriptGenForm.topic.trim()) {
      setErrors(prev => ({ ...prev, scriptgen: 'Topic is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, scriptgen: true }));
    setErrors(prev => ({ ...prev, scriptgen: null }));
    setResults(prev => ({ ...prev, scriptgen: null }));

    try {
      const response = await directApi.post('/api/v1/ai/script-generation', scriptGenForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/script-generation', 'scriptgen');
      } else {
        setErrors(prev => ({ ...prev, scriptgen: 'Failed to create script generation job' }));
        setLoading(prev => ({ ...prev, scriptgen: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, scriptgen: errorMessage }));
      setLoading(prev => ({ ...prev, scriptgen: false }));
    }
  };

  const handleVideoSearch = async () => {
    if (!videoSearchForm.topic.trim()) {
      setErrors(prev => ({ ...prev, videosearch: 'Topic is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, videosearch: true }));
    setErrors(prev => ({ ...prev, videosearch: null }));
    setResults(prev => ({ ...prev, videosearch: null }));

    try {
      const response = await directApi.post('/api/v1/ai/video-search-queries', videoSearchForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/video-search-queries', 'videosearch');
      } else {
        setErrors(prev => ({ ...prev, videosearch: 'Failed to create video search job' }));
        setLoading(prev => ({ ...prev, videosearch: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, videosearch: errorMessage }));
      setLoading(prev => ({ ...prev, videosearch: false }));
    }
  };

  const handleManualVideoSearch = async () => {
    if (!manualSearchForm.query.trim()) {
      setErrors(prev => ({ ...prev, manualsearch: 'Search query is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, manualsearch: true }));
    setErrors(prev => ({ ...prev, manualsearch: null }));
    setResults(prev => ({ ...prev, manualsearch: null }));

    try {
      const response = await directApi.post('/api/v1/ai/video-browse', manualSearchForm);
      setResults(prev => ({ ...prev, manualsearch: { job_id: 'manual', result: response.data, status: 'completed' } }));
      setLoading(prev => ({ ...prev, manualsearch: false }));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, manualsearch: errorMessage }));
      setLoading(prev => ({ ...prev, manualsearch: false }));
    }
  };

  const handleScenesVideo = async () => {
    const validScenes = scenesForm.scenes.filter(scene => scene.text.trim() && scene.searchTerms.some(term => term.trim()));
    if (validScenes.length === 0) {
      setErrors(prev => ({ ...prev, scenes: 'At least one scene with text and search terms is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, scenes: true }));
    setErrors(prev => ({ ...prev, scenes: null }));
    setResults(prev => ({ ...prev, scenes: null }));

    try {
      const response = await directApi.post('/api/v1/ai/scenes-to-video', {
        ...scenesForm,
        scenes: validScenes
      });
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/ai/scenes-to-video', 'scenes');
      } else {
        setErrors(prev => ({ ...prev, scenes: 'Failed to create scenes video job' }));
        setLoading(prev => ({ ...prev, scenes: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, scenes: errorMessage }));
      setLoading(prev => ({ ...prev, scenes: false }));
    }
  };

  const handleNewsResearch = async () => {
    if (!newsResearchForm.searchTerm.trim()) {
      setErrors(prev => ({ ...prev, news: 'Search term is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, news: true }));
    setErrors(prev => ({ ...prev, news: null }));
    setResults(prev => ({ ...prev, news: null }));

    try {
      const response = await directApi.post('/api/v1/ai/news-research', newsResearchForm);
      setResults(prev => ({ ...prev, news: { job_id: 'news', result: response.data, status: 'completed' } }));
      setLoading(prev => ({ ...prev, news: false }));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, news: errorMessage }));
      setLoading(prev => ({ ...prev, news: false }));
    }
  };

  const handleAIChat = async () => {
    if (!aiChatForm.prompt.trim()) {
      setErrors(prev => ({ ...prev, aichat: 'Prompt is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, aichat: true }));
    setErrors(prev => ({ ...prev, aichat: null }));
    setResults(prev => ({ ...prev, aichat: null }));

    try {
      const response = await directApi.post('/api/v1/ai/generate', aiChatForm);
      setResults(prev => ({ ...prev, aichat: { job_id: 'chat', result: response.data, status: 'completed' } }));
      setLoading(prev => ({ ...prev, aichat: false }));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, aichat: errorMessage }));
      setLoading(prev => ({ ...prev, aichat: false }));
    }
  };

  const addScene = () => {
    setScenesForm(prev => ({
      ...prev,
      scenes: [...prev.scenes, { text: '', searchTerms: [''], duration: 3.0 }]
    }));
  };

  const removeScene = (index: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.filter((_, i) => i !== index)
    }));
  };

  const updateScene = (index: number, field: string, value: unknown) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === index ? { ...scene, [field]: value } : scene
      )
    }));
  };

  const addSearchTerm = (sceneIndex: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { ...scene, searchTerms: [...scene.searchTerms, ''] }
          : scene
      )
    }));
  };

  const removeSearchTerm = (sceneIndex: number, termIndex: number) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { ...scene, searchTerms: scene.searchTerms.filter((_, ti) => ti !== termIndex) }
          : scene
      )
    }));
  };

  const updateSearchTerm = (sceneIndex: number, termIndex: number, value: string) => {
    setScenesForm(prev => ({
      ...prev,
      scenes: prev.scenes.map((scene, i) => 
        i === sceneIndex 
          ? { 
              ...scene, 
              searchTerms: scene.searchTerms.map((term, ti) => 
                ti === termIndex ? value : term
              ) 
            }
          : scene
      )
    }));
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const renderJobResult = (toolName: string, result: JobResult | null, icon: React.ReactNode) => {
    if (!result && !loading[toolName] && !errors[toolName]) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {toolName.charAt(0).toUpperCase() + toolName.slice(1)} Result
            {loading[toolName] && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading[toolName] && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatuses[toolName] || 'Processing...'}
              </Typography>
            </Box>
          )}

          {errors[toolName] && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors[toolName]}
            </Alert>
          )}

          {result && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 {toolName.charAt(0).toUpperCase() + toolName.slice(1)} completed successfully!
              </Alert>

              {/* Script Content */}
              {(result.result.script || result.result.script_content) && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      Generated Script
                    </Typography>
                    <Button
                      startIcon={<CopyIcon />}
                      onClick={() => copyToClipboard(result.result?.script || result.result?.script_content || '')}
                      variant="outlined"
                      size="small"
                    >
                      Copy Script
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.script || result.result.script_content}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* Search Queries */}
              {result.result.search_queries && result.result.search_queries.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    AI-Generated Search Queries
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {result.result.search_queries.map((query, index) => (
                      <Chip 
                        key={index} 
                        label={query} 
                        variant="outlined"
                        onClick={() => copyToClipboard(query)}
                        sx={{ cursor: 'pointer' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}

              {/* Video Results */}
              {result.result.videos && result.result.videos.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    Found Videos ({result.result.videos.length})
                  </Typography>
                  <Grid container spacing={2}>
                    {result.result.videos.slice(0, 12).map((video, index) => (
                      <Grid item xs={12} sm={6} md={4} key={video.id}>
                        <Card elevation={1}>
                          <Box sx={{ position: 'relative' }}>
                            <img
                              src={video.image}
                              alt={`Video ${index + 1}`}
                              style={{
                                width: '100%',
                                height: '120px',
                                objectFit: 'cover'
                              }}
                            />
                            <Chip 
                              label={`${video.duration}s`}
                              size="small"
                              sx={{ 
                                position: 'absolute', 
                                bottom: 8, 
                                right: 8,
                                bgcolor: 'rgba(0,0,0,0.7)',
                                color: 'white'
                              }}
                            />
                          </Box>
                          <CardContent sx={{ p: 1.5 }}>
                            <Typography variant="caption" color="text.secondary">
                              {video.width}x{video.height} • by {video.user.name}
                            </Typography>
                            <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                              <Button
                                startIcon={<PlayIcon />}
                                href={video.url}
                                target="_blank"
                                size="small"
                                variant="outlined"
                                sx={{ flex: 1 }}
                              >
                                View
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* News Articles */}
              {result.result.news_articles && result.result.news_articles.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    News Articles ({result.result.news_articles.length})
                  </Typography>
                  <List>
                    {result.result.news_articles.map((article, index) => (
                      <React.Fragment key={index}>
                        <ListItem alignItems="flex-start">
                          <ListItemAvatar>
                            <Avatar sx={{ bgcolor: 'primary.main' }}>
                              <NewsIcon />
                            </Avatar>
                          </ListItemAvatar>
                          <ListItemText
                            primary={
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                                <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
                                  {article.title}
                                </Typography>
                                <Button
                                  href={article.url}
                                  target="_blank"
                                  size="small"
                                  variant="outlined"
                                  sx={{ ml: 1, minWidth: 'auto' }}
                                >
                                  Read
                                </Button>
                              </Box>
                            }
                            secondary={
                              <Box>
                                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                                  {article.description}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {article.source} • {new Date(article.publishedAt).toLocaleDateString()}
                                </Typography>
                              </Box>
                            }
                          />
                        </ListItem>
                        {index < (result.result?.news_articles?.length || 0) - 1 && <Divider />}
                      </React.Fragment>
                    ))}
                  </List>
                </Box>
              )}

              {/* AI Chat Response */}
              {result.result.content && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      AI Response
                    </Typography>
                    <Button
                      startIcon={<CopyIcon />}
                      onClick={() => copyToClipboard(result.result?.content || '')}
                      variant="outlined"
                      size="small"
                    >
                      Copy Response
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.content}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* Video Download */}
              {(result.result.final_video_url || result.result.video_url) && (
                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      Generated Video
                    </Typography>
                    <Button
                      startIcon={<DownloadIcon />}
                      component="a"
                      href={result.result?.final_video_url || result.result?.video_url || '#'}
                      target="_blank"
                      variant="contained"
                      size="small"
                    >
                      Download
                    </Button>
                  </Box>
                  
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                    <video
                      src={result.result.final_video_url || result.result.video_url}
                      controls
                      style={{
                        width: '100%',
                        maxHeight: '400px',
                        borderRadius: '8px'
                      }}
                    />
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          AI Video Tools 🤖
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Advanced AI-powered tools for script generation, video search, scene creation, and content research.
        </Typography>
      </Box>

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flexGrow: 1 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ px: 3 }}
          >
            <Tab icon={<AIIcon />} label="Script Generator" />
            <Tab icon={<SearchIcon />} label="Video Search" />
            <Tab icon={<ScenesIcon />} label="Scene Builder" />
            <Tab icon={<NewsIcon />} label="News Research" />
            <Tab icon={<ChatIcon />} label="AI Chat" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Script Generator Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AIIcon color="primary" />
                      AI Script Generator
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Topic"
                          placeholder="Enter your video topic or concept..."
                          value={scriptGenForm.topic}
                          onChange={(e) => setScriptGenForm({ ...scriptGenForm, topic: e.target.value })}
                          helperText="Describe what you want your video to be about"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Script Type</InputLabel>
                          <Select
                            value={scriptGenForm.script_type}
                            label="Script Type"
                            onChange={(e) => setScriptGenForm({ ...scriptGenForm, script_type: e.target.value })}
                          >
                            <MenuItem value="facts">Interesting Facts</MenuItem>
                            <MenuItem value="story">Storytelling</MenuItem>
                            <MenuItem value="tutorial">Tutorial/How-to</MenuItem>
                            <MenuItem value="news">News Summary</MenuItem>
                            <MenuItem value="entertainment">Entertainment</MenuItem>
                            <MenuItem value="educational">Educational</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Language</InputLabel>
                          <Select
                            value={scriptGenForm.language}
                            label="Language"
                            onChange={(e) => setScriptGenForm({ ...scriptGenForm, language: e.target.value })}
                          >
                            <MenuItem value="en">English</MenuItem>
                            <MenuItem value="es">Spanish</MenuItem>
                            <MenuItem value="fr">French</MenuItem>
                            <MenuItem value="de">German</MenuItem>
                            <MenuItem value="it">Italian</MenuItem>
                            <MenuItem value="pt">Portuguese</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Target Duration (seconds)"
                          value={scriptGenForm.target_duration}
                          onChange={(e) => setScriptGenForm({ ...scriptGenForm, target_duration: parseInt(e.target.value) })}
                          inputProps={{ min: 15, max: 300 }}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.scriptgen ? <CircularProgress size={20} /> : <AIIcon />}
                      onClick={handleScriptGeneration}
                      disabled={loading.scriptgen || !scriptGenForm.topic.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.scriptgen ? 'Generating...' : 'Generate Script'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Script Features
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="AI-Powered Content" variant="outlined" size="small" />
                      <Chip label="Multiple Script Types" variant="outlined" size="small" />
                      <Chip label="Optimized for TTS" variant="outlined" size="small" />
                      <Chip label="Viral Content Focus" variant="outlined" size="small" />
                      <Chip label="Duration Targeting" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('scriptgen', results.scriptgen, <AIIcon />)}
          </TabPanel>

          {/* Video Search Tab */}
          <TabPanel value={tabValue} index={1}>
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ mb: 2 }}>
                AI-Powered Video Search
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                    <CardContent>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Smart Search Queries
                      </Typography>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Topic"
                            placeholder="AI and machine learning"
                            value={videoSearchForm.topic}
                            onChange={(e) => setVideoSearchForm({ ...videoSearchForm, topic: e.target.value })}
                            helperText="AI will generate optimized search queries"
                          />
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <FormControl fullWidth>
                            <InputLabel>Orientation</InputLabel>
                            <Select
                              value={videoSearchForm.orientation}
                              label="Orientation"
                              onChange={(e) => setVideoSearchForm({ ...videoSearchForm, orientation: e.target.value })}
                            >
                              <MenuItem value="portrait">Portrait (9:16)</MenuItem>
                              <MenuItem value="landscape">Landscape (16:9)</MenuItem>
                              <MenuItem value="square">Square (1:1)</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <FormControl fullWidth>
                            <InputLabel>Language</InputLabel>
                            <Select
                              value={videoSearchForm.language}
                              label="Language"
                              onChange={(e) => setVideoSearchForm({ ...videoSearchForm, language: e.target.value })}
                            >
                              <MenuItem value="en">English</MenuItem>
                              <MenuItem value="es">Spanish</MenuItem>
                              <MenuItem value="fr">French</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>

                      <Button
                        variant="contained"
                        size="large"
                        startIcon={loading.videosearch ? <CircularProgress size={20} /> : <AIIcon />}
                        onClick={handleVideoSearch}
                        disabled={loading.videosearch || !videoSearchForm.topic.trim()}
                        sx={{ mt: 2, px: 3 }}
                      >
                        {loading.videosearch ? 'Searching...' : 'Generate Search Queries'}
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                    <CardContent>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Manual Video Browse
                      </Typography>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="Search Query"
                            placeholder="technology, innovation, startup"
                            value={manualSearchForm.query}
                            onChange={(e) => setManualSearchForm({ ...manualSearchForm, query: e.target.value })}
                            helperText="Direct search in Pexels database"
                          />
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <FormControl fullWidth>
                            <InputLabel>Orientation</InputLabel>
                            <Select
                              value={manualSearchForm.orientation}
                              label="Orientation"
                              onChange={(e) => setManualSearchForm({ ...manualSearchForm, orientation: e.target.value })}
                            >
                              <MenuItem value="landscape">Landscape</MenuItem>
                              <MenuItem value="portrait">Portrait</MenuItem>
                              <MenuItem value="square">Square</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <TextField
                            fullWidth
                            type="number"
                            label="Results Per Page"
                            value={manualSearchForm.per_page}
                            onChange={(e) => setManualSearchForm({ ...manualSearchForm, per_page: parseInt(e.target.value) })}
                            inputProps={{ min: 5, max: 80 }}
                          />
                        </Grid>
                      </Grid>

                      <Button
                        variant="outlined"
                        size="large"
                        startIcon={loading.manualsearch ? <CircularProgress size={20} /> : <SearchIcon />}
                        onClick={handleManualVideoSearch}
                        disabled={loading.manualsearch || !manualSearchForm.query.trim()}
                        sx={{ mt: 2, px: 3 }}
                      >
                        {loading.manualsearch ? 'Searching...' : 'Browse Videos'}
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>

            {renderJobResult('videosearch', results.videosearch, <SearchIcon />)}
            {renderJobResult('manualsearch', results.manualsearch, <VideoIcon />)}
          </TabPanel>

          {/* Scene Builder Tab */}
          <TabPanel value={tabValue} index={2}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ScenesIcon color="primary" />
                  Scene-Based Video Creator
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                      Video Scenes
                    </Typography>
                    {scenesForm.scenes.map((scene, sceneIndex) => (
                      <Accordion key={sceneIndex} sx={{ mb: 2 }}>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="subtitle2">
                            Scene {sceneIndex + 1} {scene.text && `- "${scene.text.slice(0, 30)}..."`}
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                rows={3}
                                label="Scene Text"
                                placeholder="Enter the narration text for this scene..."
                                value={scene.text}
                                onChange={(e) => updateScene(sceneIndex, 'text', e.target.value)}
                              />
                            </Grid>

                            <Grid item xs={12} md={8}>
                              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                                Search Terms for Background Video
                              </Typography>
                              {scene.searchTerms.map((term, termIndex) => (
                                <Box key={termIndex} sx={{ display: 'flex', gap: 1, mb: 1 }}>
                                  <TextField
                                    fullWidth
                                    size="small"
                                    placeholder="e.g., technology, innovation"
                                    value={term}
                                    onChange={(e) => updateSearchTerm(sceneIndex, termIndex, e.target.value)}
                                  />
                                  {scene.searchTerms.length > 1 && (
                                    <Button 
                                      variant="outlined" 
                                      color="error"
                                      size="small"
                                      onClick={() => removeSearchTerm(sceneIndex, termIndex)}
                                      sx={{ minWidth: 'auto', px: 1 }}
                                    >
                                      ✕
                                    </Button>
                                  )}
                                </Box>
                              ))}
                              <Button 
                                variant="outlined" 
                                size="small"
                                onClick={() => addSearchTerm(sceneIndex)}
                                sx={{ mt: 1 }}
                              >
                                + Add Search Term
                              </Button>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Duration (seconds)"
                                value={scene.duration}
                                onChange={(e) => updateScene(sceneIndex, 'duration', parseFloat(e.target.value))}
                                inputProps={{ min: 1, max: 30, step: 0.5 }}
                              />
                            </Grid>

                            {scenesForm.scenes.length > 1 && (
                              <Grid item xs={12}>
                                <Button 
                                  variant="outlined" 
                                  color="error"
                                  onClick={() => removeScene(sceneIndex)}
                                >
                                  Remove Scene
                                </Button>
                              </Grid>
                            )}
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    ))}
                    
                    <Button 
                      variant="outlined" 
                      onClick={addScene}
                      sx={{ mt: 1 }}
                    >
                      + Add Scene
                    </Button>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Voice Provider</InputLabel>
                      <Select
                        value={scenesForm.voice_provider}
                        label="Voice Provider"
                        onChange={(e) => setScenesForm({ ...scenesForm, voice_provider: e.target.value })}
                      >
                        <MenuItem value="kokoro">Kokoro TTS</MenuItem>
                        <MenuItem value="edge">Edge TTS</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <TextField
                      fullWidth
                      label="Voice Name"
                      value={scenesForm.voice_name}
                      onChange={(e) => setScenesForm({ ...scenesForm, voice_name: e.target.value })}
                      placeholder="af_bella, en_male_1, etc."
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Resolution</InputLabel>
                      <Select
                        value={scenesForm.resolution}
                        label="Resolution"
                        onChange={(e) => setScenesForm({ ...scenesForm, resolution: e.target.value })}
                      >
                        <MenuItem value="1080x1920">1080x1920 (Portrait)</MenuItem>
                        <MenuItem value="1920x1080">1920x1080 (Landscape)</MenuItem>
                        <MenuItem value="1080x1080">1080x1080 (Square)</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.scenes ? <CircularProgress size={20} /> : <ScenesIcon />}
                  onClick={handleScenesVideo}
                  disabled={loading.scenes || scenesForm.scenes.every(s => !s.text.trim())}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.scenes ? 'Creating Video...' : 'Create Video from Scenes'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('scenes', results.scenes, <ScenesIcon />)}
          </TabPanel>

          {/* News Research Tab */}
          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <NewsIcon color="primary" />
                      News Research
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Search Term"
                          placeholder="artificial intelligence, climate change, technology trends..."
                          value={newsResearchForm.searchTerm}
                          onChange={(e) => setNewsResearchForm({ ...newsResearchForm, searchTerm: e.target.value })}
                          helperText="Enter topics to research current news and trends"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <FormControl fullWidth>
                          <InputLabel>Language</InputLabel>
                          <Select
                            value={newsResearchForm.targetLanguage}
                            label="Language"
                            onChange={(e) => setNewsResearchForm({ ...newsResearchForm, targetLanguage: e.target.value })}
                          >
                            <MenuItem value="en">English</MenuItem>
                            <MenuItem value="es">Spanish</MenuItem>
                            <MenuItem value="fr">French</MenuItem>
                            <MenuItem value="de">German</MenuItem>
                            <MenuItem value="it">Italian</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Max Results"
                          value={newsResearchForm.maxResults}
                          onChange={(e) => setNewsResearchForm({ ...newsResearchForm, maxResults: parseInt(e.target.value) })}
                          inputProps={{ min: 1, max: 20 }}
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.news ? <CircularProgress size={20} /> : <NewsIcon />}
                      onClick={handleNewsResearch}
                      disabled={loading.news || !newsResearchForm.searchTerm.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.news ? 'Researching...' : 'Research News'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Research Features
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Real-time News" variant="outlined" size="small" />
                      <Chip label="Multiple Sources" variant="outlined" size="small" />
                      <Chip label="Content Ideas" variant="outlined" size="small" />
                      <Chip label="Trending Topics" variant="outlined" size="small" />
                      <Chip label="Multi-language" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('news', results.news, <NewsIcon />)}
          </TabPanel>

          {/* AI Chat Tab */}
          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ChatIcon color="primary" />
                      AI Assistant
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={4}
                          label="Prompt"
                          placeholder="Ask anything about content creation, video ideas, script improvements..."
                          value={aiChatForm.prompt}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, prompt: e.target.value })}
                          helperText="Get AI assistance for creative ideas, content planning, and more"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>AI Provider</InputLabel>
                          <Select
                            value={aiChatForm.provider}
                            label="AI Provider"
                            onChange={(e) => setAiChatForm({ ...aiChatForm, provider: e.target.value })}
                          >
                            <MenuItem value="auto">Auto (Best Available)</MenuItem>
                            <MenuItem value="openai">OpenAI GPT</MenuItem>
                            <MenuItem value="groq">Groq Llama</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Max Tokens"
                          value={aiChatForm.max_tokens}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, max_tokens: parseInt(e.target.value) })}
                          inputProps={{ min: 100, max: 4000 }}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Temperature"
                          value={aiChatForm.temperature}
                          onChange={(e) => setAiChatForm({ ...aiChatForm, temperature: parseFloat(e.target.value) })}
                          inputProps={{ min: 0, max: 2, step: 0.1 }}
                          helperText="0=Focused, 1=Creative"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.aichat ? <CircularProgress size={20} /> : <ChatIcon />}
                      onClick={handleAIChat}
                      disabled={loading.aichat || !aiChatForm.prompt.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.aichat ? 'Thinking...' : 'Ask AI'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      AI Capabilities
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Content Planning" variant="outlined" size="small" />
                      <Chip label="Script Enhancement" variant="outlined" size="small" />
                      <Chip label="Creative Ideas" variant="outlined" size="small" />
                      <Chip label="SEO Optimization" variant="outlined" size="small" />
                      <Chip label="Trend Analysis" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('aichat', results.aichat, <ChatIcon />)}
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default AIVideoTools;