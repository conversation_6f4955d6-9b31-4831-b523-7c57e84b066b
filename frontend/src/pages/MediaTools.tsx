import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow
} from '@mui/material';
import {
  CloudDownload as DownloadIcon,
  Transform as ConvertIcon,
  Info as MetadataIcon,
  YouTube as YouTubeIcon,
  ExpandMore as ExpandMoreIcon,
  AudioFile as AudioIcon,
  VideoFile as VideoIcon,
  Image as ImageIcon,
  VideoLibrary as LibraryIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`media-tools-tabpanel-${index}`}
      aria-labelledby={`media-tools-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface MediaMetadata {
  filesize?: number;
  filesize_mb?: number;
  duration?: number;
  duration_formatted?: string;
  format?: string;
  overall_bitrate?: number;
  overall_bitrate_mbps?: number;
  has_video?: boolean;
  has_audio?: boolean;
  video_codec?: string;
  video_codec_long?: string;
  width?: number;
  height?: number;
  resolution?: string;
  fps?: number;
  video_bitrate?: number;
  video_bitrate_mbps?: number;
  pixel_format?: string;
  audio_codec?: string;
  audio_codec_long?: string;
  audio_channels?: number;
  audio_sample_rate?: number;
  audio_sample_rate_khz?: number;
  audio_bitrate?: number;
  audio_bitrate_kbps?: number;
  [key: string]: string | number | boolean | undefined;
}

interface SupportedFormats {
  [category: string]: {
    [format: string]: {
      codec?: string;
      description?: string;
    };
  };
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    file_url?: string;
    supported_formats?: SupportedFormats;
    metadata?: MediaMetadata;
    transcript?: string;
    [key: string]: string | number | boolean | SupportedFormats | MediaMetadata | undefined;
  };
  error?: string | null;
}

const MediaTools: React.FC = () => {
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, JobResult | null>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});
  const [jobStatuses, setJobStatuses] = useState<Record<string, string>>({});

  // Form states for different tools
  const [downloadForm, setDownloadForm] = useState({
    url: '',
    file_name: '',
    cookies_url: ''
  });

  const [conversionForm, setConversionForm] = useState({
    input_url: '',
    output_format: 'mp3',
    quality: 'medium',
    custom_options: ''
  });

  const [metadataForm, setMetadataForm] = useState({
    media_url: ''
  });

  const [transcriptForm, setTranscriptForm] = useState({
    url: '',
    translate_to: ''
  });

  const [supportedFormats, setSupportedFormats] = useState<{
    supported_formats?: SupportedFormats;
    quality_presets?: string[];
    total_formats?: number;
  } | null>(null);

  // Generic job polling function
  const pollJobStatus = async (jobId: string, endpoint: string, toolName: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatuses(prev => ({ ...prev, [toolName]: status }));

        if (status === 'completed') {
          setResults(prev => ({ ...prev, [toolName]: { job_id: jobId, result: jobResult, status: 'completed' } }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        } else if (status === 'failed') {
          setErrors(prev => ({ ...prev, [toolName]: jobError || 'Job failed' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Job polling timeout' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Failed to check job status' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      }
    };

    poll();
  };

  // Fetch supported formats on component mount
  React.useEffect(() => {
    const fetchFormats = async () => {
      try {
        const response = await directApi.get('/api/v1/conversions/formats');
        setSupportedFormats(response.data);
      } catch (error) {
        console.error('Failed to fetch supported formats:', error);
      }
    };

    fetchFormats();
  }, []);

  const handleDownload = async () => {
    if (!downloadForm.url.trim()) {
      setErrors(prev => ({ ...prev, download: 'URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, download: true }));
    setErrors(prev => ({ ...prev, download: null }));
    setResults(prev => ({ ...prev, download: null }));

    try {
      const response = await directApi.post('/api/v1/media/download', downloadForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/media/download', 'download');
      } else {
        setErrors(prev => ({ ...prev, download: 'Failed to create download job' }));
        setLoading(prev => ({ ...prev, download: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, download: errorMessage }));
      setLoading(prev => ({ ...prev, download: false }));
    }
  };

  const handleConversion = async () => {
    if (!conversionForm.input_url.trim()) {
      setErrors(prev => ({ ...prev, conversion: 'URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, conversion: true }));
    setErrors(prev => ({ ...prev, conversion: null }));
    setResults(prev => ({ ...prev, conversion: null }));

    try {
      const response = await directApi.post('/api/v1/conversions/', conversionForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/conversions', 'conversion');
      } else {
        setErrors(prev => ({ ...prev, conversion: 'Failed to create conversion job' }));
        setLoading(prev => ({ ...prev, conversion: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, conversion: errorMessage }));
      setLoading(prev => ({ ...prev, conversion: false }));
    }
  };

  const handleMetadata = async () => {
    if (!metadataForm.media_url.trim()) {
      setErrors(prev => ({ ...prev, metadata: 'Media URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, metadata: true }));
    setErrors(prev => ({ ...prev, metadata: null }));
    setResults(prev => ({ ...prev, metadata: null }));

    try {
      const response = await directApi.post('/api/v1/media/metadata', metadataForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/media/metadata', 'metadata');
      } else {
        setErrors(prev => ({ ...prev, metadata: 'Failed to create metadata job' }));
        setLoading(prev => ({ ...prev, metadata: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, metadata: errorMessage }));
      setLoading(prev => ({ ...prev, metadata: false }));
    }
  };

  const handleTranscript = async () => {
    if (!transcriptForm.url.trim()) {
      setErrors(prev => ({ ...prev, transcript: 'URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, transcript: true }));
    setErrors(prev => ({ ...prev, transcript: null }));
    setResults(prev => ({ ...prev, transcript: null }));

    try {
      const response = await directApi.post('/api/v1/media/youtube-transcripts', transcriptForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/media/youtube-transcripts', 'transcript');
      } else {
        setErrors(prev => ({ ...prev, transcript: 'Failed to create transcript job' }));
        setLoading(prev => ({ ...prev, transcript: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, transcript: errorMessage }));
      setLoading(prev => ({ ...prev, transcript: false }));
    }
  };

  const renderJobResult = (toolName: string, result: JobResult | null, icon: React.ReactNode) => {
    if (!result && !loading[toolName] && !errors[toolName]) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {toolName.charAt(0).toUpperCase() + toolName.slice(1)} Result
            {loading[toolName] && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading[toolName] && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatuses[toolName] || 'Processing...'}
              </Typography>
            </Box>
          )}

          {errors[toolName] && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors[toolName]}
            </Alert>
          )}

          {result && jobStatuses[toolName] === 'completed' && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 {toolName.charAt(0).toUpperCase() + toolName.slice(1)} completed successfully!
              </Alert>

              {/* Download Link */}
              {result.result.file_url && (
                <Box sx={{ mb: 2, display: 'flex', gap: 1 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    href={result.result.file_url}
                    target="_blank"
                    variant="contained"
                    size="small"
                  >
                    Download Result
                  </Button>
                  <Button
                    startIcon={<LibraryIcon />}
                    onClick={() => navigate('/dashboard/library')}
                    variant="outlined"
                    size="small"
                    color="primary"
                  >
                    View in Library
                  </Button>
                </Box>
              )}

              {/* Metadata Display */}
              {(result.result.metadata || (toolName === 'metadata' && result.result)) && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">Media Metadata</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableBody>
                          {Object.entries(result.result.metadata || result.result).map(([key, value]) => (
                            <TableRow key={key}>
                              <TableCell component="th" scope="row" sx={{ fontWeight: 600, width: '30%' }}>
                                {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              </TableCell>
                              <TableCell>
                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </AccordionDetails>
                </Accordion>
              )}

              {/* Transcript Display */}
              {result.result.transcript && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600 }}>
                    Transcript:
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 300, overflow: 'auto' }}>
                    <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.transcript}
                    </Typography>
                  </Paper>
                </Box>
              )}

              {/* Supported Formats Display */}
              {result.result.supported_formats && (
                <Accordion>
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Typography variant="subtitle1">Supported Formats</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    <Grid container spacing={2}>
                      {Object.entries(result.result.supported_formats).map(([category, formats]: [string, SupportedFormats[string]]) => (
                        <Grid item xs={12} md={4} key={category}>
                          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                            {category.charAt(0).toUpperCase() + category.slice(1)}
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                            {Object.keys(formats).map((format) => (
                              <Chip key={format} label={format} size="small" variant="outlined" />
                            ))}
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </AccordionDetails>
                </Accordion>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Media Tools 🛠️
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Download, convert, analyze, and extract transcripts from media files.
        </Typography>
      </Box>

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flexGrow: 1 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ px: 3 }}
          >
            <Tab icon={<DownloadIcon />} label="Media Download" />
            <Tab icon={<ConvertIcon />} label="Format Conversion" />
            <Tab icon={<MetadataIcon />} label="Metadata Extraction" />
            <Tab icon={<YouTubeIcon />} label="YouTube Transcripts" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Media Download Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <DownloadIcon color="primary" />
                      Download Media from URL
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Media URL"
                          placeholder="https://www.youtube.com/watch?v=... or direct media URL"
                          value={downloadForm.url}
                          onChange={(e) => setDownloadForm({ ...downloadForm, url: e.target.value })}
                          helperText="Supports YouTube, direct media URLs, and streaming links"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="File Name (Optional)"
                          placeholder="my_video.mp4"
                          value={downloadForm.file_name}
                          onChange={(e) => setDownloadForm({ ...downloadForm, file_name: e.target.value })}
                          helperText="Leave empty for auto-generated name"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Cookies URL (Optional)"
                          placeholder="https://example.com/cookies.txt"
                          value={downloadForm.cookies_url}
                          onChange={(e) => setDownloadForm({ ...downloadForm, cookies_url: e.target.value })}
                          helperText="For authentication with private content"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.download ? <CircularProgress size={20} /> : <DownloadIcon />}
                      onClick={handleDownload}
                      disabled={loading.download || !downloadForm.url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.download ? 'Downloading...' : 'Download Media'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Supported Sources
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip icon={<YouTubeIcon />} label="YouTube Videos" variant="outlined" />
                      <Chip icon={<VideoIcon />} label="Direct Video URLs" variant="outlined" />
                      <Chip icon={<AudioIcon />} label="Audio Streams" variant="outlined" />
                      <Chip icon={<ImageIcon />} label="Image Files" variant="outlined" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('download', results.download, <DownloadIcon />)}
          </TabPanel>

          {/* Format Conversion Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ConvertIcon color="primary" />
                      Convert Media Format
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Media URL"
                          placeholder="https://example.com/video.mp4"
                          value={conversionForm.input_url}
                          onChange={(e) => setConversionForm({ ...conversionForm, input_url: e.target.value })}
                          helperText="URL of the media file to convert"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Output Format</InputLabel>
                          <Select
                            value={conversionForm.output_format}
                            label="Output Format"
                            onChange={(e) => setConversionForm({ ...conversionForm, output_format: e.target.value })}
                          >
                            <MenuItem value="mp3">MP3 (Audio)</MenuItem>
                            <MenuItem value="wav">WAV (Audio)</MenuItem>
                            <MenuItem value="mp4">MP4 (Video)</MenuItem>
                            <MenuItem value="webm">WebM (Video)</MenuItem>
                            <MenuItem value="avi">AVI (Video)</MenuItem>
                            <MenuItem value="mov">MOV (Video)</MenuItem>
                            <MenuItem value="jpg">JPEG (Image)</MenuItem>
                            <MenuItem value="png">PNG (Image)</MenuItem>
                            <MenuItem value="webp">WebP (Image)</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Quality</InputLabel>
                          <Select
                            value={conversionForm.quality}
                            label="Quality"
                            onChange={(e) => setConversionForm({ ...conversionForm, quality: e.target.value })}
                          >
                            <MenuItem value="low">Low</MenuItem>
                            <MenuItem value="medium">Medium</MenuItem>
                            <MenuItem value="high">High</MenuItem>
                            <MenuItem value="lossless">Lossless</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Custom Options"
                          placeholder="-vf scale=1280:-1"
                          value={conversionForm.custom_options}
                          onChange={(e) => setConversionForm({ ...conversionForm, custom_options: e.target.value })}
                          helperText="FFmpeg options"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.conversion ? <CircularProgress size={20} /> : <ConvertIcon />}
                      onClick={handleConversion}
                      disabled={loading.conversion || !conversionForm.input_url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.conversion ? 'Converting...' : 'Convert Media'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Conversion Info
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Convert between 50+ media formats using FFmpeg.
                    </Typography>
                    {supportedFormats && (
                      <Typography variant="caption" color="text.secondary">
                        {supportedFormats.total_formats} formats supported
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('conversion', results.conversion, <ConvertIcon />)}
          </TabPanel>

          {/* Metadata Extraction Tab */}
          <TabPanel value={tabValue} index={2}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <MetadataIcon color="primary" />
                      Extract Media Metadata
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Media URL"
                          placeholder="https://example.com/video.mp4"
                          value={metadataForm.media_url}
                          onChange={(e) => setMetadataForm({ ...metadataForm, media_url: e.target.value })}
                          helperText="URL of the media file to analyze"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.metadata ? <CircularProgress size={20} /> : <MetadataIcon />}
                      onClick={handleMetadata}
                      disabled={loading.metadata || !metadataForm.media_url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.metadata ? 'Analyzing...' : 'Extract Metadata'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Metadata Info
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Extracts comprehensive information including:
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                      <Typography variant="caption">• Duration & file size</Typography>
                      <Typography variant="caption">• Video resolution & codec</Typography>
                      <Typography variant="caption">• Audio channels & bitrate</Typography>
                      <Typography variant="caption">• Format & technical details</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('metadata', results.metadata, <MetadataIcon />)}
          </TabPanel>

          {/* YouTube Transcripts Tab */}
          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <YouTubeIcon color="primary" />
                      Extract YouTube Transcripts
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="YouTube URL"
                          placeholder="https://www.youtube.com/watch?v=..."
                          value={transcriptForm.url}
                          onChange={(e) => setTranscriptForm({ ...transcriptForm, url: e.target.value })}
                          helperText="YouTube video URL to extract transcript from"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Translate To (Optional)"
                          placeholder="en, es, fr, de, etc."
                          value={transcriptForm.translate_to}
                          onChange={(e) => setTranscriptForm({ ...transcriptForm, translate_to: e.target.value })}
                          helperText="Language code for translation"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.transcript ? <CircularProgress size={20} /> : <YouTubeIcon />}
                      onClick={handleTranscript}
                      disabled={loading.transcript || !transcriptForm.url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.transcript ? 'Extracting...' : 'Extract Transcript'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Transcript Features
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Extract existing captions and subtitles from YouTube videos.
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                      <Typography variant="caption">• Multiple language support</Typography>
                      <Typography variant="caption">• Auto-generated captions</Typography>
                      <Typography variant="caption">• Translation capabilities</Typography>
                      <Typography variant="caption">• Clean text output</Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('transcript', results.transcript, <YouTubeIcon />)}
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default MediaTools;