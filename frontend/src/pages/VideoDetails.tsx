import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Grid,
  LinearProgress,
  Card,
  CardContent,
  Chip
} from '@mui/material';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import DownloadIcon from '@mui/icons-material/Download';
import RefreshIcon from '@mui/icons-material/Refresh';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import ShareIcon from '@mui/icons-material/Share';
import ReplayIcon from '@mui/icons-material/Replay';
import ScheduleIcon from '@mui/icons-material/Schedule';

import { mcpApi, directApi, apiUtils } from '../utils/api';
import { Job, JobStatus } from '../types/ouinhi';
import { PostizScheduleDialog } from '../components/PostizScheduleDialog';

const VideoDetails: React.FC = () => {
  const { videoId } = useParams<{ videoId: string }>();
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [job, setJob] = useState<Job | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [postizDialogOpen, setPostizDialogOpen] = useState(false);
  
  const intervalRef = useRef<number | null>(null);
  const isMounted = useRef(true);

  const checkVideoStatus = useCallback(async (showLoader = false) => {
    if (!videoId) return;
    
    try {
      if (showLoader) setRefreshing(true);

      let currentJob: Job;
      
      try {
        // Try MCP API first
        const mcpResponse = await mcpApi.getVideoStatus(videoId);
        if (mcpResponse.error) {
          throw new Error(mcpResponse.error.message);
        }
        currentJob = mcpResponse.result as Job;
      } catch (mcpError) {
        // Fallback to direct API
        const directResponse = await directApi.getJobStatus(videoId);
        if (!directResponse.success || !directResponse.data) {
          throw new Error(directResponse.error || 'Failed to get job status');
        }
        currentJob = directResponse.data;
      }

      if (isMounted.current) {
        setJob(currentJob);
        setError(null);
        
        // Stop polling if job is complete or failed
        if (currentJob.status === JobStatus.COMPLETED || currentJob.status === JobStatus.FAILED) {
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
        }
      }
    } catch (err: unknown) {
      if (isMounted.current) {
        setError(apiUtils.formatError(err));
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
      }
    } finally {
      if (isMounted.current) {
        setLoading(false);
        if (showLoader) setRefreshing(false);
      }
    }
  }, [videoId]);

  useEffect(() => {
    if (!videoId) {
      setError('No video ID provided');
      setLoading(false);
      return;
    }

    // Initial load
    checkVideoStatus();
    
    // Start polling for status updates
    intervalRef.current = setInterval(() => {
      checkVideoStatus();
    }, 5000); // Poll every 5 seconds
    
    return () => {
      isMounted.current = false;
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [videoId, checkVideoStatus]);

  const handleBack = () => {
    navigate('/dashboard/library');
  };

  const handleRefresh = () => {
    checkVideoStatus(true);
  };

  const handleDownload = () => {
    if (job?.result?.final_video_url || job?.result?.video_url) {
      const url = job.result.final_video_url || job.result.video_url;
      const link = document.createElement('a');
      link.href = url;
      link.download = `video-${videoId}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleRetry = async () => {
    if (!videoId || !job || job.status !== JobStatus.FAILED) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/jobs/${videoId}/retry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': localStorage.getItem('ouinhi_api_key') || ''
        }
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || 'Failed to retry job');
      }
      
      // Start polling again after successful retry
      intervalRef.current = setInterval(() => {
        checkVideoStatus();
      }, 5000);
      
      // Immediately check status
      checkVideoStatus();
      
    } catch (err: unknown) {
      setError(`Failed to retry job: ${apiUtils.formatError(err)}`);
      setLoading(false);
    }
  };

  interface ScheduleData {
    jobId: string;
    content: string;
    integrations: string[];
    postType: string;
    scheduleDate?: Date;
    tags: string[];
  }

  const handlePostizSchedule = async (scheduleData: ScheduleData) => {
    // Call the Postiz job scheduling API
    const requestData = {
      job_id: scheduleData.jobId,
      content: scheduleData.content,
      integrations: scheduleData.integrations,
      post_type: scheduleData.postType,
      schedule_date: scheduleData.scheduleDate,
      tags: scheduleData.tags
    };

    const response = await fetch('/api/v1/postiz/schedule-job', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': localStorage.getItem('ouinhi_api_key') || ''
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to schedule post');
    }

    await response.json();
    
    setPostizDialogOpen(false);
    // TODO: Add success notification
  };

  const getStatusColor = (status: JobStatus) => {
    switch (status) {
      case JobStatus.COMPLETED:
        return 'success';
      case JobStatus.PROCESSING:
        return 'primary';
      case JobStatus.FAILED:
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusIcon = (status: JobStatus) => {
    switch (status) {
      case JobStatus.PROCESSING:
        return <CircularProgress size={16} />;
      case JobStatus.COMPLETED:
        return <PlayArrowIcon />;
      default:
        return undefined;
    }
  };

  const renderVideoPlayer = () => {
    if (!job?.result) return null;
    
    const videoUrl = job.result.final_video_url || job.result.video_url;
    if (!videoUrl) return null;

    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Generated Video
          </Typography>
          <Box sx={{ 
            position: 'relative', 
            paddingTop: '56.25%', // 16:9 aspect ratio
            mb: 2,
            backgroundColor: '#000',
            borderRadius: 1,
            overflow: 'hidden'
          }}>
            <video
              controls
              autoPlay={false}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: '100%',
              }}
              src={videoUrl}
            >
              Your browser does not support the video tag.
            </video>
          </Box>
          
          <Box display="flex" gap={2} flexWrap="wrap">
            <Button 
              variant="contained"
              color="primary" 
              startIcon={<DownloadIcon />}
              onClick={handleDownload}
            >
              Download Video
            </Button>
            
            <Button 
              variant="outlined"
              startIcon={<ShareIcon />}
              onClick={() => {
                navigator.clipboard.writeText(videoUrl);
                // Could add a toast notification here
              }}
            >
              Copy Link
            </Button>

            <Button
              variant="outlined"
              color="secondary"
              startIcon={<ScheduleIcon />}
              onClick={() => setPostizDialogOpen(true)}
            >
              Schedule Post
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  };

  const renderJobDetails = () => {
    if (!job) return null;

    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Job Details
          </Typography>
          
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Job ID
              </Typography>
              <Typography variant="body1" fontFamily="monospace">
                {videoId}
              </Typography>
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" color="text.secondary">
                Status
              </Typography>
              <Box display="flex" alignItems="center" gap={1}>
                <Chip 
                  label={job.status} 
                  color={getStatusColor(job.status)}
                  icon={getStatusIcon(job.status)}
                  size="small"
                />
                {job.progress !== undefined && job.status === JobStatus.PROCESSING && (
                  <Typography variant="body2" color="text.secondary">
                    {job.progress}%
                  </Typography>
                )}
              </Box>
            </Grid>
            
            {job.result?.video_duration && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Duration
                </Typography>
                <Typography variant="body1">
                  {Math.round(job.result.video_duration)} seconds
                </Typography>
              </Grid>
            )}
            
            {job.result?.word_count && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Script Word Count
                </Typography>
                <Typography variant="body1">
                  {job.result.word_count} words
                </Typography>
              </Grid>
            )}
            
            {job.result?.segments_count && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Video Segments
                </Typography>
                <Typography variant="body1">
                  {job.result.segments_count} segments
                </Typography>
              </Grid>
            )}
            
            {job.result?.processing_time && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Processing Time
                </Typography>
                <Typography variant="body1">
                  {Math.round(job.result.processing_time)} seconds
                </Typography>
              </Grid>
            )}
          </Grid>
        </CardContent>
      </Card>
    );
  };

  const renderContent = () => {
    if (loading && !job) {
      return (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="40vh">
          <CircularProgress size={60} />
        </Box>
      );
    }

    if (error && !job) {
      return <Alert severity="error">{error}</Alert>;
    }

    if (!job) {
      return <Alert severity="info">No job information found.</Alert>;
    }

    return (
      <>
        {/* Progress Bar for Processing */}
        {job.status === JobStatus.PROCESSING && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Creating Your Video...</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={refreshing ? <CircularProgress size={16} /> : <RefreshIcon />}
                  onClick={handleRefresh}
                  disabled={refreshing}
                >
                  Refresh
                </Button>
              </Box>
              
              <LinearProgress 
                variant={job.progress !== undefined ? "determinate" : "indeterminate"}
                value={job.progress}
                sx={{ mb: 1 }}
              />
              
              <Typography variant="body2" color="text.secondary" align="center">
                {job.progress !== undefined 
                  ? `${job.progress}% complete`
                  : 'Processing... This may take a few minutes.'
                }
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Video Player */}
        {job.status === JobStatus.COMPLETED && renderVideoPlayer()}

        {/* Error Message */}
        {job.status === JobStatus.FAILED && (
          <Alert 
            severity="error" 
            sx={{ mb: 3 }}
            action={
              <Button
                color="inherit"
                size="small"
                startIcon={<ReplayIcon />}
                onClick={handleRetry}
                disabled={loading}
                variant="outlined"
                sx={{ mr: 1 }}
              >
                Retry
              </Button>
            }
          >
            Video creation failed: {job.error || 'Unknown error occurred'}
            <Typography variant="body2" sx={{ mt: 1, opacity: 0.8 }}>
              Click "Retry" to attempt video creation again with the same parameters.
            </Typography>
          </Alert>
        )}

        {/* Job Details */}
        {renderJobDetails()}

        {/* Generated Script */}
        {job.result?.script_generated && (
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Generated Script
              </Typography>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                {job.result.script_generated}
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Additional Files */}
        {job.result && (
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Additional Files
              </Typography>
              
              <Box display="flex" gap={2} flexWrap="wrap">
                {job.result.audio_url && (
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => window.open(job.result.audio_url, '_blank')}
                  >
                    Audio Track
                  </Button>
                )}
                
                {job.result.srt_url && (
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => window.open(job.result.srt_url, '_blank')}
                  >
                    Subtitles (SRT)
                  </Button>
                )}
                
                {job.result.video_with_audio_url && (
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => window.open(job.result.video_with_audio_url, '_blank')}
                  >
                    Video (No Captions)
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        )}
      </>
    );
  };

  return (
    <Box maxWidth="lg" mx="auto" py={4}>
      <Box display="flex" alignItems="center" mb={4}>
        <Button 
          startIcon={<ArrowBackIcon />} 
          onClick={handleBack}
          sx={{ mr: 2 }}
        >
          Back to Videos
        </Button>
        <Typography variant="h4" component="h1">
          Video Details
        </Typography>
      </Box>

      {renderContent()}

      {/* Postiz Schedule Dialog */}
      <PostizScheduleDialog
        open={postizDialogOpen}
        onClose={() => setPostizDialogOpen(false)}
        job={job}
        onSchedule={handlePostizSchedule}
      />
    </Box>
  );
};

export default VideoDetails;