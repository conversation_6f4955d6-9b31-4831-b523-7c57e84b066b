import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>pography,
  <PERSON>rid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  TextField,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Pagination,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Menu,
  ListItemIcon,
  ListItemText,
  Alert,
  useTheme,
  alpha,
  Tooltip
} from '@mui/material';
import {
  Search,
  Download,
  MoreVert,
  Edit,
  Delete,
  PlayArrow,
  Subtitles,
  AudioFile,
  VideoFile,
  Schedule,
  Person,
  Language,
  FilterList
} from '@mui/icons-material';
import { directApi } from '../utils/api';

interface Video {
  id: string;
  title: string;
  description?: string;
  video_type: string;
  final_video_url: string;
  video_with_audio_url?: string;
  audio_url?: string;
  srt_url?: string;
  thumbnail_url?: string;
  duration_seconds?: number;
  resolution?: string;
  file_size_mb?: number;
  word_count?: number;
  segments_count?: number;
  script_text?: string;
  voice_provider?: string;
  voice_name?: string;
  language?: string;
  processing_time_seconds?: number;
  background_videos_used?: string[];
  tags?: string[];
  download_count: number;
  last_accessed?: string;
  created_at: string;
  updated_at: string;
}

const Videos: React.FC = () => {
  const theme = useTheme();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [videoTypeFilter, setVideoTypeFilter] = useState('');
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [videoDialogOpen, setVideoDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuVideoId, setMenuVideoId] = useState<string | null>(null);

  const pageSize = 12;

  const fetchVideos = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await directApi.getVideos({
        page,
        limit: pageSize,
        video_type: videoTypeFilter || undefined,
        search: searchQuery || undefined
      });

      if (response.success && response.data) {
        setVideos(response.data.videos);
        setTotalPages(Math.ceil(response.data.total / pageSize));
      } else {
        setError(response.error || 'Failed to fetch videos');
      }
    } catch (err: any) {
      setError('Failed to connect to server');
      console.error('Error fetching videos:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVideos();
  }, [page, videoTypeFilter]);

  const handleSearch = () => {
    setPage(1);
    fetchVideos();
  };

  const handleSearchKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleVideoClick = (video: Video) => {
    setSelectedVideo(video);
    setVideoDialogOpen(true);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, videoId: string) => {
    setAnchorEl(event.currentTarget);
    setMenuVideoId(videoId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuVideoId(null);
  };

  const handleDownload = async (videoId: string, format: string = 'mp4') => {
    try {
      const response = await directApi.getVideoDownloadUrl(videoId, format);
      if (response.success && response.data) {
        window.open(response.data.download_url, '_blank');
      }
    } catch (err) {
      console.error('Failed to get download URL:', err);
    }
    handleMenuClose();
  };

  const handleDelete = async (videoId: string) => {
    if (window.confirm('Are you sure you want to delete this video?')) {
      try {
        const response = await directApi.deleteVideo(videoId);
        if (response.success) {
          setVideos(videos.filter(v => v.id !== videoId));
        }
      } catch (err) {
        console.error('Failed to delete video:', err);
      }
    }
    handleMenuClose();
  };

  const formatDuration = (seconds?: number) => {
    if (!seconds) return 'Unknown';
    const mins = Math.floor(seconds / 60);
    const secs = Math.round(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (mb?: number) => {
    if (!mb) return 'Unknown';
    return mb > 1000 ? `${(mb / 1000).toFixed(1)}GB` : `${mb.toFixed(1)}MB`;
  };

  const getVideoTypeChipColor = (type: string) => {
    const colors: Record<string, any> = {
      'footage_to_video': 'primary',
      'aiimage_to_video': 'secondary', 
      'scenes_to_video': 'success',
      'short_video_creation': 'warning',
      'image_to_video': 'info',
      'other': 'default'
    };
    return colors[type] || 'default';
  };

  const getVideoTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'footage_to_video': 'Topic to Video',
      'aiimage_to_video': 'Script to Video',
      'scenes_to_video': 'Scenes to Video', 
      'short_video_creation': 'Short Video',
      'image_to_video': 'Image to Video',
      'other': 'Other'
    };
    return labels[type] || type;
  };

  const VideoCard: React.FC<{ video: Video }> = ({ video }) => (
    <Card 
      sx={{ 
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        cursor: 'pointer',
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8]
        }
      }}
      onClick={() => handleVideoClick(video)}
    >
      {video.thumbnail_url ? (
        <CardMedia
          component="img"
          height="200"
          image={video.thumbnail_url}
          alt={video.title}
          sx={{ objectFit: 'cover' }}
        />
      ) : (
        <Box
          sx={{
            height: 200,
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <VideoFile sx={{ fontSize: 60, color: theme.palette.primary.main }} />
        </Box>
      )}
      
      <CardContent sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
          <Typography 
            variant="h6" 
            component="div"
            sx={{ 
              fontWeight: 600,
              fontSize: '1rem',
              lineHeight: 1.3,
              flexGrow: 1,
              mr: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            {video.title}
          </Typography>
          <IconButton
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              handleMenuClick(e, video.id);
            }}
          >
            <MoreVert />
          </IconButton>
        </Box>

        <Chip
          label={getVideoTypeLabel(video.video_type)}
          color={getVideoTypeChipColor(video.video_type)}
          size="small"
          sx={{ alignSelf: 'flex-start', mb: 1 }}
        />

        {video.description && (
          <Typography 
            variant="body2" 
            color="text.secondary" 
            sx={{ 
              mb: 2,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical'
            }}
          >
            {video.description}
          </Typography>
        )}

        <Box sx={{ mt: 'auto' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <Schedule sx={{ fontSize: 16, color: 'text.secondary' }} />
              <Typography variant="caption" color="text.secondary">
                {formatDuration(video.duration_seconds)}
              </Typography>
            </Box>
            {video.file_size_mb && (
              <Typography variant="caption" color="text.secondary">
                {formatFileSize(video.file_size_mb)}
              </Typography>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            {video.voice_provider && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Person sx={{ fontSize: 14, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  {video.voice_provider}
                </Typography>
              </Box>
            )}
            {video.language && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Language sx={{ fontSize: 14, color: 'text.secondary' }} />
                <Typography variant="caption" color="text.secondary">
                  {video.language.toUpperCase()}
                </Typography>
              </Box>
            )}
          </Box>

          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
            Created: {new Date(video.created_at).toLocaleDateString()}
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Box>
          <Typography variant="h4" sx={{ fontWeight: 700, mb: 0.5 }}>
            Video Library
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Manage and download your generated videos
          </Typography>
        </Box>
      </Box>

      {/* Filters */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <TextField
          size="small"
          placeholder="Search videos..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onKeyPress={handleSearchKeyPress}
          InputProps={{
            startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />
          }}
          sx={{ minWidth: 300 }}
        />
        
        <FormControl size="small" sx={{ minWidth: 150 }}>
          <InputLabel>Video Type</InputLabel>
          <Select
            value={videoTypeFilter}
            label="Video Type"
            onChange={(e) => setVideoTypeFilter(e.target.value)}
          >
            <MenuItem value="">All Types</MenuItem>
            <MenuItem value="footage_to_video">Topic to Video</MenuItem>
            <MenuItem value="aiimage_to_video">Script to Video</MenuItem>
            <MenuItem value="scenes_to_video">Scenes to Video</MenuItem>
            <MenuItem value="short_video_creation">Short Video</MenuItem>
            <MenuItem value="image_to_video">Image to Video</MenuItem>
          </Select>
        </FormControl>

        <Button
          variant="contained"
          startIcon={<Search />}
          onClick={handleSearch}
        >
          Search
        </Button>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Loading */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Videos Grid */}
          {videos.length > 0 ? (
            <Grid container spacing={3}>
              {videos.map((video) => (
                <Grid item xs={12} sm={6} md={4} lg={3} key={video.id}>
                  <VideoCard video={video} />
                </Grid>
              ))}
            </Grid>
          ) : (
            <Box sx={{ textAlign: 'center', py: 8 }}>
              <VideoFile sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" gutterBottom>
                No videos found
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {searchQuery || videoTypeFilter 
                  ? 'Try adjusting your search or filters'
                  : 'Create your first video to see it here'
                }
              </Typography>
            </Box>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={(e, newPage) => setPage(newPage)}
                color="primary"
              />
            </Box>
          )}
        </>
      )}

      {/* Video Detail Dialog */}
      <Dialog
        open={videoDialogOpen}
        onClose={() => setVideoDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        {selectedVideo && (
          <>
            <DialogTitle>
              <Typography variant="h6" component="div">
                {selectedVideo.title}
              </Typography>
              <Chip
                label={getVideoTypeLabel(selectedVideo.video_type)}
                color={getVideoTypeChipColor(selectedVideo.video_type)}
                size="small"
                sx={{ mt: 1 }}
              />
            </DialogTitle>
            <DialogContent>
              {selectedVideo.description && (
                <Typography variant="body1" paragraph>
                  {selectedVideo.description}
                </Typography>
              )}
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Video Details
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Duration: {formatDuration(selectedVideo.duration_seconds)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Resolution: {selectedVideo.resolution || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    File Size: {formatFileSize(selectedVideo.file_size_mb)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Downloads: {selectedVideo.download_count}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" gutterBottom>
                    Generation Info
                  </Typography>
                  {selectedVideo.voice_provider && (
                    <Typography variant="body2" color="text.secondary">
                      Voice: {selectedVideo.voice_provider} - {selectedVideo.voice_name}
                    </Typography>
                  )}
                  {selectedVideo.language && (
                    <Typography variant="body2" color="text.secondary">
                      Language: {selectedVideo.language.toUpperCase()}
                    </Typography>
                  )}
                  {selectedVideo.word_count && (
                    <Typography variant="body2" color="text.secondary">
                      Word Count: {selectedVideo.word_count}
                    </Typography>
                  )}
                  {selectedVideo.segments_count && (
                    <Typography variant="body2" color="text.secondary">
                      Segments: {selectedVideo.segments_count}
                    </Typography>
                  )}
                </Grid>
              </Grid>

              {selectedVideo.script_text && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" gutterBottom>
                    Script
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color="text.secondary" 
                    sx={{ 
                      maxHeight: 200, 
                      overflow: 'auto', 
                      backgroundColor: 'grey.50', 
                      p: 2, 
                      borderRadius: 1 
                    }}
                  >
                    {selectedVideo.script_text}
                  </Typography>
                </Box>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setVideoDialogOpen(false)}>
                Close
              </Button>
              <Button
                variant="contained"
                startIcon={<Download />}
                onClick={() => handleDownload(selectedVideo.id)}
              >
                Download Video
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => menuVideoId && handleDownload(menuVideoId, 'mp4')}>
          <ListItemIcon>
            <VideoFile fontSize="small" />
          </ListItemIcon>
          <ListItemText>Download Video</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => menuVideoId && handleDownload(menuVideoId, 'mp4_no_captions')}>
          <ListItemIcon>
            <PlayArrow fontSize="small" />
          </ListItemIcon>
          <ListItemText>Download (No Captions)</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => menuVideoId && handleDownload(menuVideoId, 'audio')}>
          <ListItemIcon>
            <AudioFile fontSize="small" />
          </ListItemIcon>
          <ListItemText>Download Audio</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => menuVideoId && handleDownload(menuVideoId, 'srt')}>
          <ListItemIcon>
            <Subtitles fontSize="small" />
          </ListItemIcon>
          <ListItemText>Download Subtitles</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => menuVideoId && handleDelete(menuVideoId)} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <Delete fontSize="small" sx={{ color: 'error.main' }} />
          </ListItemIcon>
          <ListItemText>Delete Video</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default Videos;