import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress
} from '@mui/material';
import {
  Code as CodeIcon,
  PlayArrow as PlayIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

// Type definitions matching dahopevi API response
interface CodeExecutionResult {
  result?: unknown;      // The return value from the code
  stdout: string;        // Standard output
  stderr: string;        // Standard error
  exit_code: number;     // Exit code (0 = success)
}

interface ApiResult {
  job_id: string;
  status?: string;       // "pending" | "processing" | "completed" | "failed"
  result?: CodeExecutionResult;  // The execution result when completed
  error?: string | null; // Error message if failed
}

const CodeExecutor: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ApiResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);

  // Code execution state
  const [codeForm, setCodeForm] = useState({
    code: ''
  });

  // Job status polling function
  const pollJobStatus = async (jobId: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`/api/v1/code/execute/python/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Code execution completed successfully!');
          setResult({ job_id: jobId, result: jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Code execution failed');
          setError(jobError || 'Code execution failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  // Code execution result component
  const CodeExecutionResultComponent = ({ result }: { result: CodeExecutionResult }) => (
    <Box sx={{ mt: 3, mb: 3 }}>
      {/* Return Value */}
      {result.result !== undefined && result.result !== null && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ 
            mb: 1,
            color: '#00ff00',
            fontFamily: 'monospace'
          }}>🎯 Return Value:</Typography>
          <Paper sx={{ 
            p: 3, 
            bgcolor: '#0d1117', 
            border: '1px solid #30363d',
            maxHeight: 200, 
            overflow: 'auto' 
          }}>
            <Typography variant="body2" sx={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              color: '#58a6ff'
            }}>
              {typeof result.result === 'string' ? result.result : JSON.stringify(result.result, null, 2)}
            </Typography>
          </Paper>
        </Box>
      )}
      
      {/* Standard Output */}
      {result.stdout && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ 
            mb: 1,
            color: '#00ff00',
            fontFamily: 'monospace'
          }}>📤 Standard Output:</Typography>
          <Paper sx={{ 
            p: 3, 
            bgcolor: '#0d1117', 
            border: '1px solid #30363d',
            maxHeight: 300, 
            overflow: 'auto' 
          }}>
            <Typography variant="body2" sx={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
              color: '#c9d1d9'
            }}>
              {result.stdout}
            </Typography>
          </Paper>
        </Box>
      )}
      
      {/* Standard Error */}
      {result.stderr && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ 
            mb: 1,
            color: '#ff6b6b',
            fontFamily: 'monospace'
          }}>⚠️ Standard Error:</Typography>
          <Paper sx={{ 
            p: 3, 
            bgcolor: '#0d1117', 
            border: '1px solid #d73a49',
            maxHeight: 200, 
            overflow: 'auto' 
          }}>
            <Typography variant="body2" sx={{ 
              whiteSpace: 'pre-wrap', 
              fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace', 
              color: '#f85149'
            }}>
              {result.stderr}
            </Typography>
          </Paper>
        </Box>
      )}
      
      
      {/* Exit Code */}
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2" sx={{ 
          mb: 1,
          color: '#00ff00',
          fontFamily: 'monospace'
        }}>🔢 Exit Code:</Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
            color: result.exit_code === 0 ? '#00ff00' : '#f85149',
            fontWeight: 'bold',
            fontSize: '1rem'
          }}
        >
          {result.exit_code}
        </Typography>
      </Box>
    </Box>
  );

  const handleCodeSubmit = async () => {
    if (!codeForm.code.trim()) {
      setError('Python code is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const requestData = {
        code: codeForm.code
      };

      const response = await directApi.post('/api/v1/code/execute/python', requestData);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting code execution...');
        // Start polling for job status
        pollJobStatus(response.data.job_id);
      } else {
        setError('Failed to execute Python code');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8 // Add bottom padding for results section
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Python Code Executor 🐍
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Execute Python code in a secure sandboxed environment and see the results in real-time.
        </Typography>
      </Box>

      {/* Code Execution Section */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ 
                border: '1px solid #30363d', 
                backgroundColor: '#161b22',
                borderRadius: 2
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ 
                    mb: 3, 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: 1,
                    color: '#00ff00',
                    fontFamily: 'monospace',
                    backgroundColor: '#0a0a0a',
                    p: 2,
                    borderRadius: 1,
                    border: '1px solid #333'
                  }}>
                    <CodeIcon sx={{ color: '#00ff00' }} />
                    user@python-interpreter:~$
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={12}
                        placeholder=">>> # Enter your Python code here...
>>> print('Hello, World!')
>>> 
>>> # Example: Simple calculation
>>> x = 10
>>> y = 20
>>> result = x + y
>>> print(f'The sum of {x} and {y} is {result}')
>>> 
>>> # Example: Working with lists
>>> numbers = [1, 2, 3, 4, 5]
>>> squared = [n**2 for n in numbers]
>>> print(f'Original: {numbers}')
>>> print(f'Squared: {squared}')
>>> 
>>> # Return a value (optional)
>>> return {'sum': result, 'squared_numbers': squared}"
                        value={codeForm.code}
                        onChange={(e) => setCodeForm({ ...codeForm, code: e.target.value })}
                        helperText="Write Python code that will be executed in a secure sandbox environment. Standard library modules are available."
                        sx={{
                          backgroundColor: '#0d1117',
                          borderRadius: 2,
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#0d1117',
                            color: '#c9d1d9',
                            fontFamily: 'Monaco, Menlo, "Ubuntu Mono", monospace',
                            fontSize: '0.875rem',
                            '& fieldset': {
                              borderColor: '#30363d',
                              borderWidth: 2
                            },
                            '&:hover fieldset': {
                              borderColor: '#58a6ff'
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#00ff00'
                            }
                          },
                          '& .MuiInputBase-input': {
                            color: '#c9d1d9',
                            caretColor: '#00ff00',
                            '&::placeholder': {
                              color: '#7d8590',
                              opacity: 1
                            },
                            '&::selection': {
                              backgroundColor: '#264f78'
                            }
                          },
                          '& .MuiFormHelperText-root': {
                            color: '#7d8590',
                            fontFamily: 'monospace',
                            fontSize: '0.75rem'
                          }
                        }}
                      />
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} sx={{ color: '#00ff00' }} /> : <PlayIcon />}
                    onClick={handleCodeSubmit}
                    disabled={loading || !codeForm.code.trim()}
                    sx={{ 
                      mt: 3, 
                      px: 4,
                      backgroundColor: '#238636',
                      color: '#ffffff',
                      fontFamily: 'monospace',
                      fontWeight: 'bold',
                      border: '1px solid #2ea043',
                      '&:hover': {
                        backgroundColor: '#2ea043',
                        boxShadow: '0 0 10px rgba(0, 255, 0, 0.3)'
                      },
                      '&:disabled': {
                        backgroundColor: '#21262d',
                        color: '#7d8590',
                        border: '1px solid #30363d'
                      }
                    }}
                  >
                    {loading ? 'Executing...' : '▶ Execute Code'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ 
                border: '1px solid #30363d', 
                backgroundColor: '#161b22',
                height: 'fit-content' 
              }}>
                <CardContent>
                  <Typography variant="h6" sx={{ 
                    mb: 2,
                    color: '#00ff00',
                    fontFamily: 'monospace',
                    backgroundColor: '#0a0a0a',
                    p: 2,
                    borderRadius: 1,
                    border: '1px solid #333'
                  }}>
                    ~/examples $
                  </Typography>
                  <Typography variant="body2" sx={{ 
                    mb: 2,
                    color: '#7d8590',
                    fontFamily: 'monospace'
                  }}>
                    # Click any example to load it into the editor
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {[
                      {
                        title: 'Hello World',
                        code: `print("Hello, World!")
print("Welcome to Python code execution!")
return "Hello from Python!"`
                      },
                      {
                        title: 'Math Operations',
                        code: `import math

# Basic calculations
a = 15
b = 4
print(f"Addition: {a} + {b} = {a + b}")
print(f"Division: {a} / {b} = {a / b}")
print(f"Square root of {a}: {math.sqrt(a)}")
print(f"Power: {a}^{b} = {a**b}")

# Return calculation result
return {"sum": a + b, "power": a**b, "sqrt": math.sqrt(a)}`
                      },
                      {
                        title: 'List Processing',
                        code: `# Working with lists
numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

# Filter even numbers
evens = [n for n in numbers if n % 2 == 0]
print(f"Even numbers: {evens}")

# Calculate sum and average
total = sum(numbers)
average = total / len(numbers)
print(f"Sum: {total}, Average: {average}")

# Return processed data
return {"evens": evens, "total": total, "average": average}`
                      },
                      {
                        title: 'String Manipulation',
                        code: `text = "Python Code Execution"

print(f"Original: {text}")
print(f"Uppercase: {text.upper()}")
print(f"Lowercase: {text.lower()}")
print(f"Word count: {len(text.split())}")
print(f"Character count: {len(text)}")

# Reverse the string
reversed_text = text[::-1]
print(f"Reversed: {reversed_text}")`
                      },
                      {
                        title: 'JSON Processing',
                        code: `import json

# Create a dictionary
data = {
    "name": "Python Executor",
    "version": "1.0",
    "features": ["code execution", "real-time results", "error handling"]
}

# Convert to JSON string
json_string = json.dumps(data, indent=2)
print("JSON representation:")
print(json_string)

# Parse back from JSON
parsed_data = json.loads(json_string)
print(f"\\nName: {parsed_data['name']}")
print(f"Features: {', '.join(parsed_data['features'])}")`
                      }
                    ].map((example, index) => (
                      <Box key={index}>
                        <Button
                          variant="outlined"
                          onClick={() => setCodeForm({ code: example.code })}
                          sx={{ 
                            justifyContent: 'flex-start',
                            textAlign: 'left',
                            mb: 1,
                            width: '100%',
                            backgroundColor: '#0d1117',
                            color: '#c9d1d9',
                            borderColor: '#30363d',
                            fontFamily: 'monospace',
                            '&:hover': {
                              backgroundColor: '#21262d',
                              borderColor: '#58a6ff',
                              boxShadow: '0 0 5px rgba(88, 166, 255, 0.3)'
                            }
                          }}
                        >
                          ./{example.title.toLowerCase().replace(' ', '_')}.py
                        </Button>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ 
            border: '1px solid #30363d', 
            backgroundColor: '#0d1117',
            borderRadius: 2 
          }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ 
                mb: 2, 
                display: 'flex', 
                alignItems: 'center', 
                gap: 1,
                color: '#00ff00',
                fontFamily: 'monospace',
                backgroundColor: '#161b22',
                p: 2,
                borderRadius: 1,
                border: '1px solid #30363d'
              }}>
                user@python-interpreter:~$ execution_result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1, color: '#00ff00' }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" sx={{ 
                    mb: 1,
                    color: '#7d8590',
                    fontFamily: 'monospace'
                  }}>
                    Job ID: <span style={{ color: '#58a6ff' }}>{pollingJobId}</span>
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ 
                      mb: 1, 
                      height: 6, 
                      borderRadius: 3,
                      backgroundColor: '#21262d',
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: jobStatus === 'completed' ? '#00ff00' : 
                                       jobStatus === 'failed' ? '#f85149' : '#58a6ff'
                      }
                    }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? '#00ff00' : 
                           jobStatus === 'failed' ? '#f85149' : '#58a6ff',
                    fontFamily: 'monospace'
                  }}>
                    # {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Results - Both Success and Failure */}
              {result && jobStatus === 'completed' && result.result && (
                <Box>
                  <Alert 
                    severity={result.result.exit_code === 0 ? "success" : "warning"} 
                    sx={{ mb: 2 }}
                  >
                    {result.result.exit_code === 0 ? 
                      "🎉 Code executed successfully!" : 
                      "⚠️ Code executed with errors"}
                  </Alert>
                  
                  {/* Code Execution Results */}
                  <CodeExecutionResultComponent result={result.result} />
                  
                  {/* Additional Result Info */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ 
                      mb: 1,
                      color: '#00ff00',
                      fontFamily: 'monospace'
                    }}>📊 Execution Details:</Typography>
                    <Paper sx={{ 
                      p: 2, 
                      bgcolor: '#161b22',
                      border: '1px solid #30363d'
                    }}>
                      <Grid container spacing={2} sx={{ 
                        fontSize: '0.875rem',
                        fontFamily: 'monospace',
                        color: '#c9d1d9'
                      }}>
                        <Grid item xs={6}>
                          <strong style={{ color: '#00ff00' }}>Status:</strong> {result.result.exit_code === 0 ? 'Success' : 'Error'}
                        </Grid>
                        <Grid item xs={6}>
                          <strong style={{ color: '#00ff00' }}>Exit Code:</strong> {result.result.exit_code}
                        </Grid>
                        <Grid item xs={6}>
                          <strong style={{ color: '#00ff00' }}>Has Output:</strong> {result.result.stdout ? 'Yes' : 'No'}
                        </Grid>
                        <Grid item xs={6}>
                          <strong style={{ color: '#00ff00' }}>Has Errors:</strong> {result.result.stderr ? 'Yes' : 'No'}
                        </Grid>
                        {result.result.result !== undefined && result.result.result !== null && (
                          <Grid item xs={12}>
                            <strong style={{ color: '#00ff00' }}>Return Value:</strong> <span style={{ color: '#58a6ff' }}>{JSON.stringify(result.result.result)}</span>
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Python code execution job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default CodeExecutor;