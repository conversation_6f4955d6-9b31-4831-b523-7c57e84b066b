import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tab,
  Tabs
} from '@mui/material';
import {
  Visibility as VisionIcon,
  Chat as ChatIcon,
  TextFields as TextIcon
} from '@mui/icons-material';
import { pollinationsApi } from '../utils/api';
import { useJobPolling } from '../hooks/useJobPolling';
import {
  VisionAnalysisParams,
  TextGenerationParams,
  ChatCompletionParams,
  ModelOption,
  ExamplePrompts,
  TextModelsResponse,
  ModelObject,
  JobResult
} from '../types/pollinations';
import VisionAnalysisTab from '../components/pollinations/VisionAnalysisTab';
import TextGenerationTab from '../components/pollinations/TextGenerationTab';
import ChatCompletionTab from '../components/pollinations/ChatCompletionTab';
import ResultsDisplay from '../components/pollinations/ResultsDisplay';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`pollinations-tabpanel-${index}`}
      aria-labelledby={`pollinations-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const PollinationsAI: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [useSync, setUseSync] = useState(false);

  // Job polling hook
  const {
    result,
    jobStatus,
    jobProgress,
    pollingJobId,
    loading,
    error,
    setResult,
    setLoading,
    setError,
    startJob,
    resetState
  } = useJobPolling();

  // Dynamic model states
  const [textModels, setTextModels] = useState<ModelOption[]>([]);
  const [loadingModels, setLoadingModels] = useState(true);
  const [modelError, setModelError] = useState<string | null>(null);

  // Form states
  const [visionForm, setVisionForm] = useState<VisionAnalysisParams>({
    image_url: '',
    question: "What's in this image?",
    model: 'openai'
  });

  const [textForm, setTextForm] = useState<TextGenerationParams>({
    prompt: '',
    model: 'openai',
    temperature: 0.7,
    top_p: 0.9,
    json_mode: false
  });

  const [chatForm, setChatForm] = useState<ChatCompletionParams>({
    messages: [{ role: 'user', content: '' }],
    model: 'openai',
    temperature: 0.7,
    top_p: 0.9,
    json_mode: false
  });

  // Example prompts
  const examplePrompts: ExamplePrompts = {
    text: [
      'Write a creative story about a robot learning to paint',
      'Explain quantum computing in simple terms',
      'Create a marketing copy for an eco-friendly product',
      'Generate a list of healthy meal ideas for busy professionals',
      'Write a poem about the beauty of technology'
    ],
    vision: [
      "What objects can you see in this image?",
      "Describe the mood and atmosphere of this scene",
      "What colors are dominant in this image?",
      "Can you identify any text or writing in this image?",
      "Describe the composition and artistic style"
    ],
    chat: [
      "Hello! Can you help me brainstorm ideas for a creative project?",
      "I'm learning to code. Can you explain the concept of variables?",
      "What are some tips for improving productivity while working from home?",
      "Can you help me write a professional email?",
      "I'm planning a trip to Japan. What should I know?"
    ]
  };

  // Load dynamic models on component mount
  useEffect(() => {
    const loadModels = async () => {
      try {
        setLoadingModels(true);
        setModelError(null);

        const textModelsResponse = await pollinationsApi.listTextModels();
        
        if (textModelsResponse.success && textModelsResponse.data) {
          const models = textModelsResponse.data as TextModelsResponse;
          const availableModels: ModelOption[] = [];
          
          if (models.text_models && Array.isArray(models.text_models)) {
            models.text_models.forEach((model) => {
              let modelName: string;
              if (typeof model === 'string') {
                modelName = model;
              } else if (typeof model === 'object' && model !== null) {
                const modelObj = model as ModelObject;
                modelName = modelObj.name || modelObj.id || modelObj.model || String(model);
              } else {
                modelName = String(model);
              }
              
              let label = modelName;
              if (modelName === 'openai') label = 'OpenAI (GPT-4)';
              else if (modelName === 'mistral') label = 'Mistral AI';
              else if (modelName === 'anthropic') label = 'Claude';
              else if (modelName && typeof modelName === 'string') {
                label = modelName.charAt(0).toUpperCase() + modelName.slice(1);
              }
              
              availableModels.push({ name: modelName, label });
            });
          } else {
            // Fallback models
            const fallbackModels = ['openai', 'mistral', 'anthropic'];
            fallbackModels.forEach(model => {
              let label = model;
              if (model === 'openai') label = 'OpenAI (GPT-4)';
              else if (model === 'mistral') label = 'Mistral AI';
              else if (model === 'anthropic') label = 'Claude';
              
              availableModels.push({ name: model, label });
            });
          }
          
          setTextModels(availableModels);
        } else {
          throw new Error(textModelsResponse.error || 'Failed to fetch models');
        }
      } catch (err) {
        console.error('Failed to load models:', err);
        setModelError(err instanceof Error ? err.message : 'Failed to load models');
        
        // Use fallback models
        setTextModels([
          { name: 'openai', label: 'OpenAI (GPT-4)' },
          { name: 'mistral', label: 'Mistral AI' },
          { name: 'anthropic', label: 'Claude' }
        ]);
      } finally {
        setLoadingModels(false);
      }
    };

    loadModels();
  }, []);

  // Handlers
  const handleJobSubmit = (jobId: string) => {
    setLoading(true);
    setError(null);
    setResult(null);
    
    const jobType = activeTab === 0 ? 'vision' : activeTab === 1 ? 'text' : 'chat';
    const progressMessage = activeTab === 0 
      ? 'Job created, starting vision analysis...'
      : activeTab === 1 
      ? 'Job created, starting text generation...'
      : 'Job created, starting chat completion...';
    
    startJob(jobId, jobType, progressMessage);
  };

  const handleSyncResult = (syncResult: unknown) => {
    setResult(syncResult as JobResult);
    setLoading(false);
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setLoading(false);
  };

  const handleTabChange = (_e: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    resetState();
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Pollinations AI Tools 🌸
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Advanced AI capabilities including vision analysis, text generation, and intelligent chat powered by Pollinations.AI.
        </Typography>
      </Box>

      {/* Tab Navigation */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab 
            icon={<VisionIcon />} 
            label="Vision Analysis" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<TextIcon />} 
            label="Text Generation" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            icon={<ChatIcon />} 
            label="Chat Completion" 
            iconPosition="start"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
        </Tabs>
      </Box>

      {/* Vision Analysis Tab */}
      <TabPanel value={activeTab} index={0}>
        <VisionAnalysisTab
          visionForm={visionForm}
          setVisionForm={setVisionForm}
          textModels={textModels}
          loadingModels={loadingModels}
          modelError={modelError}
          loading={loading}
          onSubmit={handleJobSubmit}
          onError={handleError}
          examplePrompts={examplePrompts}
        />
      </TabPanel>

      {/* Text Generation Tab */}
      <TabPanel value={activeTab} index={1}>
        <TextGenerationTab
          textForm={textForm}
          setTextForm={setTextForm}
          textModels={textModels}
          loadingModels={loadingModels}
          modelError={modelError}
          loading={loading}
          useSync={useSync}
          setUseSync={setUseSync}
          onSubmit={handleJobSubmit}
          onSyncResult={handleSyncResult}
          onError={handleError}
          examplePrompts={examplePrompts}
        />
      </TabPanel>

      {/* Chat Completion Tab */}
      <TabPanel value={activeTab} index={2}>
        <ChatCompletionTab
          chatForm={chatForm}
          setChatForm={setChatForm}
          textModels={textModels}
          loadingModels={loadingModels}
          modelError={modelError}
          loading={loading}
          useSync={useSync}
          setUseSync={setUseSync}
          onSubmit={handleJobSubmit}
          onSyncResult={handleSyncResult}
          onError={handleError}
          examplePrompts={examplePrompts}
        />
      </TabPanel>

      {/* Results Display */}
      <ResultsDisplay
        result={result}
        error={error}
        jobStatus={jobStatus}
        jobProgress={jobProgress}
        pollingJobId={pollingJobId}
        activeTab={activeTab}
      />
    </Box>
  );
};

export default PollinationsAI;