import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  VideoFile as VideoIcon,
  Transform as TransformIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  VideoLibrary as LibraryIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

// Voice and provider interfaces
interface Voice {
  id: string;
  name?: string;
  label?: string;
  language?: string;
  [key: string]: unknown;
}

interface VoicesData {
  [provider: string]: Voice[];
}

interface ProvidersData {
  providers?: string[];
  default_provider?: string;
}

// Type definitions based on backend API
interface ImageToVideoParams extends Record<string, unknown> {
  image_url: string;
  video_length?: number;
  frame_rate?: number;
  zoom_speed?: number;
  narrator_speech_text?: string;
  voice?: string;
  tts_provider?: string;
  narrator_audio_url?: string;
  narrator_vol?: number;
  background_music_url?: string;
  background_music_vol?: number;
  should_add_captions?: boolean;
  caption_properties?: {
    max_words_per_line?: number;
    font_size?: number;
    font_family?: string;
    color?: string;
    position?: string;
    stroke_color?: string;
    stroke_width?: number;
    box_color?: string;
    box_opacity?: number;
    margin_vertical?: number;
    margin_horizontal?: number;
    alignment?: string;
    line_spacing?: number;
    animation?: string;
  };
  zoom_direction?: string;
  zoom_start_scale?: number;
  zoom_end_scale?: number;
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    video_url?: string;
    final_video_url?: string;
    video_with_audio_url?: string;
    audio_url?: string;
    srt_url?: string;
    thumbnail_url?: string;
    duration?: number;
    duration_seconds?: number;
    processing_time?: number;
    processing_time_seconds?: number;
    file_size?: number;
    file_size_mb?: number;
    word_count?: number;
    segments_count?: number;
    resolution?: string;
    [key: string]: unknown; // Allow for flexible response structure
  };
  error?: string | null;
}

const ImageToVideo: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<JobResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);
  const [voices, setVoices] = useState<VoicesData>({});
  const [providers, setProviders] = useState<ProvidersData>({});
  const [loadingVoices, setLoadingVoices] = useState(false);

  // Form state
  const [form, setForm] = useState<ImageToVideoParams>({
    image_url: '',
    video_length: 10,
    frame_rate: 30,
    zoom_speed: 1.0,
    zoom_direction: 'zoom_in',
    zoom_start_scale: 1.0,
    zoom_end_scale: 1.2,
    narrator_speech_text: '',
    voice: 'af_heart',
    tts_provider: 'kokoro',
    narrator_vol: 80,
    background_music_url: '',
    background_music_vol: 20,
    should_add_captions: false,
    caption_properties: {
      max_words_per_line: 10,
      font_size: 48,
      font_family: 'Arial Bold',
      color: 'white',
      position: 'bottom',
      stroke_color: 'black',
      stroke_width: 2,
      box_color: 'rgba(0,0,0,0.7)',
      box_opacity: 0.7,
      margin_vertical: 50,
      margin_horizontal: 50,
      alignment: 'center',
      line_spacing: 1.2,
      animation: 'fade_in'
    }
  });

  // Fetch TTS data on component mount
  React.useEffect(() => {
    const fetchTTSData = async () => {
      setLoadingVoices(true);
      try {
        // Fetch voices and providers in parallel
        const [voicesRes, providersRes] = await Promise.all([
          directApi.get('/api/v1/audio/voices/all'),
          directApi.get('/api/v1/audio/providers')
        ]);

        if (voicesRes.data) {
          setVoices(voicesRes.data.voices || {});
        }

        if (providersRes.data) {
          setProviders(providersRes.data || {});
          // Set default provider and voice if available
          if (providersRes.data.default_provider) {
            const defaultProvider = providersRes.data.default_provider;
            const defaultVoices = voicesRes.data?.voices?.[defaultProvider];
            setForm(prev => ({
              ...prev,
              tts_provider: defaultProvider,
              voice: defaultVoices?.[0]?.id || prev.voice
            }));
          }
        }
      } catch (error) {
        console.error('Failed to fetch TTS data:', error);
      } finally {
        setLoadingVoices(false);
      }
    };

    fetchTTSData();
  }, []);

  // Handle provider change and update voice selection
  const handleProviderChange = (newProvider: string) => {
    const availableVoices = voices[newProvider];
    
    setForm(prev => ({
      ...prev,
      tts_provider: newProvider,
      voice: availableVoices?.[0]?.id || '' // Set to first available voice or empty
    }));
  };

  // Job status polling function
  const pollJobStatus = async (jobId: string) => {
    const maxAttempts = 120; // 10 minutes max for video processing
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`/api/v1/videos/generations/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Video processing completed successfully!');
          setResult({ job_id: jobId, result: jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Video processing failed');
          setError(jobError || 'Video processing failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing video... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  const handleSubmit = async () => {
    if (!form.image_url.trim()) {
      setError('Image URL is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/videos/generations', form);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting video processing...');
        pollJobStatus(response.data.job_id);
      } else {
        setError('Failed to create image-to-video job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const exampleImages = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
    'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
    'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
    'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800'
  ];

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Image to Video Converter 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Transform static images into dynamic videos with narration, background music, and captions.
        </Typography>
      </Box>

      {/* Main Form */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TransformIcon color="primary" />
                    Image to Video Settings
                  </Typography>

                  <Grid container spacing={3}>
                    {/* Image URL */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Image URL"
                        placeholder="https://example.com/image.jpg"
                        value={form.image_url}
                        onChange={(e) => setForm({ ...form, image_url: e.target.value })}
                        helperText="URL of the image to convert to video"
                      />
                    </Grid>

                    {/* Video Settings */}
                    <Grid item xs={12} md={4}>
                      <Typography gutterBottom>Video Length: {form.video_length}s</Typography>
                      <Slider
                        value={form.video_length}
                        onChange={(_e, value) => setForm({ ...form, video_length: Array.isArray(value) ? value[0] : value })}
                        min={5}
                        max={60}
                        step={1}
                        marks={[
                          { value: 5, label: '5s' },
                          { value: 15, label: '15s' },
                          { value: 30, label: '30s' },
                          { value: 60, label: '60s' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        type="number"
                        label="Frame Rate (FPS)"
                        value={form.frame_rate}
                        onChange={(e) => setForm({ ...form, frame_rate: parseInt(e.target.value) })}
                        inputProps={{ min: 15, max: 60 }}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <Typography gutterBottom>Zoom Speed: {form.zoom_speed}</Typography>
                      <Slider
                        value={form.zoom_speed}
                        onChange={(_e, value) => setForm({ ...form, zoom_speed: Array.isArray(value) ? value[0] : value })}
                        min={0.1}
                        max={5.0}
                        step={0.1}
                        marks={[
                          { value: 0.1, label: 'Very Slow' },
                          { value: 1.0, label: 'Normal' },
                          { value: 3.0, label: 'Fast' },
                          { value: 5.0, label: 'Very Fast' }
                        ]}
                      />
                    </Grid>

                    {/* Enhanced Zoom Controls */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="h6">Advanced Zoom Effects</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Zoom Direction</InputLabel>
                                <Select
                                  value={form.zoom_direction}
                                  label="Zoom Direction"
                                  onChange={(e) => setForm({ ...form, zoom_direction: e.target.value })}
                                >
                                  <MenuItem value="zoom_in">Zoom In (Ken Burns)</MenuItem>
                                  <MenuItem value="zoom_out">Zoom Out</MenuItem>
                                  <MenuItem value="pan_left">Pan Left</MenuItem>
                                  <MenuItem value="pan_right">Pan Right</MenuItem>
                                  <MenuItem value="pan_up">Pan Up</MenuItem>
                                  <MenuItem value="pan_down">Pan Down</MenuItem>
                                  <MenuItem value="static">Static (No Movement)</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <Typography gutterBottom>Start Scale: {form.zoom_start_scale}</Typography>
                              <Slider
                                value={form.zoom_start_scale}
                                onChange={(_e, value) => setForm({ ...form, zoom_start_scale: Array.isArray(value) ? value[0] : value })}
                                min={0.5}
                                max={2.0}
                                step={0.1}
                                marks={[
                                  { value: 0.5, label: '0.5x' },
                                  { value: 1.0, label: '1x' },
                                  { value: 1.5, label: '1.5x' },
                                  { value: 2.0, label: '2x' }
                                ]}
                              />
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <Typography gutterBottom>End Scale: {form.zoom_end_scale}</Typography>
                              <Slider
                                value={form.zoom_end_scale}
                                onChange={(_e, value) => setForm({ ...form, zoom_end_scale: Array.isArray(value) ? value[0] : value })}
                                min={0.5}
                                max={2.0}
                                step={0.1}
                                marks={[
                                  { value: 0.5, label: '0.5x' },
                                  { value: 1.0, label: '1x' },
                                  { value: 1.5, label: '1.5x' },
                                  { value: 2.0, label: '2x' }
                                ]}
                              />
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>

                    {/* Audio Settings Accordion */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="h6">Audio Settings (Optional)</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                multiline
                                rows={3}
                                label="Narrator Text (Text-to-Speech)"
                                placeholder="Enter text to convert to speech narration..."
                                value={form.narrator_speech_text}
                                onChange={(e) => setForm({ ...form, narrator_speech_text: e.target.value })}
                                helperText="Text will be converted to speech and added as narration"
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControl fullWidth>
                                <InputLabel>TTS Provider</InputLabel>
                                <Select
                                  value={form.tts_provider || ''}
                                  label="TTS Provider"
                                  onChange={(e) => handleProviderChange(e.target.value)}
                                  disabled={loadingVoices}
                                >
                                  {providers.providers?.map((provider) => (
                                    <MenuItem key={provider} value={provider}>
                                      {provider.charAt(0).toUpperCase() + provider.slice(1)}
                                    </MenuItem>
                                  ))}
                                  {(!providers.providers || providers.providers.length === 0) && (
                                    <MenuItem value="" disabled>
                                      {loadingVoices ? 'Loading providers...' : 'No providers available'}
                                    </MenuItem>
                                  )}
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControl fullWidth>
                                <InputLabel>Voice</InputLabel>
                                <Select
                                  value={form.voice || ''}
                                  label="Voice"
                                  onChange={(e) => {
                                    setForm({ ...form, voice: e.target.value });
                                  }}
                                  disabled={loadingVoices || !form.tts_provider}
                                >
                                  {form.tts_provider && voices[form.tts_provider]?.length > 0 ? (
                                    voices[form.tts_provider].map((voice) => (
                                      <MenuItem key={voice.id} value={voice.id}>
                                        {voice.label || voice.name || voice.id}
                                        {voice.language && ` (${voice.language})`}
                                      </MenuItem>
                                    ))
                                  ) : (
                                    <MenuItem value="" disabled>
                                      {loadingVoices 
                                        ? 'Loading voices...' 
                                        : !form.tts_provider 
                                        ? 'Select a provider first'
                                        : 'No voices available for this provider'
                                      }
                                    </MenuItem>
                                  )}
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <Typography gutterBottom>Narrator Volume: {form.narrator_vol}%</Typography>
                              <Slider
                                value={form.narrator_vol}
                                onChange={(_e, value) => setForm({ ...form, narrator_vol: Array.isArray(value) ? value[0] : value })}
                                min={0}
                                max={100}
                                step={5}
                              />
                            </Grid>

                            <Grid item xs={12}>
                              <TextField
                                fullWidth
                                label="Background Music URL (Optional)"
                                placeholder="https://example.com/music.mp3 or YouTube URL"
                                value={form.background_music_url}
                                onChange={(e) => setForm({ ...form, background_music_url: e.target.value })}
                                helperText="URL to background music (supports YouTube URLs)"
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <Typography gutterBottom>Background Music Volume: {form.background_music_vol}%</Typography>
                              <Slider
                                value={form.background_music_vol}
                                onChange={(_e, value) => setForm({ ...form, background_music_vol: Array.isArray(value) ? value[0] : value })}
                                min={0}
                                max={100}
                                step={5}
                              />
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>

                    {/* Caption Settings */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="h6">Caption Settings</Typography>
                          <Box sx={{ ml: 2 }}>
                            <FormControlLabel
                              control={
                                <Switch
                                  checked={form.should_add_captions}
                                  onChange={(e) => setForm({ ...form, should_add_captions: e.target.checked })}
                                />
                              }
                              label="Enable Captions"
                              onClick={(e) => e.stopPropagation()}
                            />
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            {/* Text Styling */}
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Font Size"
                                value={form.caption_properties?.font_size || 48}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    font_size: parseInt(e.target.value) 
                                  } 
                                })}
                                inputProps={{ min: 12, max: 120 }}
                                helperText="Font size in pixels"
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControl fullWidth>
                                <InputLabel>Font Family</InputLabel>
                                <Select
                                  value={form.caption_properties?.font_family || 'Arial Bold'}
                                  label="Font Family"
                                  onChange={(e) => setForm({ 
                                    ...form, 
                                    caption_properties: { 
                                      ...form.caption_properties, 
                                      font_family: e.target.value 
                                    } 
                                  })}
                                >
                                  <MenuItem value="Arial Bold">Arial Bold</MenuItem>
                                  <MenuItem value="Helvetica Bold">Helvetica Bold</MenuItem>
                                  <MenuItem value="Times New Roman Bold">Times Bold</MenuItem>
                                  <MenuItem value="Impact">Impact</MenuItem>
                                  <MenuItem value="Montserrat Bold">Montserrat Bold</MenuItem>
                                  <MenuItem value="Oswald Bold">Oswald Bold</MenuItem>
                                  <MenuItem value="Roboto Bold">Roboto Bold</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            {/* Text Colors */}
                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                label="Text Color"
                                value={form.caption_properties?.color || 'white'}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    color: e.target.value 
                                  } 
                                })}
                                helperText="Color name or hex code"
                                placeholder="white, #FFFFFF"
                              />
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                label="Stroke Color"
                                value={form.caption_properties?.stroke_color || 'black'}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    stroke_color: e.target.value 
                                  } 
                                })}
                                helperText="Outline color"
                                placeholder="black, #000000"
                              />
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Stroke Width"
                                value={form.caption_properties?.stroke_width || 2}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    stroke_width: parseFloat(e.target.value) 
                                  } 
                                })}
                                inputProps={{ min: 0, max: 10, step: 0.5 }}
                                helperText="Outline thickness"
                              />
                            </Grid>

                            {/* Background Box */}
                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                label="Background Box Color"
                                value={form.caption_properties?.box_color || 'rgba(0,0,0,0.7)'}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    box_color: e.target.value 
                                  } 
                                })}
                                helperText="RGBA or color name"
                                placeholder="rgba(0,0,0,0.7)"
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <Typography gutterBottom>Box Opacity: {(form.caption_properties?.box_opacity || 0.7) * 100}%</Typography>
                              <Slider
                                value={form.caption_properties?.box_opacity || 0.7}
                                onChange={(_e, value) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    box_opacity: Array.isArray(value) ? value[0] : value 
                                  } 
                                })}
                                min={0}
                                max={1}
                                step={0.1}
                                marks={[
                                  { value: 0, label: 'Transparent' },
                                  { value: 0.5, label: '50%' },
                                  { value: 1, label: 'Opaque' }
                                ]}
                              />
                            </Grid>

                            {/* Position and Layout */}
                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Position</InputLabel>
                                <Select
                                  value={form.caption_properties?.position || 'bottom'}
                                  label="Position"
                                  onChange={(e) => setForm({ 
                                    ...form, 
                                    caption_properties: { 
                                      ...form.caption_properties, 
                                      position: e.target.value 
                                    } 
                                  })}
                                >
                                  <MenuItem value="top">Top</MenuItem>
                                  <MenuItem value="center">Center</MenuItem>
                                  <MenuItem value="bottom">Bottom</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Alignment</InputLabel>
                                <Select
                                  value={form.caption_properties?.alignment || 'center'}
                                  label="Alignment"
                                  onChange={(e) => setForm({ 
                                    ...form, 
                                    caption_properties: { 
                                      ...form.caption_properties, 
                                      alignment: e.target.value 
                                    } 
                                  })}
                                >
                                  <MenuItem value="left">Left</MenuItem>
                                  <MenuItem value="center">Center</MenuItem>
                                  <MenuItem value="right">Right</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Animation</InputLabel>
                                <Select
                                  value={form.caption_properties?.animation || 'fade_in'}
                                  label="Animation"
                                  onChange={(e) => setForm({ 
                                    ...form, 
                                    caption_properties: { 
                                      ...form.caption_properties, 
                                      animation: e.target.value 
                                    } 
                                  })}
                                >
                                  <MenuItem value="none">None</MenuItem>
                                  <MenuItem value="fade_in">Fade In</MenuItem>
                                  <MenuItem value="slide_up">Slide Up</MenuItem>
                                  <MenuItem value="slide_down">Slide Down</MenuItem>
                                  <MenuItem value="scale_in">Scale In</MenuItem>
                                  <MenuItem value="typewriter">Typewriter</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            {/* Margins and Spacing */}
                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Words Per Line"
                                value={form.caption_properties?.max_words_per_line || 10}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    max_words_per_line: parseInt(e.target.value) 
                                  } 
                                })}
                                inputProps={{ min: 1, max: 20 }}
                                helperText="Max words per line"
                              />
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Vertical Margin"
                                value={form.caption_properties?.margin_vertical || 50}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    margin_vertical: parseInt(e.target.value) 
                                  } 
                                })}
                                inputProps={{ min: 0, max: 200 }}
                                helperText="Pixels from edge"
                              />
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <Typography gutterBottom>Line Spacing: {form.caption_properties?.line_spacing || 1.2}</Typography>
                              <Slider
                                value={form.caption_properties?.line_spacing || 1.2}
                                onChange={(_e, value) => setForm({ 
                                  ...form, 
                                  caption_properties: { 
                                    ...form.caption_properties, 
                                    line_spacing: Array.isArray(value) ? value[0] : value 
                                  } 
                                })}
                                min={0.8}
                                max={2.0}
                                step={0.1}
                                marks={[
                                  { value: 0.8, label: 'Tight' },
                                  { value: 1.2, label: 'Normal' },
                                  { value: 1.6, label: 'Loose' }
                                ]}
                              />
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <VideoIcon />}
                    onClick={handleSubmit}
                    disabled={loading || !form.image_url.trim()}
                    sx={{ mt: 3, px: 4 }}
                  >
                    {loading ? 'Processing...' : 'Create Video'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Example Images
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Click any image to use as your source.
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    {exampleImages.map((url, index) => (
                      <Box key={index}>
                        <img
                          src={url}
                          alt={`Example ${index + 1}`}
                          style={{
                            width: '100%',
                            height: '80px',
                            objectFit: 'cover',
                            borderRadius: '8px',
                            cursor: 'pointer',
                            border: form.image_url === url ? '2px solid #1976d2' : '1px solid #e0e0e0'
                          }}
                          onClick={() => setForm({ ...form, image_url: url })}
                        />
                        <Button
                          variant="outlined"
                          size="small"
                          onClick={() => setForm({ ...form, image_url: url })}
                          sx={{ mt: 1, width: '100%' }}
                        >
                          Use This Image
                        </Button>
                      </Box>
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                🎬 Video Processing Result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Success Results */}
              {result && jobStatus === 'completed' && result.result && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🎉 Video created successfully!
                  </Alert>
                  
                  {/* Video Preview */}
                  {(() => {
                    const videoUrl = result.result.final_video_url || result.result.video_url || result.result.video_with_audio_url;
                    return videoUrl ? (
                      <Box sx={{ mb: 3 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                          <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                            Generated Video
                          </Typography>
                          <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button
                              startIcon={<DownloadIcon />}
                              href={videoUrl}
                              target="_blank"
                              variant="contained"
                              size="small"
                            >
                              Download Video
                            </Button>
                            <Button
                              startIcon={<LibraryIcon />}
                              onClick={() => navigate('/dashboard/library')}
                              variant="outlined"
                              size="small"
                              color="primary"
                            >
                              View in Library
                            </Button>
                          </Box>
                        </Box>
                        
                        <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                          <video
                            src={videoUrl}
                            controls
                            style={{
                              width: '100%',
                              maxHeight: '400px',
                              borderRadius: '8px'
                            }}
                          />
                        </Paper>
                      </Box>
                    ) : null;
                  })()}
                  
                  {/* Processing Details */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Video Details:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {(result.result.duration || result.result.duration_seconds) && (
                          <Grid item xs={12} md={4}>
                            <strong>Duration:</strong> {(result.result.duration || result.result.duration_seconds)?.toFixed(1)}s
                          </Grid>
                        )}
                        {(result.result.processing_time || result.result.processing_time_seconds) && (
                          <Grid item xs={12} md={4}>
                            <strong>Processing Time:</strong> {(result.result.processing_time || result.result.processing_time_seconds)?.toFixed(1)}s
                          </Grid>
                        )}
                        {(result.result.file_size || result.result.file_size_mb) && (
                          <Grid item xs={12} md={4}>
                            <strong>File Size:</strong> {
                              result.result.file_size_mb ? 
                                `${result.result.file_size_mb.toFixed(1)} MB` :
                                `${(result.result.file_size! / 1024 / 1024).toFixed(1)} MB`
                            }
                          </Grid>
                        )}
                        {result.result.resolution && (
                          <Grid item xs={12} md={4}>
                            <strong>Resolution:</strong> {result.result.resolution}
                          </Grid>
                        )}
                        {result.result.word_count && (
                          <Grid item xs={12} md={4}>
                            <strong>Word Count:</strong> {result.result.word_count}
                          </Grid>
                        )}
                        {result.result.segments_count && (
                          <Grid item xs={12} md={4}>
                            <strong>Segments:</strong> {result.result.segments_count}
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Video processing job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default ImageToVideo;