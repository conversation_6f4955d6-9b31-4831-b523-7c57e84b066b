import React from 'react';
import {
  Box,
  Typography,
  Paper
} from '@mui/material';
import { VideoGeneratorTab } from '../components/contentCreation';

const AIVideoGenerator: React.FC = () => {
  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 2
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          AI Video Generator 🤖
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Generate high-quality videos from text prompts or animate images using advanced AI models (LTX-Video, WaveSpeed AI, ComfyUI).
        </Typography>
      </Box>

      {/* Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, mb: 3, flex: 1, display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <VideoGeneratorTab />
        </Box>
      </Paper>
    </Box>
  );
};

export default AIVideoGenerator;