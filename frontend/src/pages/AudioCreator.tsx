import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Slider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  CircularProgress,
  Paper,
  Tab,
  Tabs,
  LinearProgress
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  VolumeUp as AudioIcon,
  MusicNote as MusicIcon,
  Transcribe as TranscribeIcon,
  Send as SendIcon,
  Settings as SettingsIcon,
  VideoLibrary as LibraryIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

// Type definitions
interface Voice {
  id: string;
  name?: string;
  label?: string;
  language?: string;
  [key: string]: unknown;
}

interface VoicesData {
  [provider: string]: Voice[];
}

interface ModelsData {
  models?: { [provider: string]: ModelItem[] };
}

interface ProvidersData {
  providers?: string[];
  formats?: { [provider: string]: string[] };
  models?: { [provider: string]: ModelItem[] };
  default_provider?: string;
}

interface ModelItem {
  id: string;
  name: string;
}

// Transcription segment interface
interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
  confidence?: number;
}

// TTS job result interface
interface TTSJobResult {
  audio_url: string;
  tts_engine?: string;
  voice?: string;
  response_format?: string;
  estimated_duration?: number;
  word_count?: number;
  model_used?: string;
}

// Music job result interface
interface MusicJobResult {
  audio_url: string;
  model_used?: string;
  estimated_duration?: number;
}

// Transcription job result interface
interface TranscriptionJobResult {
  text?: string;
  srt_url?: string;
  segments?: TranscriptionSegment[];
  word_count?: number;
  estimated_duration?: number;
}

// Union type for all job results
type JobResult = TTSJobResult | MusicJobResult | TranscriptionJobResult;

// API result interface
interface ApiResult {
  job_id: string;
  status?: string;
  jobResult?: JobResult;
  [key: string]: unknown;
}


interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`audio-tabpanel-${index}`}
      aria-labelledby={`audio-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const Audio: React.FC = () => {
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ApiResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);
  const [voices, setVoices] = useState<VoicesData>({});
  const [models, setModels] = useState<ModelsData>({});
  const [providers, setProviders] = useState<ProvidersData>({});
  const [loadingVoices, setLoadingVoices] = useState(false);

  // Audio Generation (TTS) State
  const [ttsForm, setTtsForm] = useState({
    text: '',
    voice: 'af_heart',
    provider: 'kokoro',
    model: 'tts-1',
    response_format: 'mp3',
    speed: 1.0,
    volume_multiplier: 1.0,
    lang_code: '',
    return_timestamps: false,
    stream: false,
    stream_format: 'audio',
    remove_filter: false,
    voice_weights: '',
    // Advanced normalization options
    normalize: true,
    unit_normalization: false,
    url_normalization: true,
    email_normalization: true,
    phone_normalization: true,
    replace_remaining_symbols: true
  });

  // Music Generation State
  const [musicForm, setMusicForm] = useState({
    description: '',
    duration: 8,
    model_size: 'small',
    output_format: 'wav'
  });

  // Transcription State
  const [transcriptionForm, setTranscriptionForm] = useState({
    media_url: '',
    include_text: true,
    include_srt: true,
    word_timestamps: false,
    include_segments: false,
    language: '',
    max_words_per_line: 10,
    beam_size: 5
  });

  // Fetch TTS data on component mount
  useEffect(() => {
    const fetchTTSData = async () => {
      setLoadingVoices(true);
      try {
        // Fetch all TTS data in parallel using directApi
        const [voicesRes, modelsRes, providersRes] = await Promise.all([
          directApi.get('/api/v1/audio/voices/all'),
          directApi.get('/api/v1/audio/models'),
          directApi.get('/api/v1/audio/providers')
        ]);

        if (voicesRes.data) {
          setVoices(voicesRes.data.voices || {});
        }

        if (modelsRes.data) {
          setModels(modelsRes.data || {});
        }

        if (providersRes.data) {
          setProviders(providersRes.data || {});
        }
      } catch (error) {
        console.error('Failed to fetch TTS data:', error);
      } finally {
        setLoadingVoices(false);
      }
    };

    fetchTTSData();
  }, []);

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setResult(null);
    setError(null);
    setJobStatus(null);
    setJobProgress('');
    setPollingJobId(null);
  };

  // Job status polling function
  const pollJobStatus = async (jobId: string, jobType: 'speech' | 'music' | 'transcription') => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        let statusResponse;

        // Use appropriate endpoint based on job type
        if (jobType === 'speech') {
          statusResponse = await directApi.get(`/api/v1/audio/speech/${jobId}`);
        } else if (jobType === 'music') {
          statusResponse = await directApi.get(`/api/v1/audio/music/${jobId}`);
        } else {
          statusResponse = await directApi.get(`/api/v1/audio/transcriptions/${jobId}`);
        }

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Job completed successfully!');
          setResult({ job_id: jobId, jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Job failed');
          setError(jobError || 'Job processing failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  // Audio player component
  const AudioPlayer = ({ audioUrl, title }: { audioUrl: string; title: string }) => (
    <Box sx={{ mt: 3, mb: 3, p: 3, border: '1px solid #e2e8f0', borderRadius: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 1 }}>
        🎵 {title}
      </Typography>
      <audio controls style={{ width: '100%', marginBottom: '8px' }}>
        <source src={audioUrl} type="audio/mpeg" />
        <source src={audioUrl} type="audio/wav" />
        Your browser does not support the audio element.
      </audio>
      <Button
        variant="outlined"
        size="small"
        onClick={() => window.open(audioUrl, '_blank')}
        sx={{ mr: 1 }}
      >
        Download
      </Button>
      <Button
        variant="outlined"
        size="small"
        onClick={() => navigator.clipboard.writeText(audioUrl)}
        sx={{ mr: 1 }}
      >
        Copy URL
      </Button>
      <Button
        startIcon={<LibraryIcon />}
        onClick={() => navigate('/dashboard/library')}
        variant="outlined"
        size="small"
        color="primary"
      >
        View in Library
      </Button>
    </Box>
  );

  // Transcription result component
  const TranscriptionResult = ({ result }: { result: TranscriptionJobResult }) => (
    <Box sx={{ mt: 3, mb: 3 }}>
      {result.text && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>📝 Transcription Text:</Typography>
          <Paper sx={{ p: 3, bgcolor: '#f8fafc', maxHeight: 200, overflow: 'auto' }}>
            <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
              {result.text}
            </Typography>
          </Paper>
        </Box>
      )}
      {result.srt_url && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>📄 SRT Subtitles:</Typography>
          <Button
            variant="outlined"
            onClick={() => window.open(result.srt_url, '_blank')}
            sx={{ mr: 1 }}
          >
            Download SRT
          </Button>
          <Button
            variant="outlined"
            onClick={() => navigator.clipboard.writeText(result.srt_url || '')}
            sx={{ mr: 1 }}
          >
            Copy SRT URL
          </Button>
          <Button
            startIcon={<LibraryIcon />}
            onClick={() => navigate('/dashboard/library')}
            variant="outlined"
            size="small"
            color="primary"
          >
            View in Library
          </Button>
        </Box>
      )}
      {result.segments && Array.isArray(result.segments) && (
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>🎯 Segments ({result.segments.length}):</Typography>
          <Paper sx={{ p: 3, bgcolor: '#f8fafc', maxHeight: 150, overflow: 'auto' }}>
            {result.segments.slice(0, 3).map((segment: TranscriptionSegment, index: number) => (
              <Typography key={index} variant="body2" sx={{ mb: 1 }}>
                [{segment.start?.toFixed(1)}s - {segment.end?.toFixed(1)}s]: {segment.text}
              </Typography>
            ))}
            {result.segments.length > 3 && (
              <Typography variant="body2" color="text.secondary">
                ... and {result.segments.length - 3} more segments
              </Typography>
            )}
          </Paper>
        </Box>
      )}
    </Box>
  );

  const handleTtsSubmit = async () => {
    if (!ttsForm.text.trim()) {
      setError('Text content is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const requestData = {
        text: ttsForm.text,
        voice: ttsForm.voice,
        provider: ttsForm.provider,
        model: ttsForm.model,
        response_format: ttsForm.response_format,
        speed: ttsForm.speed,
        volume_multiplier: ttsForm.volume_multiplier,
        lang_code: ttsForm.lang_code || undefined,
        return_timestamps: ttsForm.return_timestamps,
        stream: ttsForm.stream,
        stream_format: ttsForm.stream_format,
        remove_filter: ttsForm.remove_filter,
        voice_weights: ttsForm.voice_weights || undefined,
        normalization_options: {
          normalize: ttsForm.normalize,
          unit_normalization: ttsForm.unit_normalization,
          url_normalization: ttsForm.url_normalization,
          email_normalization: ttsForm.email_normalization,
          phone_normalization: ttsForm.phone_normalization,
          replace_remaining_symbols: ttsForm.replace_remaining_symbols
        }
      };

      const response = await directApi.post('/api/v1/audio/speech', requestData);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting processing...');
        // Start polling for job status
        pollJobStatus(response.data.job_id, 'speech');
      } else {
        setError('Failed to generate speech');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleMusicSubmit = async () => {
    if (!musicForm.description.trim()) {
      setError('Music description is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/audio/music', musicForm);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting music generation...');
        // Start polling for job status
        pollJobStatus(response.data.job_id, 'music');
      } else {
        setError('Failed to generate music');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleTranscriptionSubmit = async () => {
    if (!transcriptionForm.media_url.trim()) {
      setError('Media URL is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/audio/transcriptions', transcriptionForm);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting transcription...');
        // Start polling for job status
        pollJobStatus(response.data.job_id, 'transcription');
      } else {
        setError('Failed to create transcription job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };


  // Helper functions to get dynamic options
  const getProviderOptions = useCallback(() => {
    // providers.providers is an array of provider names
    if (providers.providers && Array.isArray(providers.providers) && providers.providers.length > 0) {
      return providers.providers;
    }
    return ['kokoro', 'edge'];
  }, [providers.providers]);

  const getFormatOptions = useCallback(() => {
    if (!providers.formats) return ['mp3', 'wav'];
    const currentProvider = ttsForm.provider;
    return providers.formats[currentProvider] || ['mp3', 'wav'];
  }, [providers.formats, ttsForm.provider]);

  const getModelOptions = useCallback(() => {
    // Handle both old format (models directly) and new format (models.models)
    const modelsData = (models as Record<string, unknown>)?.models || models;
    if (!modelsData || typeof modelsData !== 'object' || Object.keys(modelsData).length === 0) return [];
    
    const currentProvider = ttsForm.provider;
    const providerModels = (modelsData as Record<string, unknown>)[currentProvider];
    if (providerModels && Array.isArray(providerModels)) {
      return providerModels.map((model: ModelItem) => model.id);
    }
    return [];
  }, [models, ttsForm.provider]);

  const getVoiceOptions = useCallback(() => {
    const currentProvider = ttsForm.provider;
    if (!voices[currentProvider]) return [];
    
    return voices[currentProvider].map((voice: Voice) => {
      // Handle different voice formats (Kokoro vs Edge)
      const id = String(voice.name || voice.id || '');
      let label = '';
      
      if (voice.display_name) {
        // Edge TTS format
        label = String(voice.display_name);
      } else if (voice.description) {
        // Kokoro format
        label = String(voice.description);
      } else {
        // Fallback
        label = String(voice.name || voice.id || 'Unknown Voice');
      }
      
      return {
        ...voice,
        id,
        label
      };
    });
  }, [voices, ttsForm.provider]);

  // Update form defaults when data is loaded
  useEffect(() => {
    if (!loadingVoices && (voices || providers)) {
      try {
        const availableProviders = getProviderOptions();
        const currentProvider = availableProviders.includes(ttsForm.provider) 
          ? ttsForm.provider 
          : (availableProviders[0] || 'kokoro');
        
        const availableVoices = voices[currentProvider] || [];
        const currentVoice = availableVoices.find(v => 
          String(v.name || v.id) === String(ttsForm.voice)
        )
          ? ttsForm.voice
          : String(availableVoices[0]?.name || availableVoices[0]?.id || 'af_heart');
        
        const availableFormats = getFormatOptions();
        const currentFormat = availableFormats.includes(ttsForm.response_format)
          ? ttsForm.response_format
          : (availableFormats[0] || 'mp3');
        
        const availableModels = getModelOptions();
        const currentModel = availableModels.includes(ttsForm.model)
          ? ttsForm.model
          : (availableModels[0] || 'tts-1');
        
        setTtsForm(prev => ({
          ...prev,
          provider: String(currentProvider),
          voice: String(currentVoice),
          response_format: String(currentFormat),
          model: String(currentModel)
        }));
      } catch (error) {
        console.error('Error initializing form defaults:', error);
      }
    }
  }, [loadingVoices, voices, providers, ttsForm.provider, ttsForm.voice, ttsForm.response_format, ttsForm.model, getFormatOptions, getModelOptions, getProviderOptions]);

  const voiceOptions = getVoiceOptions();
  const formatOptions = getFormatOptions();
  const providerOptions = getProviderOptions();
  const modelOptions = getModelOptions();
  const streamFormatOptions = ['audio', 'sse'];
  const languageOptions = ['en', 'ja', 'zh', 'es', 'fr', 'de', 'it', 'pt', 'hi'];
  const modelSizeOptions = ['small'];
  const musicFormatOptions = ['wav', 'mp3'];

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8 // Add bottom padding for results section
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Audio Tools 🎵
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Generate speech, create music, and transcribe audio content with AI-powered tools.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
        <Tabs 
          value={tabValue} 
          onChange={handleTabChange}
          sx={{ 
            borderBottom: '1px solid #e2e8f0',
            '& .MuiTab-root': {
              textTransform: 'none',
              fontWeight: 600,
              fontSize: '1rem',
              minHeight: 64
            }
          }}
        >
          <Tab 
            icon={<AudioIcon />} 
            label="Audio Generation" 
            iconPosition="start"
            sx={{ gap: 1 }}
          />
          <Tab 
            icon={<MusicIcon />} 
            label="Music Generation" 
            iconPosition="start"
            sx={{ gap: 1 }}
          />
          <Tab 
            icon={<TranscribeIcon />} 
            label="Transcription" 
            iconPosition="start"
            sx={{ gap: 1 }}
          />
        </Tabs>

        {/* Audio Generation Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <AudioIcon color="primary" />
                    Text-to-Speech Configuration
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={4}
                        label="Text Content"
                        placeholder="Enter the text you want to convert to speech..."
                        value={ttsForm.text}
                        onChange={(e) => setTtsForm({ ...ttsForm, text: e.target.value })}
                        helperText="Maximum 5000 characters. Supports pause tags like [pause:0.5s]"
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Voice</InputLabel>
                        <Select
                          value={ttsForm.voice || ''}
                          label="Voice"
                          onChange={(e) => setTtsForm({ ...ttsForm, voice: e.target.value })}
                          disabled={loadingVoices}
                        >
                          {loadingVoices ? (
                            <MenuItem disabled>Loading voices...</MenuItem>
                          ) : voiceOptions.length > 0 ? (
                            voiceOptions.map((voice: Voice) => (
                              <MenuItem key={voice.id} value={voice.id}>
                                {String(voice.label || voice.id || 'Unknown Voice')}
                              </MenuItem>
                            ))
                          ) : (
                            <MenuItem disabled>No voices available</MenuItem>
                          )}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Provider</InputLabel>
                        <Select
                          value={ttsForm.provider || ''}
                          label="Provider"
                          onChange={(e) => {
                            const newProvider = String(e.target.value);
                            const newVoices = voices[newProvider] || [];
                            const newVoice = String(newVoices[0]?.name || newVoices[0]?.id || 'af_heart');
                            setTtsForm({
                              ...ttsForm,
                              provider: newProvider,
                              voice: newVoice
                            });
                          }}
                        >
                          {providerOptions.map((provider) => (
                            <MenuItem key={provider} value={provider}>
                              {provider.charAt(0).toUpperCase() + provider.slice(1)}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Output Format</InputLabel>
                        <Select
                          value={ttsForm.response_format || ''}
                          label="Output Format"
                          onChange={(e) => setTtsForm({ ...ttsForm, response_format: e.target.value })}
                        >
                          {formatOptions.map((format: string) => (
                            <MenuItem key={format} value={format}>
                              {format.toUpperCase()}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Model</InputLabel>
                        <Select
                          value={ttsForm.model || ''}
                          label="Model"
                          onChange={(e) => setTtsForm({ ...ttsForm, model: e.target.value })}
                        >
                          {modelOptions.map((model: string) => (
                            <MenuItem key={model} value={model}>
                              {model}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Language (Optional)</InputLabel>
                        <Select
                          value={ttsForm.lang_code}
                          label="Language (Optional)"
                          onChange={(e) => setTtsForm({ ...ttsForm, lang_code: e.target.value })}
                        >
                          <MenuItem value="">
                            <em>Auto-detect</em>
                          </MenuItem>
                          {languageOptions.map((lang: string) => (
                            <MenuItem key={lang} value={lang}>
                              {lang.toUpperCase()}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography gutterBottom>Speed: {ttsForm.speed}x</Typography>
                      <Slider
                        value={ttsForm.speed}
                        onChange={(_, value) => setTtsForm({ ...ttsForm, speed: value as number })}
                        min={0.1}
                        max={2.0}
                        step={0.1}
                        marks={[
                          { value: 0.5, label: '0.5x' },
                          { value: 1.0, label: '1x' },
                          { value: 1.5, label: '1.5x' },
                          { value: 2.0, label: '2x' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography gutterBottom>Volume: {ttsForm.volume_multiplier}x</Typography>
                      <Slider
                        value={ttsForm.volume_multiplier}
                        onChange={(_, value) => setTtsForm({ ...ttsForm, volume_multiplier: value as number })}
                        min={0.1}
                        max={3.0}
                        step={0.1}
                        marks={[
                          { value: 0.5, label: '0.5x' },
                          { value: 1.0, label: '1x' },
                          { value: 2.0, label: '2x' },
                          { value: 3.0, label: '3x' }
                        ]}
                      />
                    </Grid>
                  </Grid>

                  {/* Advanced Options */}
                  <Accordion sx={{ mt: 3 }}>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                      <Typography sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <SettingsIcon fontSize="small" />
                        Advanced Options
                      </Typography>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.return_timestamps}
                                onChange={(e) => setTtsForm({ ...ttsForm, return_timestamps: e.target.checked })}
                              />
                            }
                            label="Return Word Timestamps"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.stream}
                                onChange={(e) => setTtsForm({ ...ttsForm, stream: e.target.checked })}
                              />
                            }
                            label="Stream Response"
                          />
                        </Grid>
                        {ttsForm.stream && (
                          <Grid item xs={12} sm={6}>
                            <FormControl fullWidth>
                              <InputLabel>Stream Format</InputLabel>
                              <Select
                                value={ttsForm.stream_format}
                                label="Stream Format"
                                onChange={(e) => setTtsForm({ ...ttsForm, stream_format: e.target.value })}
                              >
                                {streamFormatOptions.map((format: string) => (
                                  <MenuItem key={format} value={format}>
                                    {format === 'audio' ? 'Raw Audio' : 'Server-Sent Events (SSE)'}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          </Grid>
                        )}
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.remove_filter}
                                onChange={(e) => setTtsForm({ ...ttsForm, remove_filter: e.target.checked })}
                              />
                            }
                            label="Skip Text Preprocessing"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.normalize}
                                onChange={(e) => setTtsForm({ ...ttsForm, normalize: e.target.checked })}
                              />
                            }
                            label="Text Normalization"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.unit_normalization}
                                onChange={(e) => setTtsForm({ ...ttsForm, unit_normalization: e.target.checked })}
                              />
                            }
                            label="Unit Normalization"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={ttsForm.url_normalization}
                                onChange={(e) => setTtsForm({ ...ttsForm, url_normalization: e.target.checked })}
                              />
                            }
                            label="URL Normalization"
                          />
                        </Grid>
                        {ttsForm.provider === 'kokoro' && (
                          <Grid item xs={12}>
                            <TextField
                              fullWidth
                              label="Voice Weights (Optional)"
                              placeholder="e.g., af_heart:0.7,af_bella:0.3"
                              value={ttsForm.voice_weights || ''}
                              onChange={(e) => setTtsForm({ ...ttsForm, voice_weights: e.target.value })}
                              helperText="For voice combinations, specify weights like 'voice1:weight1,voice2:weight2'"
                            />
                          </Grid>
                        )}
                      </Grid>
                    </AccordionDetails>
                  </Accordion>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                    onClick={handleTtsSubmit}
                    disabled={loading || !ttsForm.text.trim()}
                    sx={{ mt: 3, px: 4 }}
                  >
                    {loading ? 'Generating...' : 'Generate Speech'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Voice Options
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                    {loadingVoices ? (
                      <Typography variant="body2" color="text.secondary">
                        Loading voices...
                      </Typography>
                    ) : voiceOptions.length > 0 ? (
                      voiceOptions.slice(0, 6).map((voice: Voice) => (
                        <Chip
                          key={voice.id}
                          label={String(voice.id || 'Unknown')}
                          variant={ttsForm.voice === voice.id ? 'filled' : 'outlined'}
                          onClick={() => setTtsForm({ ...ttsForm, voice: voice.id })}
                          sx={{ cursor: 'pointer' }}
                        />
                      ))
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No voices available
                      </Typography>
                    )}
                  </Box>
                  <Typography variant="body2" color="text.secondary">
                    {loadingVoices
                      ? 'Loading available voices from API...'
                      : `${voiceOptions.length} voices available from ${ttsForm.provider} provider. Kokoro supports voice combinations and weights.`
                    }
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Music Generation Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <MusicIcon color="primary" />
                    Music Generation Configuration
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        label="Music Description"
                        placeholder="Describe the music you want to generate..."
                        value={musicForm.description}
                        onChange={(e) => setMusicForm({ ...musicForm, description: e.target.value })}
                        helperText={`${musicForm.description.length}/500 characters. Be specific about genre, instruments, mood, and style.`}
                        error={musicForm.description.length > 500}
                        inputProps={{ maxLength: 500 }}
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <Typography gutterBottom>Duration: {musicForm.duration}s</Typography>
                      <Slider
                        value={musicForm.duration}
                        onChange={(_, value) => setMusicForm({ ...musicForm, duration: value as number })}
                        min={1}
                        max={30}
                        step={1}
                        marks={[
                          { value: 5, label: '5s' },
                          { value: 15, label: '15s' },
                          { value: 30, label: '30s' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth>
                        <InputLabel>Model Size</InputLabel>
                        <Select
                          value={musicForm.model_size}
                          label="Model Size"
                          onChange={(e) => setMusicForm({ ...musicForm, model_size: e.target.value })}
                        >
                          {modelSizeOptions.map((size) => (
                            <MenuItem key={size} value={size}>
                              {size.charAt(0).toUpperCase() + size.slice(1)} - Meta MusicGen Stereo
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={4}>
                      <FormControl fullWidth>
                        <InputLabel>Output Format</InputLabel>
                        <Select
                          value={musicForm.output_format}
                          label="Output Format"
                          onChange={(e) => setMusicForm({ ...musicForm, output_format: e.target.value })}
                        >
                          {musicFormatOptions.map((format) => (
                            <MenuItem key={format} value={format}>
                              {format.toUpperCase()} {format === 'wav' ? '(High quality, 32kHz stereo)' : '(Compressed, smaller file size)'}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                    onClick={handleMusicSubmit}
                    disabled={loading || !musicForm.description.trim()}
                    sx={{ mt: 3, px: 4 }}
                  >
                    {loading ? 'Generating...' : 'Generate Music'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Music Examples
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Click any example to use it. Be specific about genre, instruments, mood, and tempo for best results.
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {[
                      'lo-fi hip hop with mellow beats and vinyl crackle',
                      'upbeat electronic dance music with synthesizers and bass drops',
                      'acoustic guitar melody in major key with gentle strumming',
                      'orchestral music with strings and piano, romantic style',
                      'smooth jazz with saxophone solo and walking bassline',
                      'calming music with soft piano and gentle strings, peaceful atmosphere',
                      'high-energy music with fast tempo and driving rhythm',
                      'solo piano piece with expressive dynamics and emotional phrasing'
                    ].map((example, index) => (
                      <Chip
                        key={index}
                        label={example}
                        variant="outlined"
                        onClick={() => setMusicForm({ ...musicForm, description: example })}
                        sx={{ 
                          cursor: 'pointer',
                          justifyContent: 'flex-start',
                          height: 'auto',
                          '& .MuiChip-label': {
                            whiteSpace: 'normal',
                            textAlign: 'left'
                          }
                        }}
                      />
                    ))}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Transcription Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <TranscribeIcon color="primary" />
                    Media Transcription Configuration
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Media URL"
                        placeholder="Enter the URL of the audio/video file to transcribe..."
                        value={transcriptionForm.media_url}
                        onChange={(e) => setTranscriptionForm({ ...transcriptionForm, media_url: e.target.value })}
                        helperText="Supports MP3, WAV, M4A, MP4, MOV, AVI, MKV, WebM. Max 1GB, 4 hours duration. S3 URLs and public media URLs supported."
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Language (Optional)</InputLabel>
                        <Select
                          value={transcriptionForm.language}
                          label="Language (Optional)"
                          onChange={(e) => setTranscriptionForm({ ...transcriptionForm, language: e.target.value })}
                        >
                          <MenuItem value="">
                            <em>Auto-detect (Recommended)</em>
                          </MenuItem>
                          <MenuItem value="en">English</MenuItem>
                          <MenuItem value="zh">Chinese</MenuItem>
                          <MenuItem value="de">German</MenuItem>
                          <MenuItem value="es">Spanish</MenuItem>
                          <MenuItem value="ru">Russian</MenuItem>
                          <MenuItem value="ko">Korean</MenuItem>
                          <MenuItem value="fr">French</MenuItem>
                          <MenuItem value="ja">Japanese</MenuItem>
                          <MenuItem value="pt">Portuguese</MenuItem>
                          <MenuItem value="tr">Turkish</MenuItem>
                          <MenuItem value="pl">Polish</MenuItem>
                          <MenuItem value="ca">Catalan</MenuItem>
                          <MenuItem value="nl">Dutch</MenuItem>
                          <MenuItem value="ar">Arabic</MenuItem>
                          <MenuItem value="sv">Swedish</MenuItem>
                          <MenuItem value="it">Italian</MenuItem>
                          <MenuItem value="id">Indonesian</MenuItem>
                          <MenuItem value="hi">Hindi</MenuItem>
                          <MenuItem value="fi">Finnish</MenuItem>
                          <MenuItem value="vi">Vietnamese</MenuItem>
                          <MenuItem value="he">Hebrew</MenuItem>
                          <MenuItem value="uk">Ukrainian</MenuItem>
                          <MenuItem value="el">Greek</MenuItem>
                          <MenuItem value="ms">Malay</MenuItem>
                          <MenuItem value="cs">Czech</MenuItem>
                          <MenuItem value="ro">Romanian</MenuItem>
                          <MenuItem value="da">Danish</MenuItem>
                          <MenuItem value="hu">Hungarian</MenuItem>
                          <MenuItem value="ta">Tamil</MenuItem>
                          <MenuItem value="no">Norwegian</MenuItem>
                          <MenuItem value="th">Thai</MenuItem>
                          <MenuItem value="ur">Urdu</MenuItem>
                          <MenuItem value="hr">Croatian</MenuItem>
                          <MenuItem value="bg">Bulgarian</MenuItem>
                          <MenuItem value="lt">Lithuanian</MenuItem>
                          <MenuItem value="la">Latin</MenuItem>
                          <MenuItem value="mi">Maori</MenuItem>
                          <MenuItem value="ml">Malayalam</MenuItem>
                          <MenuItem value="cy">Welsh</MenuItem>
                          <MenuItem value="sk">Slovak</MenuItem>
                          <MenuItem value="te">Telugu</MenuItem>
                          <MenuItem value="fa">Persian</MenuItem>
                          <MenuItem value="lv">Latvian</MenuItem>
                          <MenuItem value="bn">Bengali</MenuItem>
                          <MenuItem value="sr">Serbian</MenuItem>
                          <MenuItem value="az">Azerbaijani</MenuItem>
                          <MenuItem value="sl">Slovenian</MenuItem>
                          <MenuItem value="kn">Kannada</MenuItem>
                          <MenuItem value="et">Estonian</MenuItem>
                          <MenuItem value="mk">Macedonian</MenuItem>
                          <MenuItem value="br">Breton</MenuItem>
                          <MenuItem value="eu">Basque</MenuItem>
                          <MenuItem value="is">Icelandic</MenuItem>
                          <MenuItem value="hy">Armenian</MenuItem>
                          <MenuItem value="ne">Nepali</MenuItem>
                          <MenuItem value="mn">Mongolian</MenuItem>
                          <MenuItem value="bs">Bosnian</MenuItem>
                          <MenuItem value="kk">Kazakh</MenuItem>
                          <MenuItem value="sq">Albanian</MenuItem>
                          <MenuItem value="sw">Swahili</MenuItem>
                          <MenuItem value="gl">Galician</MenuItem>
                          <MenuItem value="mr">Marathi</MenuItem>
                          <MenuItem value="pa">Punjabi</MenuItem>
                          <MenuItem value="si">Sinhala</MenuItem>
                          <MenuItem value="km">Khmer</MenuItem>
                          <MenuItem value="sn">Shona</MenuItem>
                          <MenuItem value="yo">Yoruba</MenuItem>
                          <MenuItem value="so">Somali</MenuItem>
                          <MenuItem value="af">Afrikaans</MenuItem>
                          <MenuItem value="oc">Occitan</MenuItem>
                          <MenuItem value="ka">Georgian</MenuItem>
                          <MenuItem value="be">Belarusian</MenuItem>
                          <MenuItem value="tg">Tajik</MenuItem>
                          <MenuItem value="sd">Sindhi</MenuItem>
                          <MenuItem value="gu">Gujarati</MenuItem>
                          <MenuItem value="am">Amharic</MenuItem>
                          <MenuItem value="yi">Yiddish</MenuItem>
                          <MenuItem value="lo">Lao</MenuItem>
                          <MenuItem value="uz">Uzbek</MenuItem>
                          <MenuItem value="fo">Faroese</MenuItem>
                          <MenuItem value="ht">Haitian Creole</MenuItem>
                          <MenuItem value="ps">Pashto</MenuItem>
                          <MenuItem value="tk">Turkmen</MenuItem>
                          <MenuItem value="nn">Nynorsk</MenuItem>
                          <MenuItem value="mt">Maltese</MenuItem>
                          <MenuItem value="sa">Sanskrit</MenuItem>
                          <MenuItem value="lb">Luxembourgish</MenuItem>
                          <MenuItem value="my">Myanmar</MenuItem>
                          <MenuItem value="bo">Tibetan</MenuItem>
                          <MenuItem value="tl">Tagalog</MenuItem>
                          <MenuItem value="mg">Malagasy</MenuItem>
                          <MenuItem value="as">Assamese</MenuItem>
                          <MenuItem value="tt">Tatar</MenuItem>
                          <MenuItem value="haw">Hawaiian</MenuItem>
                          <MenuItem value="ln">Lingala</MenuItem>
                          <MenuItem value="ha">Hausa</MenuItem>
                          <MenuItem value="ba">Bashkir</MenuItem>
                          <MenuItem value="jw">Javanese</MenuItem>
                          <MenuItem value="su">Sundanese</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography gutterBottom>Max Words Per Line: {transcriptionForm.max_words_per_line}</Typography>
                      <Slider
                        value={transcriptionForm.max_words_per_line}
                        onChange={(_, value) => setTranscriptionForm({ ...transcriptionForm, max_words_per_line: value as number })}
                        min={1}
                        max={20}
                        step={1}
                        marks={[
                          { value: 5, label: '5' },
                          { value: 10, label: '10' },
                          { value: 15, label: '15' },
                          { value: 20, label: '20' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography gutterBottom>Beam Size: {transcriptionForm.beam_size}</Typography>
                      <Slider
                        value={transcriptionForm.beam_size}
                        onChange={(_, value) => setTranscriptionForm({ ...transcriptionForm, beam_size: value as number })}
                        min={1}
                        max={10}
                        step={1}
                        marks={[
                          { value: 1, label: '1' },
                          { value: 5, label: '5' },
                          { value: 10, label: '10' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Typography variant="subtitle2" sx={{ mb: 2 }}>
                        Output Options
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={transcriptionForm.include_text}
                                onChange={(e) => setTranscriptionForm({ ...transcriptionForm, include_text: e.target.checked })}
                              />
                            }
                            label="Include Plain Text"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={transcriptionForm.include_srt}
                                onChange={(e) => setTranscriptionForm({ ...transcriptionForm, include_srt: e.target.checked })}
                              />
                            }
                            label="Include SRT Subtitles"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={transcriptionForm.word_timestamps}
                                onChange={(e) => setTranscriptionForm({ ...transcriptionForm, word_timestamps: e.target.checked })}
                              />
                            }
                            label="Word-level Timestamps"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={transcriptionForm.include_segments}
                                onChange={(e) => setTranscriptionForm({ ...transcriptionForm, include_segments: e.target.checked })}
                              />
                            }
                            label="Include Segments"
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
                    onClick={handleTranscriptionSubmit}
                    disabled={loading || !transcriptionForm.media_url.trim()}
                    sx={{ mt: 3, px: 4 }}
                  >
                    {loading ? 'Creating Job...' : 'Start Transcription'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Transcription Features
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Powered by faster_whisper with 4-10x faster processing and 75% less memory usage.
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <Box>
                      <Typography variant="subtitle2" color="primary">
                        Plain Text
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Full text transcription for content analysis and searchable content
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="primary">
                        SRT Subtitles
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Standard subtitle format with configurable words per line (6-10 recommended)
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="primary">
                        Word Timestamps
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Individual word timing with confidence scores for karaoke-style applications
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="primary">
                        Segments
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Speech chunks with quality metrics, compression ratios, and speech detection
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="subtitle2" color="primary">
                        Performance
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Processing time: 10-25% of media duration. Supports 99+ languages with auto-detection.
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                {tabValue === 0 && '🎤 Speech Generation Result'}
                {tabValue === 1 && '🎵 Music Generation Result'}
                {tabValue === 2 && '📝 Transcription Result'}
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Success Results */}
              {result && jobStatus === 'completed' && result.jobResult && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    {tabValue === 0 && '🎉 Speech generated successfully!'}
                    {tabValue === 1 && '🎉 Music generated successfully!'}
                    {tabValue === 2 && '🎉 Transcription completed successfully!'}
                  </Alert>
                  
                  {/* Audio Player for Speech and Music */}
                  {(tabValue === 0 || tabValue === 1) && 'audio_url' in result.jobResult && result.jobResult.audio_url && (
                    <AudioPlayer 
                      audioUrl={('audio_url' in result.jobResult) ? result.jobResult.audio_url : ''}
                      title={tabValue === 0 ? 'Generated Speech' : 'Generated Music'}
                    />
                  )}
                  
                  {/* Transcription Results */}
                  {tabValue === 2 && (
                    <TranscriptionResult result={result.jobResult as TranscriptionJobResult} />
                  )}
                  
                  {/* Additional Result Info */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Job Details:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {'tts_engine' in result.jobResult && result.jobResult.tts_engine && (
                          <Grid item xs={6}>
                            <strong>Engine:</strong> {('tts_engine' in result.jobResult) ? result.jobResult.tts_engine : ''}
                          </Grid>
                        )}
                        {'voice' in result.jobResult && result.jobResult.voice && (
                          <Grid item xs={6}>
                            <strong>Voice:</strong> {('voice' in result.jobResult) ? result.jobResult.voice : ''}
                          </Grid>
                        )}
                        {'response_format' in result.jobResult && result.jobResult.response_format && (
                          <Grid item xs={6}>
                            <strong>Format:</strong> {('response_format' in result.jobResult) ? result.jobResult.response_format : ''}
                          </Grid>
                        )}
                        {'estimated_duration' in result.jobResult && result.jobResult.estimated_duration && (
                          <Grid item xs={6}>
                            <strong>Duration:</strong> {('estimated_duration' in result.jobResult) ? result.jobResult.estimated_duration : 0}s
                          </Grid>
                        )}
                        {'word_count' in result.jobResult && result.jobResult.word_count && (
                          <Grid item xs={6}>
                            <strong>Words:</strong> {('word_count' in result.jobResult) ? result.jobResult.word_count : 0}
                          </Grid>
                        )}
                        {'model_used' in result.jobResult && result.jobResult.model_used && (
                          <Grid item xs={6}>
                            <strong>Model:</strong> {('model_used' in result.jobResult) ? result.jobResult.model_used : ''}
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.jobResult && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    {tabValue === 0 && 'Speech generation job created successfully!'}
                    {tabValue === 1 && 'Music generation job created successfully!'}
                    {tabValue === 2 && 'Transcription job created successfully!'}
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default Audio;