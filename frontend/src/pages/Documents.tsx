import React, { useState } from 'react';
import {
  Box,
  Typography,
  <PERSON>rid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  Tabs,
  Tab,
  FormControlLabel,
  Switch,
  Chip,
  IconButton,
} from '@mui/material';
import {
  Upload as UploadIcon,
  Link as LinkIcon,
  Download as DownloadIcon,
  ContentCopy as CopyIcon,
  Check as CheckIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  Article as WordIcon,
  TableChart as ExcelIcon,
  Image as ImageIcon,
  Code as CodeIcon,
} from '@mui/icons-material';
import { directApi } from '../utils/api';

// Type definitions for document processing API
interface DocumentResult {
  markdown_content: string;
  original_filename: string;
  file_type: string;
  word_count: number;
  character_count: number;
  processing_time: number;
}


interface DocumentConversionResult {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  result?: DocumentResult;
  error?: string | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`document-tabpanel-${index}`}
    aria-labelledby={`document-tab-${index}`}
  >
    {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
  </div>
);

const Documents: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<DocumentConversionResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);

  // Form states
  const [urlForm, setUrlForm] = useState({
    url: '',
    includeMetadata: true,
    preserveFormatting: true
  });

  const [fileForm, setFileForm] = useState({
    file: null as File | null,
    includeMetadata: true,
    preserveFormatting: true
  });


  // Get file icon based on extension
  const getFileIcon = (filename?: string) => {
    if (!filename) return <DocumentIcon />;
    
    const ext = filename.toLowerCase().split('.').pop();
    switch (ext) {
      case 'pdf':
        return <PdfIcon color="error" />;
      case 'docx':
      case 'doc':
        return <WordIcon color="primary" />;
      case 'xlsx':
      case 'xls':
        return <ExcelIcon color="success" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
      case 'gif':
        return <ImageIcon color="secondary" />;
      default:
        return <DocumentIcon />;
    }
  };

  // Job status polling function
  const pollJobStatus = async (jobId: string) => {
    const maxAttempts = 120; // 10 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`/api/v1/documents/to-markdown/${jobId}`);

        // Handle array response format
        const responseData = Array.isArray(statusResponse.data) ? statusResponse.data[0] : statusResponse.data;
        const status = responseData.status;
        const jobResult = responseData.result;
        const jobError = responseData.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('Document conversion completed successfully!');
          setResult({
            job_id: jobId,
            status: 'completed',
            result: jobResult,
            error: null
          });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('Document conversion failed');
          setError(jobError || 'Document conversion failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing document... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  const handleUrlSubmit = async () => {
    if (!urlForm.url.trim()) {
      setError('Document URL is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('url', urlForm.url);
      formData.append('include_metadata', urlForm.includeMetadata.toString());
      formData.append('preserve_formatting', urlForm.preserveFormatting.toString());

      // Use axios directly with proper headers for FormData
      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/to-markdown', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setResult({ job_id: data.job_id, status: 'pending' });
        setPollingJobId(data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting document conversion...');
        pollJobStatus(data.job_id);
      } else {
        setError('Failed to start document conversion');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleFileSubmit = async () => {
    if (!fileForm.file) {
      setError('Please select a file to upload');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('file', fileForm.file);
      formData.append('include_metadata', fileForm.includeMetadata.toString());
      formData.append('preserve_formatting', fileForm.preserveFormatting.toString());

      // Use fetch directly with proper headers for FormData
      const apiKey = localStorage.getItem('ouinhi_api_key');
      const response = await fetch('/api/v1/documents/to-markdown', {
        method: 'POST',
        headers: {
          'X-API-Key': apiKey || ''
        },
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data && data.job_id) {
        setResult({ job_id: data.job_id, status: 'pending' });
        setPollingJobId(data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting document conversion...');
        pollJobStatus(data.job_id);
      } else {
        setError('Failed to start document conversion');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileForm(prev => ({ ...prev, file }));
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const downloadMarkdown = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Document Processor 📄
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Convert PDF, Word, Excel, and other documents to clean Markdown format.
        </Typography>
      </Box>

      {/* Tabs */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          aria-label="document conversion tabs"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Convert from URL" icon={<LinkIcon />} id="document-tab-0" />
          <Tab label="Upload File" icon={<UploadIcon />} id="document-tab-1" />
        </Tabs>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    Convert Document from URL
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Document URL"
                        placeholder="https://example.com/document.pdf"
                        value={urlForm.url}
                        onChange={(e) => setUrlForm(prev => ({ ...prev, url: e.target.value }))}
                        helperText="Supported formats: PDF, DOCX, DOC, PPTX, PPT, XLSX, XLS, TXT, HTML"
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={urlForm.includeMetadata}
                            onChange={(e) => setUrlForm(prev => ({ ...prev, includeMetadata: e.target.checked }))}
                          />
                        }
                        label="Include Metadata"
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={urlForm.preserveFormatting}
                            onChange={(e) => setUrlForm(prev => ({ ...prev, preserveFormatting: e.target.checked }))}
                          />
                        }
                        label="Preserve Formatting"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={loading ? <CircularProgress size={20} /> : <LinkIcon />}
                        onClick={handleUrlSubmit}
                        disabled={loading || !urlForm.url.trim()}
                        sx={{ px: 4 }}
                      >
                        {loading ? 'Converting...' : 'Convert Document'}
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Example URLs
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Try these example documents:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setUrlForm(prev => ({ ...prev, url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf' }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Sample PDF
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => setUrlForm(prev => ({ ...prev, url: 'https://file-examples.com/wp-content/storage/2017/02/file-sample_100kB.docx' }))}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      Sample DOCX
                    </Button>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3 }}>
                    Upload Document File
                  </Typography>

                  <Grid container spacing={3}>
                    <Grid item xs={12}>
                      <Button
                        variant="outlined"
                        component="label"
                        fullWidth
                        startIcon={<UploadIcon />}
                        sx={{ mb: 2, py: 2 }}
                      >
                        {fileForm.file ? fileForm.file.name : 'Choose File'}
                        <input
                          type="file"
                          hidden
                          accept=".pdf,.docx,.doc,.pptx,.ppt,.xlsx,.xls,.txt,.html,.htm"
                          onChange={handleFileChange}
                        />
                      </Button>
                      {fileForm.file && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getFileIcon(fileForm.file.name)}
                          <Typography variant="body2">
                            {fileForm.file.name} ({(fileForm.file.size / 1024 / 1024).toFixed(2)} MB)
                          </Typography>
                        </Box>
                      )}
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={fileForm.includeMetadata}
                            onChange={(e) => setFileForm(prev => ({ ...prev, includeMetadata: e.target.checked }))}
                          />
                        }
                        label="Include Metadata"
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={fileForm.preserveFormatting}
                            onChange={(e) => setFileForm(prev => ({ ...prev, preserveFormatting: e.target.checked }))}
                          />
                        }
                        label="Preserve Formatting"
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <Button
                        variant="contained"
                        size="large"
                        startIcon={loading ? <CircularProgress size={20} /> : <UploadIcon />}
                        onClick={handleFileSubmit}
                        disabled={loading || !fileForm.file}
                        sx={{ px: 4 }}
                      >
                        {loading ? 'Uploading...' : 'Upload & Convert'}
                      </Button>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Supported Formats
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Chip icon={<PdfIcon />} label="PDF" color="error" variant="outlined" />
                    <Chip icon={<WordIcon />} label="Word (DOCX/DOC)" color="primary" variant="outlined" />
                    <Chip icon={<ExcelIcon />} label="Excel (XLSX/XLS)" color="success" variant="outlined" />
                    <Chip icon={<DocumentIcon />} label="PowerPoint" color="secondary" variant="outlined" />
                    <Chip icon={<CodeIcon />} label="Text/HTML" color="info" variant="outlined" />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                📄 Document Conversion Result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Results */}
              {result && jobStatus === 'completed' && result.result?.markdown_content && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🎉 Document converted successfully!
                  </Alert>
                  
                  {/* Document Info */}
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Document Information:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {result.result.original_filename && (
                          <Grid item xs={6}>
                            <strong>Filename:</strong> {result.result.original_filename}
                          </Grid>
                        )}
                        {result.result.word_count && (
                          <Grid item xs={6}>
                            <strong>Word Count:</strong> {result.result.word_count}
                          </Grid>
                        )}
                        {result.result.character_count && (
                          <Grid item xs={6}>
                            <strong>Character Count:</strong> {result.result.character_count}
                          </Grid>
                        )}
                        {result.result.processing_time && (
                          <Grid item xs={6}>
                            <strong>Processing Time:</strong> {result.result.processing_time.toFixed(2)}s
                          </Grid>
                        )}
                        {result.result.file_type && (
                          <Grid item xs={6}>
                            <strong>File Type:</strong> {result.result.file_type}
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>

                  {/* Markdown Content */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="subtitle2">📝 Markdown Content:</Typography>
                      <Box>
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(result.result?.markdown_content || '')}
                          title="Copy to clipboard"
                        >
                          {copied ? <CheckIcon color="success" /> : <CopyIcon />}
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => downloadMarkdown(
                            result.result?.markdown_content || '',
                            `${result.result?.original_filename || 'document'}.md`
                          )}
                          title="Download as .md"
                        >
                          <DownloadIcon />
                        </IconButton>
                      </Box>
                    </Box>
                    <Paper sx={{ p: 3, bgcolor: '#f0f9ff', maxHeight: 400, overflow: 'auto' }}>
                      <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}>
                        {result.result.markdown_content}
                      </Typography>
                    </Paper>
                  </Box>
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result?.markdown_content && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Document conversion job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default Documents;
