import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Slider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow
} from '@mui/material';
import {
  YouTube as YouTubeIcon,
  AutoAwesome as SmartIcon,
  VideoFile as VideoIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  Face as FaceIcon,
  VolumeUp as AudioIcon,
  CropFree as CropIcon,
  AutoFixHigh as EnhanceIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';

interface YtShortsParams extends Record<string, unknown> {
  video_url: string;
  max_duration?: number;
  quality?: string;
  output_format?: string;
  use_ai_highlight?: boolean;
  crop_to_vertical?: boolean;
  speaker_tracking?: boolean;
  custom_start_time?: number | null;
  custom_end_time?: number | null;
  enhance_audio?: boolean;
  smooth_transitions?: boolean;
  create_thumbnail?: boolean;
  target_resolution?: string;
  audio_enhancement_level?: string;
  face_tracking_sensitivity?: string;
  cookies_url?: string;
}

interface ProcessingStats {
  download_size?: number;
  audio_extracted?: boolean;
  transcription_segments?: number;
  ai_highlight_detected?: boolean;
  highlight_extracted?: boolean;
  dynamic_crop_applied?: boolean;
  optimized_for_shorts?: boolean;
  thumbnail_created?: boolean;
  uploaded_to_s3?: boolean;
}

interface QualityCheck {
  file_size?: number;
  duration?: number;
  resolution?: string;
  bitrate?: number;
  av_sync?: boolean;
  has_audio?: boolean;
  has_video?: boolean;
}

interface FeaturesUsed {
  speaker_tracking?: boolean;
  audio_enhancement?: boolean;
  smooth_transitions?: boolean;
  dynamic_cropping?: boolean;
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    url?: string;
    path?: string;
    duration?: number;
    original_title?: string;
    original_duration?: number;
    highlight_start?: number;
    highlight_end?: number;
    is_vertical?: boolean;
    ai_generated?: boolean;
    quality?: string;
    thumbnail_url?: string;
    processing_stats?: ProcessingStats;
    quality_check?: QualityCheck;
    features_used?: FeaturesUsed;
    [key: string]: unknown;
  };
  error?: string | null;
}

const YtShorts: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<JobResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<'pending' | 'processing' | 'completed' | 'failed' | null>(null);
  const [jobProgress, setJobProgress] = useState<string>('');
  const [pollingJobId, setPollingJobId] = useState<string | null>(null);

  // Form state
  const [form, setForm] = useState<YtShortsParams>({
    video_url: '',
    max_duration: 60,
    quality: 'high',
    output_format: 'mp4',
    use_ai_highlight: true,
    crop_to_vertical: true,
    speaker_tracking: true,
    custom_start_time: null,
    custom_end_time: null,
    enhance_audio: true,
    smooth_transitions: true,
    create_thumbnail: true,
    target_resolution: '720x1280',
    audio_enhancement_level: 'speech',
    face_tracking_sensitivity: 'medium',
    cookies_url: ''
  });

  // Job status polling function
  const pollJobStatus = async (jobId: string) => {
    const maxAttempts = 240; // 20 minutes max for YouTube Shorts processing
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`/api/v1/yt-shorts/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setJobProgress('YouTube Short created successfully!');
          setResult({ job_id: jobId, result: jobResult, status: 'completed' });
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setJobProgress('YouTube Short creation failed');
          setError(jobError || 'YouTube Short creation failed');
          setLoading(false);
          return;
        } else if (status === 'processing') {
          setJobProgress(`Processing YouTube Short... (${attempts}/${maxAttempts})`);
        } else {
          setJobProgress(`Queued... (${attempts}/${maxAttempts})`);
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout. Please check status manually.');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  const handleSubmit = async () => {
    if (!form.video_url.trim()) {
      setError('YouTube URL is required');
      return;
    }

    // Validate YouTube URL
    const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)[\w-]+/;
    if (!youtubeRegex.test(form.video_url)) {
      setError('Please enter a valid YouTube URL');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await directApi.post('/api/v1/yt-shorts/create', form);
      if (response.data && response.data.job_id) {
        setResult(response.data);
        setPollingJobId(response.data.job_id);
        setJobStatus('pending');
        setJobProgress('Job created, starting YouTube Short processing...');
        pollJobStatus(response.data.job_id);
      } else {
        setError('Failed to create YouTube Shorts job');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setError(errorMessage);
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          YouTube Shorts Generator 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          AI-powered YouTube Shorts creation with advanced speaker tracking, face detection, and quality optimization.
        </Typography>
      </Box>

      {/* Main Form */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                    <YouTubeIcon color="primary" />
                    YouTube Shorts Settings
                  </Typography>

                  <Grid container spacing={3}>
                    {/* YouTube URL */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="YouTube Video URL"
                        placeholder="https://www.youtube.com/watch?v=..."
                        value={form.video_url}
                        onChange={(e) => setForm({ ...form, video_url: e.target.value })}
                        helperText="Enter the YouTube video URL to convert to shorts"
                      />
                    </Grid>

                    {/* Cookies URL for restricted videos */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Cookies URL (Optional)"
                        placeholder="https://example.com/cookies.txt"
                        value={form.cookies_url}
                        onChange={(e) => setForm({ ...form, cookies_url: e.target.value })}
                        helperText="URL to download cookies file for accessing restricted YouTube videos"
                      />
                    </Grid>

                    {/* Basic Settings */}
                    <Grid item xs={12} md={4}>
                      <Typography gutterBottom>Max Duration: {form.max_duration}s</Typography>
                      <Slider
                        value={form.max_duration}
                        onChange={(_e, value) => setForm({ ...form, max_duration: Array.isArray(value) ? value[0] : value })}
                        min={5}
                        max={300}
                        step={5}
                        marks={[
                          { value: 15, label: '15s' },
                          { value: 60, label: '60s' },
                          { value: 120, label: '2m' },
                          { value: 300, label: '5m' }
                        ]}
                      />
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Quality</InputLabel>
                        <Select
                          value={form.quality}
                          label="Quality"
                          onChange={(e) => setForm({ ...form, quality: e.target.value })}
                        >
                          <MenuItem value="low">Low (Fast)</MenuItem>
                          <MenuItem value="medium">Medium (Balanced)</MenuItem>
                          <MenuItem value="high">High (Recommended)</MenuItem>
                          <MenuItem value="ultra">Ultra (Best Quality)</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Target Resolution</InputLabel>
                        <Select
                          value={form.target_resolution}
                          label="Target Resolution"
                          onChange={(e) => setForm({ ...form, target_resolution: e.target.value })}
                        >
                          <MenuItem value="480x854">480x854 (Low)</MenuItem>
                          <MenuItem value="720x1280">720x1280 (HD)</MenuItem>
                          <MenuItem value="1080x1920">1080x1920 (Full HD)</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    {/* AI and Processing Options */}
                    <Grid item xs={12}>
                      <Accordion defaultExpanded>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <SmartIcon color="primary" />
                            AI & Processing Options
                          </Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.use_ai_highlight}
                                    onChange={(e) => setForm({ ...form, use_ai_highlight: e.target.checked })}
                                  />
                                }
                                label="AI Highlight Detection"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Use GPT-4 to automatically detect the best segments
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.speaker_tracking}
                                    onChange={(e) => setForm({ ...form, speaker_tracking: e.target.checked })}
                                  />
                                }
                                label="Speaker Tracking"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Advanced speaker detection and tracking
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.crop_to_vertical}
                                    onChange={(e) => setForm({ ...form, crop_to_vertical: e.target.checked })}
                                  />
                                }
                                label="Vertical Crop (9:16)"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Dynamic face-following crop for mobile formats
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.enhance_audio}
                                    onChange={(e) => setForm({ ...form, enhance_audio: e.target.checked })}
                                  />
                                }
                                label="Audio Enhancement"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Speech optimization and noise reduction
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.smooth_transitions}
                                    onChange={(e) => setForm({ ...form, smooth_transitions: e.target.checked })}
                                  />
                                }
                                label="Smooth Transitions"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Add fade effects and smooth transitions
                              </Typography>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={form.create_thumbnail}
                                    onChange={(e) => setForm({ ...form, create_thumbnail: e.target.checked })}
                                  />
                                }
                                label="Generate Thumbnail"
                              />
                              <Typography variant="caption" color="text.secondary" display="block">
                                Create preview thumbnail automatically
                              </Typography>
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>

                    {/* Advanced Settings */}
                    <Grid item xs={12}>
                      <Accordion>
                        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                          <Typography variant="h6">Advanced Settings</Typography>
                        </AccordionSummary>
                        <AccordionDetails>
                          <Grid container spacing={2}>
                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Audio Enhancement Level</InputLabel>
                                <Select
                                  value={form.audio_enhancement_level}
                                  label="Audio Enhancement Level"
                                  onChange={(e) => setForm({ ...form, audio_enhancement_level: e.target.value })}
                                >
                                  <MenuItem value="speech">Speech Optimization</MenuItem>
                                  <MenuItem value="music">Music Enhancement</MenuItem>
                                  <MenuItem value="auto">Auto Detection</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Face Tracking Sensitivity</InputLabel>
                                <Select
                                  value={form.face_tracking_sensitivity}
                                  label="Face Tracking Sensitivity"
                                  onChange={(e) => setForm({ ...form, face_tracking_sensitivity: e.target.value })}
                                >
                                  <MenuItem value="low">Low</MenuItem>
                                  <MenuItem value="medium">Medium</MenuItem>
                                  <MenuItem value="high">High</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={4}>
                              <FormControl fullWidth>
                                <InputLabel>Output Format</InputLabel>
                                <Select
                                  value={form.output_format}
                                  label="Output Format"
                                  onChange={(e) => setForm({ ...form, output_format: e.target.value })}
                                >
                                  <MenuItem value="mp4">MP4 (Recommended)</MenuItem>
                                  <MenuItem value="webm">WebM</MenuItem>
                                  <MenuItem value="mov">MOV</MenuItem>
                                </Select>
                              </FormControl>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Custom Start Time (seconds)"
                                placeholder="Leave empty for AI selection"
                                value={form.custom_start_time || ''}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  custom_start_time: e.target.value ? parseInt(e.target.value) : null 
                                })}
                                helperText="Override AI highlight detection"
                              />
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <TextField
                                fullWidth
                                type="number"
                                label="Custom End Time (seconds)"
                                placeholder="Leave empty for AI selection"
                                value={form.custom_end_time || ''}
                                onChange={(e) => setForm({ 
                                  ...form, 
                                  custom_end_time: e.target.value ? parseInt(e.target.value) : null 
                                })}
                                helperText="Override AI highlight detection"
                              />
                            </Grid>
                          </Grid>
                        </AccordionDetails>
                      </Accordion>
                    </Grid>
                  </Grid>

                  <Button
                    variant="contained"
                    size="large"
                    startIcon={loading ? <CircularProgress size={20} /> : <VideoIcon />}
                    onClick={handleSubmit}
                    disabled={loading || !form.video_url.trim()}
                    sx={{ mt: 3, px: 4 }}
                  >
                    {loading ? 'Processing...' : 'Create YouTube Short'}
                  </Button>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                <CardContent>
                  <Typography variant="h6" sx={{ mb: 2 }}>
                    Advanced Features
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <Chip icon={<SmartIcon />} label="AI Highlight Detection" variant="outlined" />
                    <Chip icon={<FaceIcon />} label="Face Tracking & Cropping" variant="outlined" />
                    <Chip icon={<AudioIcon />} label="Voice Activity Detection" variant="outlined" />
                    <Chip icon={<CropIcon />} label="Dynamic Vertical Crop" variant="outlined" />
                    <Chip icon={<EnhanceIcon />} label="Audio Enhancement" variant="outlined" />
                    <Chip icon={<VideoIcon />} label="Professional Optimization" variant="outlined" />
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                    This generator uses advanced AI to automatically detect the best segments, track speakers, 
                    and optimize your content for YouTube Shorts, TikTok, and Instagram Reels.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Results Section */}
      {(result || error || jobStatus) && (
        <Box sx={{ mt: 4, mb: 6 }}>
          <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
            <CardContent sx={{ p: 4, pb: 5 }}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                🎬 YouTube Short Processing Result
                {jobStatus && jobStatus !== 'completed' && (
                  <CircularProgress size={20} sx={{ ml: 1 }} />
                )}
              </Typography>
              
              {/* Job Status */}
              {jobStatus && (
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    Job ID: {pollingJobId}
                  </Typography>
                  <LinearProgress 
                    variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                    value={jobStatus === 'completed' ? 100 : undefined}
                    sx={{ mb: 1, height: 6, borderRadius: 3 }}
                  />
                  <Typography variant="body2" sx={{ 
                    color: jobStatus === 'completed' ? 'success.main' : 
                           jobStatus === 'failed' ? 'error.main' : 'info.main'
                  }}>
                    {jobProgress}
                  </Typography>
                </Box>
              )}
              
              {/* Error Display */}
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              )}
              
              {/* Success Results */}
              {result && jobStatus === 'completed' && result.result && (
                <Box>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    🎉 YouTube Short created successfully!
                  </Alert>
                  
                  {/* Video Preview and Download */}
                  {result.result.url && (
                    <Box sx={{ mb: 3 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          Generated YouTube Short
                        </Typography>
                        <Button
                          startIcon={<DownloadIcon />}
                          href={result.result.url}
                          target="_blank"
                          variant="contained"
                          size="small"
                        >
                          Download Video
                        </Button>
                      </Box>
                      
                      <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                        <video
                          src={result.result.url}
                          controls
                          style={{
                            width: '100%',
                            maxHeight: '400px',
                            borderRadius: '8px'
                          }}
                        />
                      </Paper>
                      
                      {/* Thumbnail */}
                      {result.result.thumbnail_url && (
                        <Box sx={{ mt: 2, textAlign: 'center' }}>
                          <Typography variant="subtitle2" sx={{ mb: 1 }}>Thumbnail:</Typography>
                          <img
                            src={result.result.thumbnail_url}
                            alt="Video thumbnail"
                            style={{
                              maxWidth: '200px',
                              maxHeight: '150px',
                              borderRadius: '8px',
                              border: '1px solid #e0e0e0'
                            }}
                          />
                        </Box>
                      )}
                    </Box>
                  )}
                  
                  {/* Video Details */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Video Details:</Typography>
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                      <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                        {result.result.original_title && (
                          <Grid item xs={12}>
                            <strong>Original Title:</strong> {result.result.original_title}
                          </Grid>
                        )}
                        {result.result.duration && (
                          <Grid item xs={12} md={4}>
                            <strong>Duration:</strong> {formatDuration(result.result.duration)}
                          </Grid>
                        )}
                        {result.result.original_duration && (
                          <Grid item xs={12} md={4}>
                            <strong>Original Duration:</strong> {formatDuration(result.result.original_duration)}
                          </Grid>
                        )}
                        {result.result.quality && (
                          <Grid item xs={12} md={4}>
                            <strong>Quality:</strong> {result.result.quality}
                          </Grid>
                        )}
                        {result.result.highlight_start !== undefined && result.result.highlight_end !== undefined && (
                          <Grid item xs={12} md={6}>
                            <strong>Highlight Segment:</strong> {formatDuration(result.result.highlight_start)} - {formatDuration(result.result.highlight_end)}
                          </Grid>
                        )}
                        {result.result.is_vertical !== undefined && (
                          <Grid item xs={12} md={6}>
                            <strong>Vertical Format:</strong> {result.result.is_vertical ? 'Yes' : 'No'}
                          </Grid>
                        )}
                        {result.result.ai_generated !== undefined && (
                          <Grid item xs={12} md={6}>
                            <strong>AI Generated:</strong> {result.result.ai_generated ? 'Yes' : 'No'}
                          </Grid>
                        )}
                      </Grid>
                    </Paper>
                  </Box>

                  {/* Processing Statistics */}
                  {result.result.processing_stats && (
                    <Accordion sx={{ mt: 2 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">Processing Statistics</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <TableContainer component={Paper} variant="outlined">
                          <Table size="small">
                            <TableBody>
                              {Object.entries(result.result.processing_stats).map(([key, value]) => (
                                <TableRow key={key}>
                                  <TableCell component="th" scope="row" sx={{ fontWeight: 600, width: '50%' }}>
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </TableCell>
                                  <TableCell>
                                    {key === 'download_size' && typeof value === 'number' 
                                      ? formatFileSize(value)
                                      : typeof value === 'boolean' 
                                      ? (value ? '✅ Yes' : '❌ No')
                                      : String(value)
                                    }
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </AccordionDetails>
                    </Accordion>
                  )}

                  {/* Quality Check */}
                  {result.result.quality_check && (
                    <Accordion sx={{ mt: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Typography variant="subtitle1">Quality Check</Typography>
                      </AccordionSummary>
                      <AccordionDetails>
                        <TableContainer component={Paper} variant="outlined">
                          <Table size="small">
                            <TableBody>
                              {Object.entries(result.result.quality_check).map(([key, value]) => (
                                <TableRow key={key}>
                                  <TableCell component="th" scope="row" sx={{ fontWeight: 600, width: '50%' }}>
                                    {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </TableCell>
                                  <TableCell>
                                    {key === 'file_size' && typeof value === 'number' 
                                      ? formatFileSize(value)
                                      : key === 'duration' && typeof value === 'number'
                                      ? formatDuration(value)
                                      : key === 'bitrate' && typeof value === 'number'
                                      ? `${(value / 1000).toFixed(0)} kbps`
                                      : typeof value === 'boolean' 
                                      ? (value ? '✅ Yes' : '❌ No')
                                      : String(value)
                                    }
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      </AccordionDetails>
                    </Accordion>
                  )}

                  {/* Features Used */}
                  {result.result.features_used && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1 }}>🔧 Features Used:</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {Object.entries(result.result.features_used).map(([key, value]) => (
                          value ? (
                            <Chip 
                              key={key}
                              label={key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                              size="small"
                              color="primary"
                              variant="outlined"
                            />
                          ) : null
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              )}
              
              {/* Initial Job Created Message */}
              {result && !result.result && jobStatus !== 'completed' && (
                <Box>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    YouTube Shorts processing job created successfully!
                  </Alert>
                  <Typography variant="body2" color="text.secondary">
                    Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                      {result.job_id}
                    </code>
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </Box>
      )}
    </Box>
  );
};

export default YtShorts;