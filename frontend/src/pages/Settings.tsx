import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Card,
  CardContent,
  Grid,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Divider,
  Alert,
  alpha,
  useTheme,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Save as SaveIcon,
  RestorePageTwoTone as ResetIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Api as ApiIcon
} from '@mui/icons-material';

interface SettingsState {
  autoRefresh: boolean;
  emailNotifications: boolean;
  apiLogging: boolean;
  maxConcurrentJobs: number;
  defaultVideoResolution: string;
  storageRetentionDays: number;
  error: string | null;
  success: string | null;
}

const Settings: React.FC = () => {
  const theme = useTheme();
  const [settings, setSettings] = useState<SettingsState>({
    autoRefresh: true,
    emailNotifications: true,
    apiLogging: true,
    maxConcurrentJobs: 5,
    defaultVideoResolution: '1080x1920',
    storageRetentionDays: 90,
    error: null,
    success: null
  });

  const handleSave = async () => {
    try {
      // TODO: Implement actual API call to save settings
      console.log('Saving settings:', settings);
      setSettings(prev => ({
        ...prev,
        success: 'Settings saved successfully',
        error: null
      }));
    } catch (error) {
      setSettings(prev => ({
        ...prev,
        error: 'Failed to save settings',
        success: null
      }));
    }
  };

  const handleReset = () => {
    setSettings({
      autoRefresh: true,
      emailNotifications: true,
      apiLogging: true,
      maxConcurrentJobs: 5,
      defaultVideoResolution: '1080x1920',
      storageRetentionDays: 90,
      error: null,
      success: null
    });
  };

  const SettingCard: React.FC<{
    title: string;
    description: string;
    icon: React.ReactNode;
    children: React.ReactNode;
  }> = ({ title, description, icon, children }) => (
    <Card elevation={1}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
          <Box 
            sx={{ 
              p: 1, 
              borderRadius: 1, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            {icon}
          </Box>
          <Box>
            <Typography variant="h6" fontWeight="medium">
              {title}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {description}
            </Typography>
          </Box>
        </Box>
        {children}
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            <SettingsIcon />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              Settings
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Configure system preferences and behavior
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Reset to defaults">
            <IconButton onClick={handleReset}>
              <ResetIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={handleSave}
          >
            Save Settings
          </Button>
        </Box>
      </Box>

      {/* Alerts */}
      {settings.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {settings.error}
        </Alert>
      )}
      
      {settings.success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {settings.success}
        </Alert>
      )}

      {/* Settings Grid */}
      <Grid container spacing={3}>
        {/* General Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="General"
            description="Basic application preferences"
            icon={<SettingsIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoRefresh}
                    onChange={(e) => setSettings(prev => ({ ...prev, autoRefresh: e.target.checked }))}
                  />
                }
                label="Auto-refresh dashboard data"
              />
              
              <TextField
                label="Default Video Resolution"
                value={settings.defaultVideoResolution}
                onChange={(e) => setSettings(prev => ({ ...prev, defaultVideoResolution: e.target.value }))}
                helperText="Format: WIDTHxHEIGHT (e.g., 1080x1920)"
                fullWidth
                size="small"
              />
              
              <TextField
                label="Max Concurrent Jobs"
                type="number"
                value={settings.maxConcurrentJobs}
                onChange={(e) => setSettings(prev => ({ ...prev, maxConcurrentJobs: parseInt(e.target.value) || 5 }))}
                helperText="Maximum number of jobs to process simultaneously"
                fullWidth
                size="small"
                inputProps={{ min: 1, max: 20 }}
              />
            </Box>
          </SettingCard>
        </Grid>

        {/* Notifications */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="Notifications"
            description="Email and system notifications"
            icon={<NotificationsIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emailNotifications}
                    onChange={(e) => setSettings(prev => ({ ...prev, emailNotifications: e.target.checked }))}
                  />
                }
                label="Email notifications for job completion"
              />
              
              <Typography variant="body2" color="text.secondary">
                Receive email notifications when video generation jobs complete or fail.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* API Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="API Configuration"
            description="API behavior and logging"
            icon={<ApiIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.apiLogging}
                    onChange={(e) => setSettings(prev => ({ ...prev, apiLogging: e.target.checked }))}
                  />
                }
                label="Enable detailed API logging"
              />
              
              <Typography variant="body2" color="text.secondary">
                Log detailed information about API requests and responses for debugging.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* Storage Settings */}
        <Grid item xs={12} md={6}>
          <SettingCard
            title="Storage"
            description="Data retention and cleanup"
            icon={<StorageIcon />}
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <TextField
                label="Storage Retention (Days)"
                type="number"
                value={settings.storageRetentionDays}
                onChange={(e) => setSettings(prev => ({ ...prev, storageRetentionDays: parseInt(e.target.value) || 90 }))}
                helperText="Number of days to retain generated videos and data"
                fullWidth
                size="small"
                inputProps={{ min: 1, max: 365 }}
              />
              
              <Typography variant="body2" color="text.secondary">
                Videos and job data older than this will be automatically deleted.
              </Typography>
            </Box>
          </SettingCard>
        </Grid>

        {/* System Information */}
        <Grid item xs={12}>
          <Paper elevation={1} sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Box 
                sx={{ 
                  p: 1, 
                  borderRadius: 1, 
                  backgroundColor: alpha(theme.palette.info.main, 0.1),
                  color: theme.palette.info.main
                }}
              >
                <SecurityIcon />
              </Box>
              <Typography variant="h6" fontWeight="medium">
                System Information
              </Typography>
            </Box>
            
            <Divider sx={{ mb: 2 }} />
            
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Version
                </Typography>
                <Typography variant="body1" fontWeight="medium">
                  Ouinhi v1.0.0
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  API Status
                </Typography>
                <Typography variant="body1" fontWeight="medium" color="success.main">
                  Operational
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Database
                </Typography>
                <Typography variant="body1" fontWeight="medium" color="success.main">
                  Connected
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="text.secondary">
                  Storage
                </Typography>
                <Typography variant="body1" fontWeight="medium" color="success.main">
                  Available
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Settings;