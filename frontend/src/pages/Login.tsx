import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Box,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  InputAdornment,
  IconButton
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  VideoLibrary,
  Key
} from '@mui/icons-material';

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { setApiKey, checkAuth } = useAuth();
  const [apiKeyInput, setApiKeyInput] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showApiKey, setShowApiKey] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKeyInput.trim()) {
      setError('Please enter your API key');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Set the API key temporarily to test it
      setApiKey(apiKeyInput.trim());
      
      // Verify the API key works
      const isValid = await checkAuth();
      
      if (isValid) {
        navigate('/');
      } else {
        setError('Invalid API key. Please check your key and try again.');
      }
    } catch (err: any) {
      console.error('Login error:', err);
      setError('Failed to authenticate. Please check your API key and try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleShowApiKey = () => {
    setShowApiKey(!showApiKey);
  };

  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
        }}
      >
        <Paper elevation={3} sx={{ padding: 4, width: '100%' }}>
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              mb: 3
            }}
          >
            <VideoLibrary sx={{ fontSize: 48, color: 'primary.main', mb: 1 }} />
            <Typography component="h1" variant="h4" align="center" gutterBottom>
              Ouinhi
            </Typography>
            <Typography variant="h6" align="center" color="text.secondary">
              AI Video Creation Platform
            </Typography>
          </Box>

          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" color="text.secondary" align="center">
              Enter your API key to access the Ouinhi video creation tools
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <TextField
              fullWidth
              label="API Key"
              type={showApiKey ? 'text' : 'password'}
              value={apiKeyInput}
              onChange={(e) => setApiKeyInput(e.target.value)}
              margin="normal"
              required
              disabled={loading}
              placeholder="Enter your Ouinhi API key"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Key color="action" />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleToggleShowApiKey}
                      edge="end"
                      disabled={loading}
                    >
                      {showApiKey ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={loading}
              sx={{ mt: 3, mb: 2 }}
            >
              {loading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Connect'
              )}
            </Button>
          </Box>

          <Box sx={{ mt: 3 }}>
            <Typography variant="body2" color="text.secondary" align="center">
              Don't have an API key? Contact your administrator or check the{' '}
              <Typography
                component="span"
                variant="body2"
                color="primary"
                sx={{ cursor: 'pointer', textDecoration: 'underline' }}
                onClick={() => window.open('/docs', '_blank')}
              >
                documentation
              </Typography>
              {' '}for more information.
            </Typography>
          </Box>

          <Box sx={{ mt: 4, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>Security Note:</strong> Your API key is stored locally in your browser
              and is never sent to external servers except for API authentication.
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default Login;