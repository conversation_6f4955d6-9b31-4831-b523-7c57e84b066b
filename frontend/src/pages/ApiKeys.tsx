import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Pagination,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Card,
  CardContent,
  Grid,
  alpha,
  useTheme,
  Snackbar,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  VpnKey as ApiKeyIcon,
  Add as AddIcon,
  ContentCopy as CopyIcon,
  Visibility as ViewIcon,
  VisibilityOff,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Today as TodayIcon,
  TrendingUp as UsageIcon
} from '@mui/icons-material';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  userId: string;
  userEmail: string;
  isActive: boolean;
  createdAt: string;
  lastUsed?: string;
  expiresAt?: string;
  usageCount: number;
  rateLimit: number;
  permissions: string[];
}

interface ApiKeysState {
  apiKeys: ApiKey[];
  loading: boolean;
  error: string | null;
  page: number;
  totalPages: number;
  searchQuery: string;
  selectedStatus: string;
  dialogOpen: boolean;
  editingKey: ApiKey | null;
  deleteDialogOpen: boolean;
  keyToDelete: ApiKey | null;
  visibleKeys: Set<string>;
  snackbarOpen: boolean;
  snackbarMessage: string;
}

const ApiKeys: React.FC = () => {
  const theme = useTheme();
  const [state, setState] = useState<ApiKeysState>({
    apiKeys: [],
    loading: false,
    error: null,
    page: 1,
    totalPages: 1,
    searchQuery: '',
    selectedStatus: 'all',
    dialogOpen: false,
    editingKey: null,
    deleteDialogOpen: false,
    keyToDelete: null,
    visibleKeys: new Set(),
    snackbarOpen: false,
    snackbarMessage: ''
  });

  // Mock API keys data
  const mockApiKeys: ApiKey[] = useMemo(() => [
    {
      id: '1',
      name: 'Production API',
      key: 'oui_sk_1234567890abcdef1234567890abcdef',
      userId: '1',
      userEmail: '<EMAIL>',
      isActive: true,
      createdAt: '2024-01-15T10:30:00Z',
      lastUsed: '2024-01-20T14:22:00Z',
      expiresAt: '2024-12-31T23:59:59Z',
      usageCount: 1247,
      rateLimit: 1000,
      permissions: ['video:create', 'video:read', 'audio:create']
    },
    {
      id: '2',
      name: 'Development Key',
      key: 'oui_sk_abcdef1234567890abcdef1234567890',
      userId: '2',
      userEmail: '<EMAIL>',
      isActive: true,
      createdAt: '2024-01-18T09:15:00Z',
      lastUsed: '2024-01-19T16:45:00Z',
      usageCount: 89,
      rateLimit: 100,
      permissions: ['video:read', 'audio:read']
    },
    {
      id: '3',
      name: 'Test Environment',
      key: 'oui_sk_fedcba0987654321fedcba0987654321',
      userId: '1',
      userEmail: '<EMAIL>',
      isActive: false,
      createdAt: '2024-01-10T11:20:00Z',
      usageCount: 5,
      rateLimit: 50,
      permissions: ['video:read']
    }
  ], []);

  const loadApiKeys = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    try {
      // TODO: Replace with actual API call
      setTimeout(() => {
        setState(prev => ({
          ...prev,
          apiKeys: mockApiKeys,
          totalPages: 1,
          loading: false
        }));
      }, 1000);
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: 'Failed to load API keys',
        loading: false
      }));
    }
  }, [mockApiKeys]);

  useEffect(() => {
    loadApiKeys();
  }, [loadApiKeys]);

  const handleCreateKey = () => {
    setState(prev => ({
      ...prev,
      dialogOpen: true,
      editingKey: null
    }));
  };

  const handleEditKey = (apiKey: ApiKey) => {
    setState(prev => ({
      ...prev,
      dialogOpen: true,
      editingKey: apiKey
    }));
  };

  const handleDeleteKey = (apiKey: ApiKey) => {
    setState(prev => ({
      ...prev,
      deleteDialogOpen: true,
      keyToDelete: apiKey
    }));
  };

  const confirmDelete = async () => {
    if (!state.keyToDelete) return;
    
    try {
      // TODO: Implement actual delete API call
      setState(prev => ({
        ...prev,
        deleteDialogOpen: false,
        keyToDelete: null,
        snackbarOpen: true,
        snackbarMessage: 'API key deleted successfully'
      }));
      loadApiKeys();
    } catch (error) {
      setState(prev => ({ ...prev, error: 'Failed to delete API key' }));
    }
  };

  const toggleKeyVisibility = (keyId: string) => {
    setState(prev => {
      const newVisibleKeys = new Set(prev.visibleKeys);
      if (newVisibleKeys.has(keyId)) {
        newVisibleKeys.delete(keyId);
      } else {
        newVisibleKeys.add(keyId);
      }
      return { ...prev, visibleKeys: newVisibleKeys };
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setState(prev => ({
      ...prev,
      snackbarOpen: true,
      snackbarMessage: 'API key copied to clipboard'
    }));
  };

  const maskApiKey = (key: string) => {
    return `${key.substring(0, 12)}${'*'.repeat(20)}${key.substring(key.length - 4)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const StatsCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    suffix?: string;
  }> = ({ title, value, icon, color, suffix = '' }) => (
    <Card 
      elevation={1}
      sx={{ 
        background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
        border: `1px solid ${alpha(color, 0.1)}`
      }}
    >
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" fontWeight="bold" color={color}>
              {value.toLocaleString()}{suffix}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
          </Box>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(color, 0.1),
              color: color
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 4 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box 
            sx={{ 
              p: 1.5, 
              borderRadius: 2, 
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            <ApiKeyIcon />
          </Box>
          <Box>
            <Typography variant="h4" fontWeight="bold">
              API Keys Management
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Manage API keys and access permissions
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Tooltip title="Refresh API Keys">
            <IconButton onClick={loadApiKeys} disabled={state.loading}>
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateKey}
          >
            Create API Key
          </Button>
        </Box>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total API Keys"
            value={mockApiKeys.length}
            icon={<ApiKeyIcon />}
            color={theme.palette.primary.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Active Keys"
            value={mockApiKeys.filter(k => k.isActive).length}
            icon={<ActiveIcon />}
            color={theme.palette.success.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="Total Usage"
            value={mockApiKeys.reduce((sum, k) => sum + k.usageCount, 0)}
            icon={<UsageIcon />}
            color={theme.palette.info.main}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatsCard
            title="This Month"
            value={mockApiKeys.filter(k => 
              new Date(k.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            ).length}
            icon={<TodayIcon />}
            color={theme.palette.warning.main}
          />
        </Grid>
      </Grid>

      {/* Filters */}
      <Paper elevation={1} sx={{ p: 3, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              placeholder="Search API keys by name, user, or key..."
              value={state.searchQuery}
              onChange={(e) => setState(prev => ({ ...prev, searchQuery: e.target.value }))}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Status</InputLabel>
              <Select
                value={state.selectedStatus}
                label="Status"
                onChange={(e) => setState(prev => ({ ...prev, selectedStatus: e.target.value }))}
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="inactive">Inactive</MenuItem>
                <MenuItem value="expired">Expired</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {state.error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {state.error}
        </Alert>
      )}

      {/* API Keys Table */}
      <TableContainer component={Paper} elevation={1}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>User</TableCell>
              <TableCell>API Key</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Usage</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Last Used</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.apiKeys.map((apiKey) => (
              <TableRow key={apiKey.id} hover>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {apiKey.name}
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 0.5, mt: 0.5 }}>
                    {apiKey.permissions.slice(0, 2).map((permission) => (
                      <Chip
                        key={permission}
                        label={permission}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    ))}
                    {apiKey.permissions.length > 2 && (
                      <Chip
                        label={`+${apiKey.permissions.length - 2}`}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {apiKey.userEmail}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography 
                      variant="body2" 
                      fontFamily="monospace"
                      sx={{ maxWidth: 200, overflow: 'hidden' }}
                    >
                      {state.visibleKeys.has(apiKey.id) ? apiKey.key : maskApiKey(apiKey.key)}
                    </Typography>
                    <Tooltip title={state.visibleKeys.has(apiKey.id) ? 'Hide' : 'Show'}>
                      <IconButton 
                        size="small" 
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                      >
                        {state.visibleKeys.has(apiKey.id) ? <VisibilityOff /> : <ViewIcon />}
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Copy to clipboard">
                      <IconButton 
                        size="small"
                        onClick={() => copyToClipboard(apiKey.key)}
                      >
                        <CopyIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    icon={apiKey.isActive ? <ActiveIcon /> : <InactiveIcon />}
                    label={apiKey.isActive ? 'Active' : 'Inactive'}
                    color={apiKey.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {apiKey.usageCount.toLocaleString()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      / {apiKey.rateLimit} limit
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {formatDate(apiKey.createdAt)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {apiKey.lastUsed ? formatDate(apiKey.lastUsed) : 'Never'}
                  </Typography>
                </TableCell>
                <TableCell align="right">
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="Edit API Key">
                      <IconButton size="small" onClick={() => handleEditKey(apiKey)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete API Key">
                      <IconButton 
                        size="small" 
                        color="error"
                        onClick={() => handleDeleteKey(apiKey)}
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Pagination */}
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
        <Pagination
          count={state.totalPages}
          page={state.page}
          onChange={(_, page) => setState(prev => ({ ...prev, page }))}
          color="primary"
        />
      </Box>

      {/* API Key Dialog */}
      <Dialog open={state.dialogOpen} onClose={() => setState(prev => ({ ...prev, dialogOpen: false }))} maxWidth="sm" fullWidth>
        <DialogTitle>
          {state.editingKey ? 'Edit API Key' : 'Create New API Key'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2 }}>
            <TextField
              fullWidth
              label="Name"
              defaultValue={state.editingKey?.name || ''}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Rate Limit (requests/hour)"
              type="number"
              defaultValue={state.editingKey?.rateLimit || 100}
              sx={{ mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch 
                  defaultChecked={state.editingKey?.isActive ?? true}
                />
              }
              label="Active"
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Permissions (comma-separated)"
              defaultValue={state.editingKey?.permissions.join(', ') || ''}
              helperText="e.g., video:create, video:read, audio:create"
              multiline
              rows={2}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setState(prev => ({ ...prev, dialogOpen: false }))}>
            Cancel
          </Button>
          <Button variant="contained">
            {state.editingKey ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={state.deleteDialogOpen} onClose={() => setState(prev => ({ ...prev, deleteDialogOpen: false }))}>
        <DialogTitle>Delete API Key</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the API key "{state.keyToDelete?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setState(prev => ({ ...prev, deleteDialogOpen: false }))}>
            Cancel
          </Button>
          <Button color="error" variant="contained" onClick={confirmDelete}>
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar */}
      <Snackbar
        open={state.snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setState(prev => ({ ...prev, snackbarOpen: false }))}
        message={state.snackbarMessage}
      />
    </Box>
  );
};

export default ApiKeys;