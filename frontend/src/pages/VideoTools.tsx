import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Switch,
  FormControlLabel,
  Slider
} from '@mui/material';
import {
  SmartToy as AIIcon,
  MergeType as ConcatenateIcon,
  Subtitles as CaptionsIcon,
  TextFields as TextOverlayIcon,
  ExpandMore as ExpandMoreIcon,
  Download as DownloadIcon,
  Transform as TransformIcon,
  AudioFile as AddAudioIcon,
  CallMerge as MergeIcon,
  PhotoLibrary as ThumbnailsIcon,
  ViewModule as FramesIcon
} from '@mui/icons-material';
import { directApi } from '../utils/api';
import VoiceSelector from '../components/settings/VoiceSelectorSettings';
import { VoiceInfo } from '../types/contentCreation';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`video-tools-tabpanel-${index}`}
      aria-labelledby={`video-tools-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}


// Enhanced image to video parameters
interface ImageToVideoParams extends Record<string, unknown> {
  image_url: string;
  video_length?: number;
  frame_rate?: number;
  zoom_speed?: number;
  narrator_speech_text?: string;
  voice?: string;
  tts_provider?: string;
  language?: string;
  narrator_audio_url?: string;
  narrator_vol?: number;
  background_music_url?: string;
  background_music_vol?: number;
  should_add_captions?: boolean;
  caption_properties?: {
    max_words_per_line?: number;
    font_size?: number;
    font_family?: string;
    color?: string;
    position?: string;
    stroke_color?: string;
    stroke_width?: number;
    box_color?: string;
    box_opacity?: number;
    margin_vertical?: number;
    margin_horizontal?: number;
    alignment?: string;
    line_spacing?: number;
    animation?: string;
  };
  zoom_direction?: string;
  zoom_start_scale?: number;
  zoom_end_scale?: number;
}

interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    final_video_url?: string;
    video_url?: string;
    video_with_audio_url?: string;
    output_url?: string;
    url?: string;
    clip_urls?: string[];
    clips?: Array<{ url: string; name?: string }>;
    audio_url?: string;
    srt_url?: string;
    thumbnail_url?: string;
    duration?: number;
    duration_seconds?: number;
    resolution?: string;
    processing_time?: number;
    processing_time_seconds?: number;
    file_size?: number;
    file_size_mb?: number;
    word_count?: number;
    segments_count?: number;
    [key: string]: unknown;
  };
  error?: string | null;
}

const VideoTools: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState<Record<string, boolean>>({});
  const [results, setResults] = useState<Record<string, JobResult | null>>({});
  const [errors, setErrors] = useState<Record<string, string | null>>({});
  const [jobStatuses, setJobStatuses] = useState<Record<string, string>>({});
  const [voices, setVoices] = useState<VoiceInfo[]>([]);

  // Enhanced image to video form state
  const [imageToVideoForm, setImageToVideoForm] = useState<ImageToVideoParams>({
    image_url: '',
    video_length: 10,
    frame_rate: 30,
    zoom_speed: 1.0,
    zoom_direction: 'zoom_in',
    zoom_start_scale: 1.0,
    zoom_end_scale: 1.2,
    narrator_speech_text: '',
    voice: 'af_heart',
    tts_provider: 'kokoro',
    language: 'en',
    narrator_vol: 80,
    background_music_url: '',
    background_music_vol: 20,
    should_add_captions: false,
    caption_properties: {
      max_words_per_line: 10,
      font_size: 48,
      font_family: 'Arial Bold',
      color: 'white',
      position: 'bottom',
      stroke_color: 'black',
      stroke_width: 2,
      box_color: 'rgba(0,0,0,0.7)',
      box_opacity: 0.7,
      margin_vertical: 50,
      margin_horizontal: 50,
      alignment: 'center',
      line_spacing: 1.2,
      animation: 'fade_in'
    }
  });

  const [aiClipsForm, setAiClipsForm] = useState({
    video_url: '',
    ai_query: '',
    max_clips: 5,
    output_format: 'mp4',
    quality: 'medium'
  });

  const [concatenateForm, setConcatenateForm] = useState({
    video_urls: ['', ''],
    output_format: 'mp4',
    transition: 'fade',
    transition_duration: 1.5
  });

  const [captionsForm, setCaptionsForm] = useState({
    video_url: '',
    captions: '',
    caption_properties: {
      style: 'classic',
      font_size: 48,
      line_color: 'white',
      word_color: 'yellow',
      outline_color: 'black',
      position: 'bottom_center',
      max_words_per_line: 10
    },
    language: 'auto'
  });

  const [textOverlayForm, setTextOverlayForm] = useState({
    video_url: '',
    text: '',
    options: {
      duration: 5,
      font_size: 48,
      font_color: 'white',
      box_color: 'black',
      box_opacity: 0.8,
      boxborderw: 60,
      position: 'bottom-center',
      y_offset: 50,
      line_spacing: 8,
      auto_wrap: true
    }
  });

  // Add Audio form state
  const [addAudioForm, setAddAudioForm] = useState({
    video_url: '',
    audio_url: '',
    video_volume: 100,
    audio_volume: 80,
    sync_mode: 'overlay',
    match_length: 'video',
    fade_in_duration: 0,
    fade_out_duration: 0
  });

  // Merge form state
  const [mergeForm, setMergeForm] = useState({
    video_urls: ['', ''],
    background_audio_url: '',
    output_format: 'mp4',
    transition: 'fade',
    transition_duration: 1.5,
    max_segment_duration: 0,
    total_duration_limit: 0,
    video_volume: 100,
    audio_volume: 20,
    sync_mode: 'overlay',
    fade_in_duration: 0,
    fade_out_duration: 0
  });

  // Thumbnails form state
  const [thumbnailsForm, setThumbnailsForm] = useState({
    video_url: '',
    timestamps: [],
    count: 5,
    format: 'jpg',
    quality: 85
  });

  // Frames form state
  const [framesForm, setFramesForm] = useState({
    video_url: '',
    interval: 1.0,
    format: 'jpg',
    quality: 85,
    max_frames: 100
  });

  // Fetch TTS data on component mount
  React.useEffect(() => {
    const fetchTTSData = async () => {
      try {
        const voicesRes = await directApi.get('/api/v1/audio/voices/all');

        if (voicesRes.data && voicesRes.data.voices) {
          // Transform provider->voices structure to flat array of VoiceInfo
          const flatVoices: VoiceInfo[] = [];
          
          Object.entries(voicesRes.data.voices).forEach(([provider, voiceList]) => {
            (voiceList as Array<{
              name?: string;
              id?: string;
              label?: string;
              gender?: string;
              language?: string;
              description?: string;
              grade?: string;
              engine?: string;
            }>).forEach((voice) => {
              flatVoices.push({
                name: voice.name || voice.id || voice.label || 'Unknown Voice',
                gender: voice.gender || 'unknown',
                language: voice.language || 'en',
                description: voice.description || '',
                grade: voice.grade || '',
                provider: provider as 'kokoro' | 'edge'
              });
            });
          });
          
          setVoices(flatVoices);
          
          // Set default voice if available
          const defaultVoice = flatVoices.find(v => v.provider === 'kokoro' && v.language === 'en');
          if (defaultVoice) {
            setImageToVideoForm(prev => ({
              ...prev,
              tts_provider: defaultVoice.provider,
              voice: defaultVoice.name,
              language: defaultVoice.language
            }));
          }
        }
      } catch (error) {
        console.error('Failed to fetch TTS data:', error);
      }
    };

    fetchTTSData();
  }, []);

  // Voice selector handlers
  const handleVoiceProviderChange = (provider: string) => {
    setImageToVideoForm(prev => ({ ...prev, tts_provider: provider }));
  };

  const handleVoiceNameChange = (name: string) => {
    setImageToVideoForm(prev => ({ ...prev, voice: name }));
  };

  const handleLanguageChange = (language: string) => {
    setImageToVideoForm(prev => ({ ...prev, language }));
  };

  // Generic job polling function
  const pollJobStatus = async (jobId: string, endpoint: string, toolName: string) => {
    const maxAttempts = 120; // 10 minutes max for video processing
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatuses(prev => ({ ...prev, [toolName]: `${status} (${attempts}/${maxAttempts})` }));

        if (status === 'completed') {
          setResults(prev => ({ ...prev, [toolName]: { job_id: jobId, result: jobResult, status: 'completed' } }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        } else if (status === 'failed') {
          setErrors(prev => ({ ...prev, [toolName]: jobError || 'Job failed' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Job polling timeout' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setErrors(prev => ({ ...prev, [toolName]: 'Failed to check job status' }));
          setLoading(prev => ({ ...prev, [toolName]: false }));
        }
      }
    };

    poll();
  };

  // Example images for the sidebar
  const exampleImages = [
    'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800',
    'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=800',
    'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
    'https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=800'
  ];

  const handleVideoGeneration = async () => {
    if (!imageToVideoForm.image_url.trim()) {
      setErrors(prev => ({ ...prev, videogen: 'Image URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, videogen: true }));
    setErrors(prev => ({ ...prev, videogen: null }));
    setResults(prev => ({ ...prev, videogen: null }));

    try {
      const response = await directApi.post('/api/v1/videos/generations', imageToVideoForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/generations', 'videogen');
      } else {
        setErrors(prev => ({ ...prev, videogen: 'Failed to create image-to-video job' }));
        setLoading(prev => ({ ...prev, videogen: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, videogen: errorMessage }));
      setLoading(prev => ({ ...prev, videogen: false }));
    }
  };

  const handleAIClips = async () => {
    if (!aiClipsForm.video_url.trim() || !aiClipsForm.ai_query.trim()) {
      setErrors(prev => ({ ...prev, aiclips: 'Video URL and AI query are required' }));
      return;
    }

    setLoading(prev => ({ ...prev, aiclips: true }));
    setErrors(prev => ({ ...prev, aiclips: null }));
    setResults(prev => ({ ...prev, aiclips: null }));

    try {
      const response = await directApi.post('/api/clips', aiClipsForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/clips', 'aiclips');
      } else {
        setErrors(prev => ({ ...prev, aiclips: 'Failed to create AI clips job' }));
        setLoading(prev => ({ ...prev, aiclips: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, aiclips: errorMessage }));
      setLoading(prev => ({ ...prev, aiclips: false }));
    }
  };

  const handleConcatenation = async () => {
    const validUrls = concatenateForm.video_urls.filter(url => url.trim());
    if (validUrls.length < 2) {
      setErrors(prev => ({ ...prev, concatenate: 'At least 2 video URLs are required' }));
      return;
    }

    setLoading(prev => ({ ...prev, concatenate: true }));
    setErrors(prev => ({ ...prev, concatenate: null }));
    setResults(prev => ({ ...prev, concatenate: null }));

    try {
      const response = await directApi.post('/api/v1/videos/concatenate', {
        ...concatenateForm,
        video_urls: validUrls
      });
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/concatenate', 'concatenate');
      } else {
        setErrors(prev => ({ ...prev, concatenate: 'Failed to create concatenation job' }));
        setLoading(prev => ({ ...prev, concatenate: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, concatenate: errorMessage }));
      setLoading(prev => ({ ...prev, concatenate: false }));
    }
  };

  const handleCaptions = async () => {
    if (!captionsForm.video_url.trim()) {
      setErrors(prev => ({ ...prev, captions: 'Video URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, captions: true }));
    setErrors(prev => ({ ...prev, captions: null }));
    setResults(prev => ({ ...prev, captions: null }));

    try {
      const response = await directApi.post('/api/v1/videos/add-captions', captionsForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/add-captions', 'captions');
      } else {
        setErrors(prev => ({ ...prev, captions: 'Failed to create captions job' }));
        setLoading(prev => ({ ...prev, captions: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, captions: errorMessage }));
      setLoading(prev => ({ ...prev, captions: false }));
    }
  };

  const handleTextOverlay = async () => {
    if (!textOverlayForm.video_url.trim() || !textOverlayForm.text.trim()) {
      setErrors(prev => ({ ...prev, textoverlay: 'Video URL and text are required' }));
      return;
    }

    setLoading(prev => ({ ...prev, textoverlay: true }));
    setErrors(prev => ({ ...prev, textoverlay: null }));
    setResults(prev => ({ ...prev, textoverlay: null }));

    try {
      const response = await directApi.post('/api/v1/videos/text-overlay', textOverlayForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/text-overlay', 'textoverlay');
      } else {
        setErrors(prev => ({ ...prev, textoverlay: 'Failed to create text overlay job' }));
        setLoading(prev => ({ ...prev, textoverlay: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, textoverlay: errorMessage }));
      setLoading(prev => ({ ...prev, textoverlay: false }));
    }
  };

  const handleAddAudio = async () => {
    if (!addAudioForm.video_url.trim() || !addAudioForm.audio_url.trim()) {
      setErrors(prev => ({ ...prev, addaudio: 'Video URL and Audio URL are required' }));
      return;
    }

    setLoading(prev => ({ ...prev, addaudio: true }));
    setErrors(prev => ({ ...prev, addaudio: null }));
    setResults(prev => ({ ...prev, addaudio: null }));

    try {
      const response = await directApi.post('/api/v1/videos/add-audio', addAudioForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/add-audio', 'addaudio');
      } else {
        setErrors(prev => ({ ...prev, addaudio: 'Failed to create add audio job' }));
        setLoading(prev => ({ ...prev, addaudio: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, addaudio: errorMessage }));
      setLoading(prev => ({ ...prev, addaudio: false }));
    }
  };

  const handleMerge = async () => {
    const validUrls = mergeForm.video_urls.filter(url => url.trim());
    if (validUrls.length < 2) {
      setErrors(prev => ({ ...prev, merge: 'At least 2 video URLs are required' }));
      return;
    }

    setLoading(prev => ({ ...prev, merge: true }));
    setErrors(prev => ({ ...prev, merge: null }));
    setResults(prev => ({ ...prev, merge: null }));

    try {
      const response = await directApi.post('/api/v1/videos/merge', {
        ...mergeForm,
        video_urls: validUrls
      });
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/merge', 'merge');
      } else {
        setErrors(prev => ({ ...prev, merge: 'Failed to create merge job' }));
        setLoading(prev => ({ ...prev, merge: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, merge: errorMessage }));
      setLoading(prev => ({ ...prev, merge: false }));
    }
  };

  const handleThumbnails = async () => {
    if (!thumbnailsForm.video_url.trim()) {
      setErrors(prev => ({ ...prev, thumbnails: 'Video URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, thumbnails: true }));
    setErrors(prev => ({ ...prev, thumbnails: null }));
    setResults(prev => ({ ...prev, thumbnails: null }));

    try {
      const response = await directApi.post('/api/v1/videos/thumbnails', thumbnailsForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/thumbnails', 'thumbnails');
      } else {
        setErrors(prev => ({ ...prev, thumbnails: 'Failed to create thumbnails job' }));
        setLoading(prev => ({ ...prev, thumbnails: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, thumbnails: errorMessage }));
      setLoading(prev => ({ ...prev, thumbnails: false }));
    }
  };

  const handleFrames = async () => {
    if (!framesForm.video_url.trim()) {
      setErrors(prev => ({ ...prev, frames: 'Video URL is required' }));
      return;
    }

    setLoading(prev => ({ ...prev, frames: true }));
    setErrors(prev => ({ ...prev, frames: null }));
    setResults(prev => ({ ...prev, frames: null }));

    try {
      const response = await directApi.post('/api/v1/videos/frames', framesForm);
      if (response.data && response.data.job_id) {
        pollJobStatus(response.data.job_id, '/api/v1/videos/frames', 'frames');
      } else {
        setErrors(prev => ({ ...prev, frames: 'Failed to create frames job' }));
        setLoading(prev => ({ ...prev, frames: false }));
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      setErrors(prev => ({ ...prev, frames: errorMessage }));
      setLoading(prev => ({ ...prev, frames: false }));
    }
  };

  const addVideoUrl = () => {
    setConcatenateForm(prev => ({
      ...prev,
      video_urls: [...prev.video_urls, '']
    }));
  };

  const removeVideoUrl = (index: number) => {
    setConcatenateForm(prev => ({
      ...prev,
      video_urls: prev.video_urls.filter((_, i) => i !== index)
    }));
  };

  const updateVideoUrl = (index: number, value: string) => {
    setConcatenateForm(prev => ({
      ...prev,
      video_urls: prev.video_urls.map((url, i) => i === index ? value : url)
    }));
  };

  const renderJobResult = (toolName: string, result: JobResult | null, icon: React.ReactNode) => {
    if (!result && !loading[toolName] && !errors[toolName]) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {icon}
            {toolName.charAt(0).toUpperCase() + toolName.slice(1)} Result
            {loading[toolName] && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading[toolName] && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatuses[toolName] || 'Processing...'}
              </Typography>
            </Box>
          )}

          {errors[toolName] && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors[toolName]}
            </Alert>
          )}

          {result && jobStatuses[toolName]?.includes('completed') && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 {toolName.charAt(0).toUpperCase() + toolName.slice(1)} completed successfully!
              </Alert>

              {/* Single Video Result */}
              {(() => {
                const videoUrl = result.result.final_video_url || result.result.video_url || result.result.output_url || result.result.url;
                return videoUrl ? (
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        Generated Video
                      </Typography>
                      <Button
                        startIcon={<DownloadIcon />}
                        href={videoUrl}
                        target="_blank"
                        variant="contained"
                        size="small"
                      >
                        Download
                      </Button>
                    </Box>
                    
                    <Paper sx={{ p: 2, bgcolor: '#f8fafc', textAlign: 'center' }}>
                      <video
                        src={videoUrl}
                        controls
                        style={{
                          width: '100%',
                          maxHeight: '400px',
                          borderRadius: '8px'
                        }}
                      />
                    </Paper>
                  </Box>
                ) : null;
              })()}

              {/* Multiple Clips Result */}
              {result.result.clip_urls && result.result.clip_urls.length > 0 && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    Generated Clips ({result.result.clip_urls.length})
                  </Typography>
                  <Grid container spacing={2}>
                    {result.result.clip_urls.map((clipUrl, index) => (
                      <Grid item xs={12} md={6} key={index}>
                        <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="subtitle2">
                              Clip {index + 1}
                            </Typography>
                            <Button
                              startIcon={<DownloadIcon />}
                              href={clipUrl}
                              target="_blank"
                              size="small"
                              variant="outlined"
                            >
                              Download
                            </Button>
                          </Box>
                          <video
                            src={clipUrl}
                            controls
                            style={{
                              width: '100%',
                              maxHeight: '200px',
                              borderRadius: '4px'
                            }}
                          />
                        </Paper>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              {/* Processing Details */}
              {(result.result.duration || result.result.resolution || result.result.processing_time || result.result.file_size) && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Video Details:</Typography>
                  <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                    <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                      {result.result.duration && (
                        <Grid item xs={12} md={3}>
                          <strong>Duration:</strong> {result.result.duration}s
                        </Grid>
                      )}
                      {result.result.resolution && (
                        <Grid item xs={12} md={3}>
                          <strong>Resolution:</strong> {result.result.resolution}
                        </Grid>
                      )}
                      {result.result.processing_time && (
                        <Grid item xs={12} md={3}>
                          <strong>Processing:</strong> {result.result.processing_time}s
                        </Grid>
                      )}
                      {result.result.file_size && (
                        <Grid item xs={12} md={3}>
                          <strong>Size:</strong> {(result.result.file_size / 1024 / 1024).toFixed(1)} MB
                        </Grid>
                      )}
                    </Grid>
                  </Paper>
                </Box>
              )}

              {/* SRT Download */}
              {result.result.srt_url && (
                <Box sx={{ mt: 2 }}>
                  <Button
                    startIcon={<DownloadIcon />}
                    href={result.result.srt_url}
                    target="_blank"
                    variant="outlined"
                    size="small"
                  >
                    Download SRT Subtitles
                  </Button>
                </Box>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 8
    }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Video Tools 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Create, edit, and enhance videos with AI-powered tools and professional effects.
        </Typography>
      </Box>

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flexGrow: 1 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={(_, newValue) => setTabValue(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            sx={{ px: 3 }}
          >
            <Tab icon={<TransformIcon />} label="Image to Video" />
            <Tab icon={<AIIcon />} label="AI Clips" />
            <Tab icon={<ConcatenateIcon />} label="Concatenate" />
            <Tab icon={<CaptionsIcon />} label="Captions" />
            <Tab icon={<TextOverlayIcon />} label="Text Overlay" />
            <Tab icon={<AddAudioIcon />} label="Add Audio" />
            <Tab icon={<MergeIcon />} label="Merge Videos" />
            <Tab icon={<ThumbnailsIcon />} label="Thumbnails" />
            <Tab icon={<FramesIcon />} label="Extract Frames" />
          </Tabs>
        </Box>

        <Box sx={{ p: 3 }}>
          {/* Image to Video Tab */}
          <TabPanel value={tabValue} index={0}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent sx={{ p: 3 }}>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TransformIcon color="primary" />
                      Image to Video Settings
                    </Typography>

                    <Grid container spacing={3}>
                      {/* Image URL */}
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Image URL"
                          placeholder="https://example.com/image.jpg"
                          value={imageToVideoForm.image_url}
                          onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, image_url: e.target.value })}
                          helperText="URL of the image to convert to video"
                        />
                      </Grid>

                      {/* Video Settings */}
                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Video Length: {imageToVideoForm.video_length}s</Typography>
                        <Slider
                          value={imageToVideoForm.video_length}
                          onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, video_length: Array.isArray(value) ? value[0] : value })}
                          min={5}
                          max={60}
                          step={1}
                          marks={[
                            { value: 5, label: '5s' },
                            { value: 15, label: '15s' },
                            { value: 30, label: '30s' },
                            { value: 60, label: '60s' }
                          ]}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          type="number"
                          label="Frame Rate (FPS)"
                          value={imageToVideoForm.frame_rate}
                          onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, frame_rate: parseInt(e.target.value) })}
                          inputProps={{ min: 15, max: 60 }}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Zoom Speed: {imageToVideoForm.zoom_speed}</Typography>
                        <Slider
                          value={imageToVideoForm.zoom_speed}
                          onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, zoom_speed: Array.isArray(value) ? value[0] : value })}
                          min={0.1}
                          max={5.0}
                          step={0.1}
                          marks={[
                            { value: 0.1, label: 'Very Slow' },
                            { value: 1.0, label: 'Normal' },
                            { value: 3.0, label: 'Fast' },
                            { value: 5.0, label: 'Very Fast' }
                          ]}
                        />
                      </Grid>

                      {/* Enhanced Zoom Controls */}
                      <Grid item xs={12}>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="h6">Advanced Zoom Effects</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid container spacing={2}>
                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth>
                                  <InputLabel>Zoom Direction</InputLabel>
                                  <Select
                                    value={imageToVideoForm.zoom_direction}
                                    label="Zoom Direction"
                                    onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, zoom_direction: e.target.value })}
                                  >
                                    <MenuItem value="zoom_in">Zoom In (Ken Burns)</MenuItem>
                                    <MenuItem value="zoom_out">Zoom Out</MenuItem>
                                    <MenuItem value="pan_left">Pan Left</MenuItem>
                                    <MenuItem value="pan_right">Pan Right</MenuItem>
                                    <MenuItem value="pan_up">Pan Up</MenuItem>
                                    <MenuItem value="pan_down">Pan Down</MenuItem>
                                    <MenuItem value="static">Static (No Movement)</MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <Typography gutterBottom>Start Scale: {imageToVideoForm.zoom_start_scale}</Typography>
                                <Slider
                                  value={imageToVideoForm.zoom_start_scale}
                                  onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, zoom_start_scale: Array.isArray(value) ? value[0] : value })}
                                  min={0.5}
                                  max={2.0}
                                  step={0.1}
                                  marks={[
                                    { value: 0.5, label: '0.5x' },
                                    { value: 1.0, label: '1x' },
                                    { value: 1.5, label: '1.5x' },
                                    { value: 2.0, label: '2x' }
                                  ]}
                                />
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <Typography gutterBottom>End Scale: {imageToVideoForm.zoom_end_scale}</Typography>
                                <Slider
                                  value={imageToVideoForm.zoom_end_scale}
                                  onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, zoom_end_scale: Array.isArray(value) ? value[0] : value })}
                                  min={0.5}
                                  max={2.0}
                                  step={0.1}
                                  marks={[
                                    { value: 0.5, label: '0.5x' },
                                    { value: 1.0, label: '1x' },
                                    { value: 1.5, label: '1.5x' },
                                    { value: 2.0, label: '2x' }
                                  ]}
                                />
                              </Grid>
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>

                      {/* Audio Settings Accordion */}
                      <Grid item xs={12}>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="h6">Audio Settings (Optional)</Typography>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid container spacing={2}>
                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  multiline
                                  rows={3}
                                  label="Narrator Text (Text-to-Speech)"
                                  placeholder="Enter text to convert to speech narration..."
                                  value={imageToVideoForm.narrator_speech_text}
                                  onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, narrator_speech_text: e.target.value })}
                                  helperText="Text will be converted to speech and added as narration"
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <VoiceSelector
                                  voiceProvider={imageToVideoForm.tts_provider || 'kokoro'}
                                  voiceName={imageToVideoForm.voice || 'af_heart'}
                                  language={imageToVideoForm.language || 'en'}
                                  voices={voices}
                                  onVoiceProviderChange={handleVoiceProviderChange}
                                  onVoiceNameChange={handleVoiceNameChange}
                                  onLanguageChange={handleLanguageChange}
                                />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <Typography gutterBottom>Narrator Volume: {imageToVideoForm.narrator_vol}%</Typography>
                                <Slider
                                  value={imageToVideoForm.narrator_vol}
                                  onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, narrator_vol: Array.isArray(value) ? value[0] : value })}
                                  min={0}
                                  max={100}
                                  step={5}
                                />
                              </Grid>

                              <Grid item xs={12}>
                                <TextField
                                  fullWidth
                                  label="Background Music URL (Optional)"
                                  placeholder="https://example.com/music.mp3 or YouTube URL"
                                  value={imageToVideoForm.background_music_url}
                                  onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, background_music_url: e.target.value })}
                                  helperText="URL to background music (supports YouTube URLs)"
                                />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <Typography gutterBottom>Background Music Volume: {imageToVideoForm.background_music_vol}%</Typography>
                                <Slider
                                  value={imageToVideoForm.background_music_vol}
                                  onChange={(_e, value) => setImageToVideoForm({ ...imageToVideoForm, background_music_vol: Array.isArray(value) ? value[0] : value })}
                                  min={0}
                                  max={100}
                                  step={5}
                                />
                              </Grid>
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>

                      {/* Caption Settings */}
                      <Grid item xs={12}>
                        <Accordion>
                          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                            <Typography variant="h6">Caption Settings</Typography>
                            <Box sx={{ ml: 2 }}>
                              <FormControlLabel
                                control={
                                  <Switch
                                    checked={imageToVideoForm.should_add_captions}
                                    onChange={(e) => setImageToVideoForm({ ...imageToVideoForm, should_add_captions: e.target.checked })}
                                  />
                                }
                                label="Enable Captions"
                                onClick={(e) => e.stopPropagation()}
                              />
                            </Box>
                          </AccordionSummary>
                          <AccordionDetails>
                            <Grid container spacing={2}>
                              {/* Font Settings */}
                              <Grid item xs={12} md={6}>
                                <TextField
                                  fullWidth
                                  type="number"
                                  label="Font Size"
                                  value={imageToVideoForm.caption_properties?.font_size || 48}
                                  onChange={(e) => setImageToVideoForm({ 
                                    ...imageToVideoForm, 
                                    caption_properties: { 
                                      ...imageToVideoForm.caption_properties, 
                                      font_size: parseInt(e.target.value) 
                                    } 
                                  })}
                                  inputProps={{ min: 12, max: 120 }}
                                  helperText="Font size in pixels"
                                />
                              </Grid>

                              <Grid item xs={12} md={6}>
                                <FormControl fullWidth>
                                  <InputLabel>Font Family</InputLabel>
                                  <Select
                                    value={imageToVideoForm.caption_properties?.font_family || 'Arial Bold'}
                                    label="Font Family"
                                    onChange={(e) => setImageToVideoForm({ 
                                      ...imageToVideoForm, 
                                      caption_properties: { 
                                        ...imageToVideoForm.caption_properties, 
                                        font_family: e.target.value 
                                      } 
                                    })}
                                  >
                                    <MenuItem value="Arial Bold">Arial Bold</MenuItem>
                                    <MenuItem value="Helvetica Bold">Helvetica Bold</MenuItem>
                                    <MenuItem value="Times New Roman Bold">Times Bold</MenuItem>
                                    <MenuItem value="Impact">Impact</MenuItem>
                                    <MenuItem value="Montserrat Bold">Montserrat Bold</MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth>
                                  <InputLabel>Position</InputLabel>
                                  <Select
                                    value={imageToVideoForm.caption_properties?.position || 'bottom'}
                                    label="Position"
                                    onChange={(e) => setImageToVideoForm({ 
                                      ...imageToVideoForm, 
                                      caption_properties: { 
                                        ...imageToVideoForm.caption_properties, 
                                        position: e.target.value 
                                      } 
                                    })}
                                  >
                                    <MenuItem value="top">Top</MenuItem>
                                    <MenuItem value="center">Center</MenuItem>
                                    <MenuItem value="bottom">Bottom</MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <FormControl fullWidth>
                                  <InputLabel>Animation</InputLabel>
                                  <Select
                                    value={imageToVideoForm.caption_properties?.animation || 'fade_in'}
                                    label="Animation"
                                    onChange={(e) => setImageToVideoForm({ 
                                      ...imageToVideoForm, 
                                      caption_properties: { 
                                        ...imageToVideoForm.caption_properties, 
                                        animation: e.target.value 
                                      } 
                                    })}
                                  >
                                    <MenuItem value="none">None</MenuItem>
                                    <MenuItem value="fade_in">Fade In</MenuItem>
                                    <MenuItem value="slide_up">Slide Up</MenuItem>
                                    <MenuItem value="typewriter">Typewriter</MenuItem>
                                  </Select>
                                </FormControl>
                              </Grid>

                              <Grid item xs={12} md={4}>
                                <TextField
                                  fullWidth
                                  type="number"
                                  label="Words Per Line"
                                  value={imageToVideoForm.caption_properties?.max_words_per_line || 10}
                                  onChange={(e) => setImageToVideoForm({ 
                                    ...imageToVideoForm, 
                                    caption_properties: { 
                                      ...imageToVideoForm.caption_properties, 
                                      max_words_per_line: parseInt(e.target.value) 
                                    } 
                                  })}
                                  inputProps={{ min: 1, max: 20 }}
                                  helperText="Max words per line"
                                />
                              </Grid>
                            </Grid>
                          </AccordionDetails>
                        </Accordion>
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.videogen ? <CircularProgress size={20} /> : <TransformIcon />}
                      onClick={handleVideoGeneration}
                      disabled={loading.videogen || !imageToVideoForm.image_url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.videogen ? 'Processing...' : 'Create Video'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Example Images
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Click any image to use as your source.
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      {exampleImages.map((url, index) => (
                        <Box key={index}>
                          <img
                            src={url}
                            alt={`Example ${index + 1}`}
                            style={{
                              width: '100%',
                              height: '80px',
                              objectFit: 'cover',
                              borderRadius: '8px',
                              cursor: 'pointer',
                              border: imageToVideoForm.image_url === url ? '2px solid #1976d2' : '1px solid #e0e0e0'
                            }}
                            onClick={() => setImageToVideoForm({ ...imageToVideoForm, image_url: url })}
                          />
                          <Button
                            variant="outlined"
                            size="small"
                            onClick={() => setImageToVideoForm({ ...imageToVideoForm, image_url: url })}
                            sx={{ mt: 1, width: '100%' }}
                          >
                            Use This Image
                          </Button>
                        </Box>
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('videogen', results.videogen, <TransformIcon />)}
          </TabPanel>

          {/* AI Clips Tab */}
          <TabPanel value={tabValue} index={1}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AIIcon color="primary" />
                      AI-Powered Video Clips
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Video URL"
                          placeholder="https://example.com/video.mp4"
                          value={aiClipsForm.video_url}
                          onChange={(e) => setAiClipsForm({ ...aiClipsForm, video_url: e.target.value })}
                          helperText="URL of the video to extract clips from"
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="AI Query"
                          placeholder="Find clips discussing machine learning and AI techniques..."
                          value={aiClipsForm.ai_query}
                          onChange={(e) => setAiClipsForm({ ...aiClipsForm, ai_query: e.target.value })}
                          helperText="Describe what kind of clips you want to extract"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Max Clips: {aiClipsForm.max_clips}</Typography>
                        <Slider
                          value={aiClipsForm.max_clips}
                          onChange={(_e, value) => setAiClipsForm({ ...aiClipsForm, max_clips: Array.isArray(value) ? value[0] : value })}
                          min={1}
                          max={20}
                          step={1}
                          marks={[
                            { value: 1, label: '1' },
                            { value: 5, label: '5' },
                            { value: 10, label: '10' },
                            { value: 20, label: '20' }
                          ]}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Output Format</InputLabel>
                          <Select
                            value={aiClipsForm.output_format}
                            label="Output Format"
                            onChange={(e) => setAiClipsForm({ ...aiClipsForm, output_format: e.target.value })}
                          >
                            <MenuItem value="mp4">MP4</MenuItem>
                            <MenuItem value="webm">WebM</MenuItem>
                            <MenuItem value="avi">AVI</MenuItem>
                            <MenuItem value="mov">MOV</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Quality</InputLabel>
                          <Select
                            value={aiClipsForm.quality}
                            label="Quality"
                            onChange={(e) => setAiClipsForm({ ...aiClipsForm, quality: e.target.value })}
                          >
                            <MenuItem value="low">Low</MenuItem>
                            <MenuItem value="medium">Medium</MenuItem>
                            <MenuItem value="high">High</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.aiclips ? <CircularProgress size={20} /> : <AIIcon />}
                      onClick={handleAIClips}
                      disabled={loading.aiclips || !aiClipsForm.video_url.trim() || !aiClipsForm.ai_query.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.aiclips ? 'Processing...' : 'Extract AI Clips'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      AI Query Examples
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip 
                        label="Find funny moments" 
                        variant="outlined" 
                        size="small"
                        onClick={() => setAiClipsForm({ ...aiClipsForm, ai_query: "Find funny moments and jokes" })}
                        sx={{ cursor: 'pointer' }}
                      />
                      <Chip 
                        label="Extract key points" 
                        variant="outlined" 
                        size="small"
                        onClick={() => setAiClipsForm({ ...aiClipsForm, ai_query: "Find segments explaining key concepts" })}
                        sx={{ cursor: 'pointer' }}
                      />
                      <Chip 
                        label="Technical discussions" 
                        variant="outlined" 
                        size="small"
                        onClick={() => setAiClipsForm({ ...aiClipsForm, ai_query: "Extract technical discussions and programming topics" })}
                        sx={{ cursor: 'pointer' }}
                      />
                      <Chip 
                        label="Q&A segments" 
                        variant="outlined" 
                        size="small"
                        onClick={() => setAiClipsForm({ ...aiClipsForm, ai_query: "Find question and answer sessions" })}
                        sx={{ cursor: 'pointer' }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('aiclips', results.aiclips, <AIIcon />)}
          </TabPanel>

          {/* Concatenate Tab */}
          <TabPanel value={tabValue} index={2}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ConcatenateIcon color="primary" />
                  Concatenate Videos
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>
                      Video URLs (in order)
                    </Typography>
                    {concatenateForm.video_urls.map((url, index) => (
                      <Box key={index} sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <TextField
                          fullWidth
                          label={`Video ${index + 1} URL`}
                          placeholder="https://example.com/video.mp4"
                          value={url}
                          onChange={(e) => updateVideoUrl(index, e.target.value)}
                        />
                        {concatenateForm.video_urls.length > 2 && (
                          <Button 
                            variant="outlined" 
                            color="error"
                            onClick={() => removeVideoUrl(index)}
                            sx={{ minWidth: 'auto', px: 2 }}
                          >
                            ✕
                          </Button>
                        )}
                      </Box>
                    ))}
                    <Button 
                      variant="outlined" 
                      onClick={addVideoUrl}
                      sx={{ mt: 1 }}
                    >
                      + Add Video URL
                    </Button>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Transition</InputLabel>
                      <Select
                        value={concatenateForm.transition}
                        label="Transition"
                        onChange={(e) => setConcatenateForm({ ...concatenateForm, transition: e.target.value })}
                      >
                        <MenuItem value="none">None (Instant Cut)</MenuItem>
                        <MenuItem value="fade">Fade</MenuItem>
                        <MenuItem value="dissolve">Dissolve</MenuItem>
                        <MenuItem value="slide">Slide</MenuItem>
                        <MenuItem value="wipe">Wipe</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Transition Duration: {concatenateForm.transition_duration}s</Typography>
                    <Slider
                      value={concatenateForm.transition_duration}
                      onChange={(_e, value) => setConcatenateForm({ ...concatenateForm, transition_duration: Array.isArray(value) ? value[0] : value })}
                      min={0.1}
                      max={5.0}
                      step={0.1}
                      disabled={concatenateForm.transition === 'none'}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Output Format</InputLabel>
                      <Select
                        value={concatenateForm.output_format}
                        label="Output Format"
                        onChange={(e) => setConcatenateForm({ ...concatenateForm, output_format: e.target.value })}
                      >
                        <MenuItem value="mp4">MP4</MenuItem>
                        <MenuItem value="webm">WebM</MenuItem>
                        <MenuItem value="avi">AVI</MenuItem>
                        <MenuItem value="mov">MOV</MenuItem>
                        <MenuItem value="mkv">MKV</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.concatenate ? <CircularProgress size={20} /> : <ConcatenateIcon />}
                  onClick={handleConcatenation}
                  disabled={loading.concatenate || concatenateForm.video_urls.filter(url => url.trim()).length < 2}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.concatenate ? 'Concatenating...' : 'Concatenate Videos'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('concatenate', results.concatenate, <ConcatenateIcon />)}
          </TabPanel>

          {/* Captions Tab */}
          <TabPanel value={tabValue} index={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <CaptionsIcon color="primary" />
                      Add Video Captions
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Video URL"
                          placeholder="https://example.com/video.mp4"
                          value={captionsForm.video_url}
                          onChange={(e) => setCaptionsForm({ ...captionsForm, video_url: e.target.value })}
                          helperText="URL of the video to add captions to"
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Caption Text (Optional)"
                          placeholder="Enter custom caption text or leave empty for auto-transcription..."
                          value={captionsForm.captions}
                          onChange={(e) => setCaptionsForm({ ...captionsForm, captions: e.target.value })}
                          helperText="Leave empty for automatic speech-to-text transcription"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Caption Style</InputLabel>
                          <Select
                            value={captionsForm.caption_properties.style}
                            label="Caption Style"
                            onChange={(e) => setCaptionsForm({ 
                              ...captionsForm, 
                              caption_properties: { 
                                ...captionsForm.caption_properties, 
                                style: e.target.value 
                              } 
                            })}
                          >
                            <MenuItem value="classic">Classic</MenuItem>
                            <MenuItem value="karaoke">Karaoke</MenuItem>
                            <MenuItem value="highlight">Highlight</MenuItem>
                            <MenuItem value="viral_bounce">Viral Bounce</MenuItem>
                            <MenuItem value="viral_cyan">Viral Cyan</MenuItem>
                            <MenuItem value="modern_neon">Modern Neon</MenuItem>
                            <MenuItem value="typewriter">Typewriter</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Position</InputLabel>
                          <Select
                            value={captionsForm.caption_properties.position}
                            label="Position"
                            onChange={(e) => setCaptionsForm({ 
                              ...captionsForm, 
                              caption_properties: { 
                                ...captionsForm.caption_properties, 
                                position: e.target.value 
                              } 
                            })}
                          >
                            <MenuItem value="top_center">Top Center</MenuItem>
                            <MenuItem value="middle_center">Middle Center</MenuItem>
                            <MenuItem value="bottom_center">Bottom Center</MenuItem>
                            <MenuItem value="bottom_left">Bottom Left</MenuItem>
                            <MenuItem value="bottom_right">Bottom Right</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Font Size: {captionsForm.caption_properties.font_size}px</Typography>
                        <Slider
                          value={captionsForm.caption_properties.font_size}
                          onChange={(_e, value) => setCaptionsForm({ 
                            ...captionsForm, 
                            caption_properties: { 
                              ...captionsForm.caption_properties, 
                              font_size: Array.isArray(value) ? value[0] : value 
                            } 
                          })}
                          min={20}
                          max={120}
                          step={4}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Text Color"
                          value={captionsForm.caption_properties.line_color}
                          onChange={(e) => setCaptionsForm({ 
                            ...captionsForm, 
                            caption_properties: { 
                              ...captionsForm.caption_properties, 
                              line_color: e.target.value 
                            } 
                          })}
                          placeholder="white, #FFFFFF"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Highlight Color"
                          value={captionsForm.caption_properties.word_color}
                          onChange={(e) => setCaptionsForm({ 
                            ...captionsForm, 
                            caption_properties: { 
                              ...captionsForm.caption_properties, 
                              word_color: e.target.value 
                            } 
                          })}
                          placeholder="yellow, #FFFF00"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.captions ? <CircularProgress size={20} /> : <CaptionsIcon />}
                      onClick={handleCaptions}
                      disabled={loading.captions || !captionsForm.video_url.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.captions ? 'Adding Captions...' : 'Add Captions'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Caption Styles
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Classic Subtitles" variant="outlined" size="small" />
                      <Chip label="TikTok Viral Bounce" variant="outlined" size="small" />
                      <Chip label="Karaoke Highlighting" variant="outlined" size="small" />
                      <Chip label="Modern Neon Glow" variant="outlined" size="small" />
                      <Chip label="Auto Transcription" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('captions', results.captions, <CaptionsIcon />)}
          </TabPanel>

          {/* Text Overlay Tab */}
          <TabPanel value={tabValue} index={4}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TextOverlayIcon color="primary" />
                      Add Text Overlay
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Video URL"
                          placeholder="https://example.com/video.mp4"
                          value={textOverlayForm.video_url}
                          onChange={(e) => setTextOverlayForm({ ...textOverlayForm, video_url: e.target.value })}
                          helperText="URL of the video to add text overlay to"
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Overlay Text"
                          placeholder="Enter text to overlay on the video..."
                          value={textOverlayForm.text}
                          onChange={(e) => setTextOverlayForm({ ...textOverlayForm, text: e.target.value })}
                          helperText="Text content to display on the video"
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Duration: {textOverlayForm.options.duration}s</Typography>
                        <Slider
                          value={textOverlayForm.options.duration}
                          onChange={(_e, value) => setTextOverlayForm({ 
                            ...textOverlayForm, 
                            options: { 
                              ...textOverlayForm.options, 
                              duration: Array.isArray(value) ? value[0] : value 
                            } 
                          })}
                          min={1}
                          max={60}
                          step={1}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <Typography gutterBottom>Font Size: {textOverlayForm.options.font_size}px</Typography>
                        <Slider
                          value={textOverlayForm.options.font_size}
                          onChange={(_e, value) => setTextOverlayForm({ 
                            ...textOverlayForm, 
                            options: { 
                              ...textOverlayForm.options, 
                              font_size: Array.isArray(value) ? value[0] : value 
                            } 
                          })}
                          min={12}
                          max={200}
                          step={4}
                        />
                      </Grid>

                      <Grid item xs={12} md={4}>
                        <FormControl fullWidth>
                          <InputLabel>Position</InputLabel>
                          <Select
                            value={textOverlayForm.options.position}
                            label="Position"
                            onChange={(e) => setTextOverlayForm({ 
                              ...textOverlayForm, 
                              options: { 
                                ...textOverlayForm.options, 
                                position: e.target.value 
                              } 
                            })}
                          >
                            <MenuItem value="top-left">Top Left</MenuItem>
                            <MenuItem value="top-center">Top Center</MenuItem>
                            <MenuItem value="top-right">Top Right</MenuItem>
                            <MenuItem value="center-left">Center Left</MenuItem>
                            <MenuItem value="center">Center</MenuItem>
                            <MenuItem value="center-right">Center Right</MenuItem>
                            <MenuItem value="bottom-left">Bottom Left</MenuItem>
                            <MenuItem value="bottom-center">Bottom Center</MenuItem>
                            <MenuItem value="bottom-right">Bottom Right</MenuItem>
                          </Select>
                        </FormControl>
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Font Color"
                          value={textOverlayForm.options.font_color}
                          onChange={(e) => setTextOverlayForm({ 
                            ...textOverlayForm, 
                            options: { 
                              ...textOverlayForm.options, 
                              font_color: e.target.value 
                            } 
                          })}
                          placeholder="white, #FFFFFF"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <TextField
                          fullWidth
                          label="Background Color"
                          value={textOverlayForm.options.box_color}
                          onChange={(e) => setTextOverlayForm({ 
                            ...textOverlayForm, 
                            options: { 
                              ...textOverlayForm.options, 
                              box_color: e.target.value 
                            } 
                          })}
                          placeholder="black, #000000"
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <Typography gutterBottom>Background Opacity: {Math.round(textOverlayForm.options.box_opacity * 100)}%</Typography>
                        <Slider
                          value={textOverlayForm.options.box_opacity}
                          onChange={(_e, value) => setTextOverlayForm({ 
                            ...textOverlayForm, 
                            options: { 
                              ...textOverlayForm.options, 
                              box_opacity: Array.isArray(value) ? value[0] : value 
                            } 
                          })}
                          min={0}
                          max={1}
                          step={0.1}
                        />
                      </Grid>

                      <Grid item xs={12} md={6}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={textOverlayForm.options.auto_wrap}
                              onChange={(e) => setTextOverlayForm({ 
                                ...textOverlayForm, 
                                options: { 
                                  ...textOverlayForm.options, 
                                  auto_wrap: e.target.checked 
                                } 
                              })}
                            />
                          }
                          label="Auto-wrap Text"
                        />
                      </Grid>
                    </Grid>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={loading.textoverlay ? <CircularProgress size={20} /> : <TextOverlayIcon />}
                      onClick={handleTextOverlay}
                      disabled={loading.textoverlay || !textOverlayForm.video_url.trim() || !textOverlayForm.text.trim()}
                      sx={{ mt: 3, px: 4 }}
                    >
                      {loading.textoverlay ? 'Adding Overlay...' : 'Add Text Overlay'}
                    </Button>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={4}>
                <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
                  <CardContent>
                    <Typography variant="h6" sx={{ mb: 2 }}>
                      Use Cases
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Chip label="Video Titles" variant="outlined" size="small" />
                      <Chip label="Watermarks" variant="outlined" size="small" />
                      <Chip label="Call-to-Actions" variant="outlined" size="small" />
                      <Chip label="Credits & Attribution" variant="outlined" size="small" />
                      <Chip label="Custom Messaging" variant="outlined" size="small" />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {renderJobResult('textoverlay', results.textoverlay, <TextOverlayIcon />)}
          </TabPanel>

          {/* Add Audio Tab */}
          <TabPanel value={tabValue} index={5}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <AddAudioIcon color="primary" />
                  Add Audio to Video
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Video URL"
                      placeholder="https://example.com/video.mp4"
                      value={addAudioForm.video_url}
                      onChange={(e) => setAddAudioForm({ ...addAudioForm, video_url: e.target.value })}
                      helperText="URL of the video to add audio to"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Audio URL"
                      placeholder="https://example.com/audio.mp3"
                      value={addAudioForm.audio_url}
                      onChange={(e) => setAddAudioForm({ ...addAudioForm, audio_url: e.target.value })}
                      helperText="URL of the audio file to add"
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Sync Mode</InputLabel>
                      <Select
                        value={addAudioForm.sync_mode}
                        label="Sync Mode"
                        onChange={(e) => setAddAudioForm({ ...addAudioForm, sync_mode: e.target.value })}
                      >
                        <MenuItem value="replace">Replace Original Audio</MenuItem>
                        <MenuItem value="mix">Mix with Original</MenuItem>
                        <MenuItem value="overlay">Overlay on Original</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Match Length</InputLabel>
                      <Select
                        value={addAudioForm.match_length}
                        label="Match Length"
                        onChange={(e) => setAddAudioForm({ ...addAudioForm, match_length: e.target.value })}
                      >
                        <MenuItem value="video">Match Video Length</MenuItem>
                        <MenuItem value="audio">Match Audio Length</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Video Volume: {addAudioForm.video_volume}%</Typography>
                    <Slider
                      value={addAudioForm.video_volume}
                      onChange={(_e, value) => setAddAudioForm({ ...addAudioForm, video_volume: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={100}
                      step={5}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Audio Volume: {addAudioForm.audio_volume}%</Typography>
                    <Slider
                      value={addAudioForm.audio_volume}
                      onChange={(_e, value) => setAddAudioForm({ ...addAudioForm, audio_volume: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={100}
                      step={5}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Fade In: {addAudioForm.fade_in_duration}s</Typography>
                    <Slider
                      value={addAudioForm.fade_in_duration}
                      onChange={(_e, value) => setAddAudioForm({ ...addAudioForm, fade_in_duration: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={10}
                      step={0.5}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Fade Out: {addAudioForm.fade_out_duration}s</Typography>
                    <Slider
                      value={addAudioForm.fade_out_duration}
                      onChange={(_e, value) => setAddAudioForm({ ...addAudioForm, fade_out_duration: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={10}
                      step={0.5}
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.addaudio ? <CircularProgress size={20} /> : <AddAudioIcon />}
                  onClick={handleAddAudio}
                  disabled={loading.addaudio || !addAudioForm.video_url.trim() || !addAudioForm.audio_url.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.addaudio ? 'Adding Audio...' : 'Add Audio'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('addaudio', results.addaudio, <AddAudioIcon />)}
          </TabPanel>

          {/* Merge Videos Tab */}
          <TabPanel value={tabValue} index={6}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <MergeIcon color="primary" />
                  Merge Multiple Videos
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Typography variant="subtitle1" sx={{ mb: 2 }}>
                      Video URLs (in order)
                    </Typography>
                    {mergeForm.video_urls.map((url, index) => (
                      <Box key={index} sx={{ display: 'flex', gap: 1, mb: 2 }}>
                        <TextField
                          fullWidth
                          label={`Video ${index + 1} URL`}
                          placeholder="https://example.com/video.mp4"
                          value={url}
                          onChange={(e) => {
                            const newUrls = [...mergeForm.video_urls];
                            newUrls[index] = e.target.value;
                            setMergeForm({ ...mergeForm, video_urls: newUrls });
                          }}
                        />
                        {mergeForm.video_urls.length > 2 && (
                          <Button
                            variant="outlined"
                            color="error"
                            onClick={() => {
                              const newUrls = mergeForm.video_urls.filter((_, i) => i !== index);
                              setMergeForm({ ...mergeForm, video_urls: newUrls });
                            }}
                            sx={{ minWidth: 'auto', px: 2 }}
                          >
                            ✕
                          </Button>
                        )}
                      </Box>
                    ))}
                    <Button
                      variant="outlined"
                      onClick={() => setMergeForm({ ...mergeForm, video_urls: [...mergeForm.video_urls, ''] })}
                      sx={{ mt: 1 }}
                    >
                      + Add Video URL
                    </Button>
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Background Audio URL (Optional)"
                      placeholder="https://example.com/music.mp3"
                      value={mergeForm.background_audio_url}
                      onChange={(e) => setMergeForm({ ...mergeForm, background_audio_url: e.target.value })}
                      helperText="Optional background music for the merged video"
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Transition</InputLabel>
                      <Select
                        value={mergeForm.transition}
                        label="Transition"
                        onChange={(e) => setMergeForm({ ...mergeForm, transition: e.target.value })}
                      >
                        <MenuItem value="none">None (Instant Cut)</MenuItem>
                        <MenuItem value="fade">Fade</MenuItem>
                        <MenuItem value="dissolve">Dissolve</MenuItem>
                        <MenuItem value="slide">Slide</MenuItem>
                        <MenuItem value="wipe">Wipe</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Transition Duration: {mergeForm.transition_duration}s</Typography>
                    <Slider
                      value={mergeForm.transition_duration}
                      onChange={(_e, value) => setMergeForm({ ...mergeForm, transition_duration: Array.isArray(value) ? value[0] : value })}
                      min={0.1}
                      max={5.0}
                      step={0.1}
                      disabled={mergeForm.transition === 'none'}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Output Format</InputLabel>
                      <Select
                        value={mergeForm.output_format}
                        label="Output Format"
                        onChange={(e) => setMergeForm({ ...mergeForm, output_format: e.target.value })}
                      >
                        <MenuItem value="mp4">MP4</MenuItem>
                        <MenuItem value="webm">WebM</MenuItem>
                        <MenuItem value="avi">AVI</MenuItem>
                        <MenuItem value="mov">MOV</MenuItem>
                        <MenuItem value="mkv">MKV</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Video Volume: {mergeForm.video_volume}%</Typography>
                    <Slider
                      value={mergeForm.video_volume}
                      onChange={(_e, value) => setMergeForm({ ...mergeForm, video_volume: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={100}
                      step={5}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Audio Volume: {mergeForm.audio_volume}%</Typography>
                    <Slider
                      value={mergeForm.audio_volume}
                      onChange={(_e, value) => setMergeForm({ ...mergeForm, audio_volume: Array.isArray(value) ? value[0] : value })}
                      min={0}
                      max={100}
                      step={5}
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.merge ? <CircularProgress size={20} /> : <MergeIcon />}
                  onClick={handleMerge}
                  disabled={loading.merge || mergeForm.video_urls.filter(url => url.trim()).length < 2}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.merge ? 'Merging Videos...' : 'Merge Videos'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('merge', results.merge, <MergeIcon />)}
          </TabPanel>

          {/* Thumbnails Tab */}
          <TabPanel value={tabValue} index={7}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ThumbnailsIcon color="primary" />
                  Generate Video Thumbnails
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Video URL"
                      placeholder="https://example.com/video.mp4"
                      value={thumbnailsForm.video_url}
                      onChange={(e) => setThumbnailsForm({ ...thumbnailsForm, video_url: e.target.value })}
                      helperText="URL of the video to generate thumbnails from"
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Number of Thumbnails: {thumbnailsForm.count}</Typography>
                    <Slider
                      value={thumbnailsForm.count}
                      onChange={(_e, value) => setThumbnailsForm({ ...thumbnailsForm, count: Array.isArray(value) ? value[0] : value })}
                      min={1}
                      max={20}
                      step={1}
                      marks={[
                        { value: 1, label: '1' },
                        { value: 5, label: '5' },
                        { value: 10, label: '10' },
                        { value: 20, label: '20' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Format</InputLabel>
                      <Select
                        value={thumbnailsForm.format}
                        label="Format"
                        onChange={(e) => setThumbnailsForm({ ...thumbnailsForm, format: e.target.value })}
                      >
                        <MenuItem value="jpg">JPG</MenuItem>
                        <MenuItem value="png">PNG</MenuItem>
                        <MenuItem value="webp">WebP</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Quality: {thumbnailsForm.quality}%</Typography>
                    <Slider
                      value={thumbnailsForm.quality}
                      onChange={(_e, value) => setThumbnailsForm({ ...thumbnailsForm, quality: Array.isArray(value) ? value[0] : value })}
                      min={10}
                      max={100}
                      step={5}
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.thumbnails ? <CircularProgress size={20} /> : <ThumbnailsIcon />}
                  onClick={handleThumbnails}
                  disabled={loading.thumbnails || !thumbnailsForm.video_url.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.thumbnails ? 'Generating...' : 'Generate Thumbnails'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('thumbnails', results.thumbnails, <ThumbnailsIcon />)}
          </TabPanel>

          {/* Extract Frames Tab */}
          <TabPanel value={tabValue} index={8}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <FramesIcon color="primary" />
                  Extract Video Frames
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Video URL"
                      placeholder="https://example.com/video.mp4"
                      value={framesForm.video_url}
                      onChange={(e) => setFramesForm({ ...framesForm, video_url: e.target.value })}
                      helperText="URL of the video to extract frames from"
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Interval: {framesForm.interval}s</Typography>
                    <Slider
                      value={framesForm.interval}
                      onChange={(_e, value) => setFramesForm({ ...framesForm, interval: Array.isArray(value) ? value[0] : value })}
                      min={0.1}
                      max={10.0}
                      step={0.1}
                      marks={[
                        { value: 0.1, label: '0.1s' },
                        { value: 1.0, label: '1s' },
                        { value: 5.0, label: '5s' },
                        { value: 10.0, label: '10s' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Format</InputLabel>
                      <Select
                        value={framesForm.format}
                        label="Format"
                        onChange={(e) => setFramesForm({ ...framesForm, format: e.target.value })}
                      >
                        <MenuItem value="jpg">JPG</MenuItem>
                        <MenuItem value="png">PNG</MenuItem>
                        <MenuItem value="webp">WebP</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={4}>
                    <Typography gutterBottom>Quality: {framesForm.quality}%</Typography>
                    <Slider
                      value={framesForm.quality}
                      onChange={(_e, value) => setFramesForm({ ...framesForm, quality: Array.isArray(value) ? value[0] : value })}
                      min={10}
                      max={100}
                      step={5}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Typography gutterBottom>Max Frames: {framesForm.max_frames || 'Unlimited'}</Typography>
                    <Slider
                      value={framesForm.max_frames}
                      onChange={(_e, value) => setFramesForm({ ...framesForm, max_frames: Array.isArray(value) ? value[0] : value })}
                      min={10}
                      max={1000}
                      step={10}
                      marks={[
                        { value: 10, label: '10' },
                        { value: 100, label: '100' },
                        { value: 500, label: '500' },
                        { value: 1000, label: '1000' }
                      ]}
                    />
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading.frames ? <CircularProgress size={20} /> : <FramesIcon />}
                  onClick={handleFrames}
                  disabled={loading.frames || !framesForm.video_url.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading.frames ? 'Extracting...' : 'Extract Frames'}
                </Button>
              </CardContent>
            </Card>

            {renderJobResult('frames', results.frames, <FramesIcon />)}
          </TabPanel>
        </Box>
      </Paper>
    </Box>
  );
};

export default VideoTools;