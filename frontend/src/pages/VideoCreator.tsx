import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  RadioGroup,
  FormControlLabel,
  Radio,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Slider,
  Paper,
  LinearProgress,
  Card,
  CardContent,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  VideoLibrary as StockIcon,
  AutoAwesome as AIIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
  CloudDownload as DownloadIcon,
  CheckCircle as SuccessIcon,
  VideoLibrary as LibraryIcon,
} from '@mui/icons-material';

import { directApi } from '../utils/api';
import { FootageToVideoRequest, AiimageToVideoRequest, LanguageCode } from '../types/ouinhi';
import AdvancedVideoSettings from '../components/AdvancedVideoSettings';

// Voice interfaces matching the actual API response
interface VoiceInfo {
  name: string;
  gender: string;
  language: string;
  description?: string;
  grade?: string;
  engine?: string;
}

interface VoicesData {
  voices: Record<string, VoiceInfo[]>; // provider -> voice[]
}

interface ProvidersData {
  providers: string[];
  default_provider?: string;
}

// Video creation result interfaces
interface GeneratedImage {
  url: string;
  prompt?: string;
}

interface VideoCreationResult {
  final_video_url?: string;
  video_with_audio_url?: string;
  video_duration?: number;
  processing_time?: number;
  word_count?: number;
  segments_count?: number;
  audio_url?: string;
  srt_url?: string;
  background_music_url?: string;
  script_generated?: string;
  generated_images?: GeneratedImage[];
}

// Import constants from the constants file
import {
  VIDEO_ORIENTATIONS
} from '../constants/videoSettings';

// Local constants specific to VideoCreator
const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'es', name: 'Spanish' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'it', name: 'Italian' },
  { code: 'pt', name: 'Portuguese' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'zh', name: 'Chinese' },
] as const;

const SCRIPT_TYPES = [
  'facts',
  'story',
  'educational',
  'motivation',
  'conspiracy',
  'life_hacks',
  'shower_thoughts',
  'prayer',
  'pov',
  'would_you_rather',
  'before_you_die',
  'dark_psychology',
  'reddit_stories',
  'daily_news',
] as const;

type VideoMethod = 'stock' | 'ai_images';

interface FormData {
  script: string;
  method: VideoMethod;
  language: LanguageCode;
  script_type: string;
  max_duration: number;
  tts_provider: string;
  voice: string;
  tts_speed: number;
  video_orientation: string;
  segment_duration: number;
  add_captions: boolean;
  caption_style: string;
  caption_color: string;
  background_music: string;
  background_music_volume: number;
  generate_background_music: boolean;
  output_width: number;
  output_height: number;
  frame_rate: number;
  video_effect: string;
  image_width: number;
  image_height: number;
  inference_steps: number;
  music_duration: number;
  image_provider: string;
  guidance_scale: number;
  caption_position?: string;
}

// Helper function to estimate script duration
const estimateScriptDuration = (script: string): number => {
  if (!script.trim()) return 0;
  
  // Average speaking rate: ~2.5 words per second (150 words per minute)
  const words = script.trim().split(/\s+/).length;
  return Math.ceil(words / 2.5);
};

const VideoCreator: React.FC = () => {
  const navigate = useNavigate();
  
  // Form state with all parameters
  const [formData, setFormData] = useState<FormData>({
    script: '',
    method: 'stock',
    language: 'en',
    script_type: 'facts',
    max_duration: 120, // Increased from 60 to 120 seconds
    tts_provider: 'kokoro',
    voice: 'af_bella',
    tts_speed: 1.0,
    video_orientation: 'portrait',
    segment_duration: 3.0,
    add_captions: true,
    caption_style: 'viral_bounce',
    caption_color: '#00FFFF',
    background_music: 'chill',
    background_music_volume: 0.3,
    generate_background_music: false,
    output_width: 1080,
    output_height: 1920,
    frame_rate: 30,
    video_effect: 'ken_burns',
    image_width: 1024,
    image_height: 1024,
    inference_steps: 4,
    music_duration: 60,
    image_provider: 'together',
    guidance_scale: 3.5,
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState<VideoCreationResult | null>(null);
  const [jobStatus, setJobStatus] = useState<string>('');

  // Voice data
  const [voices, setVoices] = useState<VoicesData>({ voices: {} });
  const [providers, setProviders] = useState<ProvidersData>({ providers: [] });
  const [loadingVoices, setLoadingVoices] = useState(true);
  
  // Auto-calculate duration when script changes
  useEffect(() => {
    if (formData.script.trim()) {
      const estimatedDuration = estimateScriptDuration(formData.script);
      // Add 15% buffer for natural speech variations and set minimum of 15 seconds
      const calculatedDuration = Math.max(15, Math.ceil(estimatedDuration * 1.15));
      
      // Only update if significantly different (to avoid constant updates)
      if (Math.abs(calculatedDuration - formData.max_duration) > 5) {
        setFormData(prev => ({
          ...prev,
          max_duration: calculatedDuration
        }));
      }
    }
  }, [formData.script, formData.max_duration]);

  // Job polling function
  const pollJobStatus = async (jobId: string, endpoint: string) => {
    const maxAttempts = 60; // 5 minutes max
    let attempts = 0;

    const poll = async () => {
      try {
        attempts++;
        const statusResponse = await directApi.get(`${endpoint}/${jobId}`);

        const status = statusResponse.data.status;
        const jobResult = statusResponse.data.result;
        const jobError = statusResponse.data.error;

        setJobStatus(status);

        if (status === 'completed') {
          setResult(jobResult);
          setLoading(false);
          return;
        } else if (status === 'failed') {
          setError(jobError || 'Job failed');
          setLoading(false);
          return;
        }

        if (attempts < maxAttempts) {
          setTimeout(poll, 5000); // Poll every 5 seconds
        } else {
          setError('Job polling timeout');
          setLoading(false);
        }
      } catch (err) {
        console.error('Polling error:', err);
        if (attempts < maxAttempts) {
          setTimeout(poll, 5000);
        } else {
          setError('Failed to check job status');
          setLoading(false);
        }
      }
    };

    poll();
  };

  // Load voices on mount
  useEffect(() => {
    const fetchVoices = async () => {
      try {
        const [voicesRes, providersRes] = await Promise.all([
          directApi.get('/api/v1/audio/voices/all'),
          directApi.get('/api/v1/audio/providers')
        ]);

        // Voices loaded successfully

        if (voicesRes.data?.voices) {
          setVoices({ voices: voicesRes.data.voices });
        }

        if (providersRes.data?.providers) {
          setProviders(providersRes.data);
          
          // Set default provider if available
          if (providersRes.data.default_provider) {
            const defaultProvider = providersRes.data.default_provider;
            const defaultVoices = voicesRes.data?.voices?.[defaultProvider] || [];
            
            setFormData(prev => ({
              ...prev,
              tts_provider: defaultProvider,
              voice: defaultVoices[0]?.name || prev.voice
            }));
          }
        }
      } catch (err) {
        console.error('Failed to fetch voices:', err);
        setError('Failed to load voice options. Please refresh the page.');
      } finally {
        setLoadingVoices(false);
      }
    };

    fetchVoices();
  }, []);

  // Update video dimensions when orientation changes
  useEffect(() => {
    const orientation = VIDEO_ORIENTATIONS.find(o => o.value === formData.video_orientation);
    if (orientation) {
      setFormData(prev => ({ 
        ...prev, 
        output_width: orientation.width, 
        output_height: orientation.height 
      }));
    }
  }, [formData.video_orientation]);

  // Update background music generation flag
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      generate_background_music: prev.background_music === 'ai_generate'
    }));
  }, [formData.background_music]);

  // Get available voices for current provider
  const getAvailableVoices = (): VoiceInfo[] => {
    if (!formData.tts_provider || !voices.voices[formData.tts_provider]) {
      return [];
    }
    return voices.voices[formData.tts_provider] || [];
  };

  // Handle form field changes
  const handleChange = (field: string, value: string | number | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle provider change
  const handleProviderChange = (newProvider: string) => {
    const availableVoices = voices.voices[newProvider] || [];
    setFormData(prev => ({
      ...prev,
      tts_provider: newProvider,
      voice: availableVoices[0]?.name || prev.voice
    }));
  };


  // Create video
  const handleCreateVideo = async () => {
    if (!formData.script.trim()) {
      setError('Please enter a script for your video');
      return;
    }

    const estimatedDuration = estimateScriptDuration(formData.script);
    if (estimatedDuration < 10) {
      setError(`Your script is too short. Estimated duration: ${estimatedDuration}s. Please add more content for at least 10 seconds.`);
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);
    setJobStatus('');

    try {
      let response;

      if (formData.method === 'stock') {
        // Use Topic-to-Video API (Stock footage) with all parameters
        const request: FootageToVideoRequest = {
          topic: formData.script,
          auto_topic: false,
          language: formData.language,
          script_provider: 'manual',
          script_type: formData.script_type,
          max_duration: formData.max_duration,
          voice: formData.voice,
          tts_provider: formData.tts_provider,
          tts_speed: formData.tts_speed,
          video_orientation: formData.video_orientation,
          segment_duration: formData.segment_duration,
          add_captions: formData.add_captions,
          caption_style: formData.caption_style,
          caption_color: formData.caption_color,
          caption_position: formData.caption_position,
          background_music: formData.background_music,
          background_music_volume: formData.background_music_volume,
          output_width: formData.output_width,
          output_height: formData.output_height,
          frame_rate: formData.frame_rate,
        };

        response = await directApi.footageToVideo(request);
      } else {
        // Use Script-to-Video API (AI images) with all parameters
        const request: AiimageToVideoRequest = {
          topic: formData.script,
          auto_topic: false,
          language: formData.language,
          script_provider: 'manual',
          script_type: formData.script_type,
          max_duration: formData.max_duration,
          voice: formData.voice,
          tts_provider: formData.tts_provider,
          tts_speed: formData.tts_speed,
          video_orientation: formData.video_orientation,
          segment_duration: formData.segment_duration,
          add_captions: formData.add_captions,
          caption_style: formData.caption_style,
          caption_color: formData.caption_color,
          caption_position: formData.caption_position,
          generate_background_music: formData.generate_background_music,
          background_music: formData.background_music,
          background_music_volume: formData.background_music_volume,
          music_duration: formData.music_duration,
          video_effect: formData.video_effect,
          output_width: formData.output_width,
          output_height: formData.output_height,
          frame_rate: formData.frame_rate,
          image_width: formData.image_width,
          image_height: formData.image_height,
          inference_steps: formData.inference_steps,
          image_provider: formData.image_provider,
          guidance_scale: formData.guidance_scale,
        };

        response = await directApi.aiimageToVideo(request);
      }
      
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to create video');
      }

      // Start polling for job status instead of navigating away
      const jobId = response.data.job_id;
      const endpoint = formData.method === 'stock' ? '/api/v1/ai/footage-to-video' : '/api/v1/ai/aiimage-to-video';
      pollJobStatus(jobId, endpoint);
      
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Render job result display
  const renderJobResult = () => {
    if (!result && !loading && !error) return null;

    return (
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0', mt: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            <PlayIcon />
            Video Creation Result
            {loading && <CircularProgress size={20} sx={{ ml: 1 }} />}
          </Typography>

          {loading && (
            <Box sx={{ mb: 2 }}>
              <LinearProgress sx={{ mb: 1, height: 6, borderRadius: 3 }} />
              <Typography variant="body2" color="text.secondary">
                Status: {jobStatus || 'Processing...'}
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                This may take several minutes. The AI is generating your video through multiple steps including script processing, audio synthesis, image generation, and final video assembly.
              </Typography>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          {result && jobStatus === 'completed' && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SuccessIcon />
                  🎉 Video created successfully!
                </Box>
              </Alert>

              {/* Download Links */}
              <Grid container spacing={2} sx={{ mb: 2 }}>
                {result.final_video_url && (
                  <Grid item xs={12} md={4}>
                    <Button
                      fullWidth
                      variant="contained"
                      startIcon={<DownloadIcon />}
                      href={result.final_video_url}
                      target="_blank"
                      size="large"
                    >
                      Download Final Video
                    </Button>
                  </Grid>
                )}
                {result.video_with_audio_url && (
                  <Grid item xs={12} md={4}>
                    <Button
                      fullWidth
                      variant="outlined"
                      startIcon={<DownloadIcon />}
                      href={result.video_with_audio_url}
                      target="_blank"
                      size="large"
                    >
                      Download Video (No Captions)
                    </Button>
                  </Grid>
                )}
                <Grid item xs={12} md={4}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="primary"
                    startIcon={<LibraryIcon />}
                    onClick={() => navigate('/dashboard/library')}
                    size="large"
                  >
                    View in Library
                  </Button>
                </Grid>
              </Grid>

              {/* Video Details */}
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle1">Video Details & Assets</Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <Grid container spacing={2}>
                    {/* Video Info */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        Video Information
                      </Typography>
                      <Typography variant="body2">Duration: {result.video_duration?.toFixed(1)}s</Typography>
                      <Typography variant="body2">Processing Time: {result.processing_time?.toFixed(1)}s</Typography>
                      <Typography variant="body2">Word Count: {result.word_count}</Typography>
                      <Typography variant="body2">Segments: {result.segments_count}</Typography>
                    </Grid>

                    {/* Additional Downloads */}
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        Additional Assets
                      </Typography>
                      {result.audio_url && (
                        <Button
                          size="small"
                          startIcon={<DownloadIcon />}
                          href={result.audio_url}
                          target="_blank"
                          sx={{ display: 'block', mb: 1 }}
                        >
                          Download Audio Only
                        </Button>
                      )}
                      {result.srt_url && (
                        <Button
                          size="small"
                          startIcon={<DownloadIcon />}
                          href={result.srt_url}
                          target="_blank"
                          sx={{ display: 'block', mb: 1 }}
                        >
                          Download Captions (SRT)
                        </Button>
                      )}
                      {result.background_music_url && (
                        <Button
                          size="small"
                          startIcon={<DownloadIcon />}
                          href={result.background_music_url}
                          target="_blank"
                          sx={{ display: 'block', mb: 1 }}
                        >
                          Download Background Music
                        </Button>
                      )}
                    </Grid>
                  </Grid>

                  {/* Generated Script */}
                  {result.script_generated && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        Generated Script:
                      </Typography>
                      <Paper sx={{ p: 2, bgcolor: '#f8fafc', maxHeight: 200, overflow: 'auto' }}>
                        <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                          {result.script_generated}
                        </Typography>
                      </Paper>
                    </Box>
                  )}

                  {/* Generated Images */}
                  {result.generated_images && result.generated_images.length > 0 && (
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
                        Generated Images ({result.generated_images.length}):
                      </Typography>
                      <Grid container spacing={1}>
                        {result.generated_images.map((image: GeneratedImage, index: number) => (
                          <Grid item xs={6} md={3} key={index}>
                            <Box>
                              <img
                                src={image.url}
                                alt={`Generated image ${index + 1}`}
                                style={{ width: '100%', height: 100, objectFit: 'cover', borderRadius: 4 }}
                              />
                              <Typography variant="caption" sx={{ display: 'block', mt: 0.5 }}>
                                {image.prompt?.substring(0, 50)}...
                              </Typography>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  )}
                </AccordionDetails>
              </Accordion>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      pb: 2
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography 
          variant="h4" 
          sx={{ 
            fontWeight: 700,
            mb: 1,
            color: '#1a202c'
          }}
        >
          Video Creator 🎬
        </Typography>
        <Typography 
          variant="body1" 
          color="text.secondary"
          sx={{ fontSize: '1.1rem' }}
        >
          Create videos from your custom scripts using stock footage or AI-generated images.
        </Typography>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Main Content */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3, flex: 1 }}>
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {/* Script Input */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={6}
                label="Video Script"
                value={formData.script}
                onChange={(e) => handleChange('script', e.target.value)}
                placeholder="Enter your video script here. Write engaging content that will be spoken by the AI narrator..."
                helperText={
                  formData.script.trim() 
                    ? `Estimated duration: ${estimateScriptDuration(formData.script)}s (auto-calculated from script length)`
                    : "Duration will be automatically calculated based on your script length (minimum 10s required)"
                }
              />
            </Grid>

            {/* Video Method Selection */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
                <SettingsIcon />
                Video Style
              </Typography>
              <RadioGroup
                value={formData.method}
                onChange={(e) => handleChange('method', e.target.value as VideoMethod)}
                row
              >
                <FormControlLabel
                  value="stock"
                  control={<Radio />}
                  label={
                    <Box display="flex" alignItems="center" gap={1}>
                      <StockIcon />
                      <Box>
                        <Typography variant="body1">Stock Footage</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Real video backgrounds from Pexels
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
                <FormControlLabel
                  value="ai_images"
                  control={<Radio />}
                  label={
                    <Box display="flex" alignItems="center" gap={1}>
                      <AIIcon />
                      <Box>
                        <Typography variant="body1">AI Images</Typography>
                        <Typography variant="caption" color="text.secondary">
                          Custom AI-generated visuals
                        </Typography>
                      </Box>
                    </Box>
                  }
                />
              </RadioGroup>
            </Grid>

            {/* Basic Settings */}
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Language</InputLabel>
                <Select
                  value={formData.language}
                  onChange={(e) => handleChange('language', e.target.value)}
                  label="Language"
                >
                  {LANGUAGES.map((lang) => (
                    <MenuItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Script Type</InputLabel>
                <Select
                  value={formData.script_type}
                  onChange={(e) => handleChange('script_type', e.target.value)}
                  label="Script Type"
                >
                  {SCRIPT_TYPES.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type.replace('_', ' ').charAt(0).toUpperCase() + type.replace('_', ' ').slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Duration field - only show when no script is provided */}
            <Grid item xs={12} md={4}>
              {formData.script.trim() ? (
                <TextField
                  fullWidth
                  label="Estimated Duration"
                  value={`~${estimateScriptDuration(formData.script)} seconds (auto-calculated)`}
                  InputProps={{
                    readOnly: true,
                  }}
                  helperText="Duration calculated from script length (~2.5 words/second)"
                  variant="outlined"
                />
              ) : (
                <FormControl fullWidth>
                  <InputLabel>Video Duration</InputLabel>
                  <Select
                    value={formData.max_duration}
                    onChange={(e) => handleChange('max_duration', e.target.value)}
                    label="Video Duration"
                  >
                    <MenuItem value={30}>Short (30 sec)</MenuItem>
                    <MenuItem value={60}>Standard (1 min)</MenuItem>
                    <MenuItem value={90}>Extended (1.5 min)</MenuItem>
                    <MenuItem value={120}>Long (2 min)</MenuItem>
                    <MenuItem value={180}>Very Long (3 min)</MenuItem>
                    <MenuItem value={300}>Extended (5 min)</MenuItem>
                  </Select>
                </FormControl>
              )}
            </Grid>

            {/* Voice Settings */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>TTS Provider</InputLabel>
                <Select
                  value={formData.tts_provider}
                  onChange={(e) => handleProviderChange(e.target.value)}
                  label="TTS Provider"
                  disabled={loadingVoices}
                >
                  {providers.providers.map((provider) => (
                    <MenuItem key={provider} value={provider}>
                      {provider.charAt(0).toUpperCase() + provider.slice(1)}
                    </MenuItem>
                  ))}
                  {providers.providers.length === 0 && (
                    <MenuItem value="" disabled>
                      {loadingVoices ? 'Loading providers...' : 'No providers available'}
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Voice</InputLabel>
                <Select
                  value={formData.voice}
                  onChange={(e) => handleChange('voice', e.target.value)}
                  label="Voice"
                  disabled={loadingVoices}
                >
                  {getAvailableVoices().map((voice) => (
                    <MenuItem key={voice.name} value={voice.name}>
                      {voice.description || voice.name} ({voice.gender})
                    </MenuItem>
                  ))}
                  {getAvailableVoices().length === 0 && (
                    <MenuItem value="" disabled>
                      {loadingVoices 
                        ? 'Loading voices...' 
                        : !formData.tts_provider 
                        ? 'Select a provider first'
                        : 'No voices available for this provider'
                      }
                    </MenuItem>
                  )}
                </Select>
              </FormControl>
            </Grid>

            {/* Advanced Settings */}
            <Grid item xs={12}>
              {/* TTS Speed - moved outside AdvancedVideoSettings since it's not included there */}
              <Grid container spacing={3} sx={{ mb: 2 }}>
                <Grid item xs={12} md={4}>
                  <Typography gutterBottom>TTS Speed: {formData.tts_speed}x</Typography>
                  <Slider
                    value={formData.tts_speed}
                    onChange={(_, value) => handleChange('tts_speed', Array.isArray(value) ? value[0] : value)}
                    min={0.5}
                    max={2.0}
                    step={0.1}
                    marks={[
                      { value: 0.5, label: '0.5x' },
                      { value: 1.0, label: '1x' },
                      { value: 1.5, label: '1.5x' },
                      { value: 2.0, label: '2x' }
                    ]}
                  />
                </Grid>
              </Grid>
              
              <AdvancedVideoSettings
                video_orientation={formData.video_orientation}
                segment_duration={formData.segment_duration}
                frame_rate={formData.frame_rate}
                output_width={formData.output_width}
                output_height={formData.output_height}
                add_captions={formData.add_captions}
                caption_style={formData.caption_style}
                caption_color={formData.caption_color}
                background_music={formData.background_music}
                background_music_volume={formData.background_music_volume}
                generate_background_music={formData.generate_background_music}
                music_duration={formData.music_duration}
                image_width={formData.image_width}
                image_height={formData.image_height}
                inference_steps={formData.inference_steps}
                guidance_scale={formData.guidance_scale}
                video_effect={formData.video_effect}
                image_provider={formData.image_provider}
                onChange={handleChange}
                showImageSettings={formData.method === 'ai_images'}
                showMusicGeneration={formData.method === 'ai_images'}
                showVideoEffects={formData.method === 'ai_images'}
                showImageProviderSettings={formData.method === 'ai_images'}
              />

            </Grid>

            {/* Method-specific Info */}
            <Grid item xs={12}>
              <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 2, border: '1px solid', borderColor: 'divider' }}>
                {formData.method === 'stock' ? (
                  <Box>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <StockIcon color="primary" />
                      <Typography variant="h6">Stock Footage Mode</Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Your script will be used to search for relevant stock videos from Pexels. 
                      The AI will automatically find background footage that matches your content.
                    </Typography>
                    <Box display="flex" gap={1} mt={1}>
                      <Chip label="Real footage" size="small" />
                      <Chip label="Fast processing" size="small" />
                      <Chip label="Professional quality" size="small" />
                    </Box>
                  </Box>
                ) : (
                  <Box>
                    <Box display="flex" alignItems="center" gap={1} mb={1}>
                      <AIIcon color="primary" />
                      <Typography variant="h6">AI Images Mode</Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      AI will generate custom images for each part of your script using advanced FLUX image generation models. 
                      Creates unique visuals tailored to your content with customizable effects.
                    </Typography>
                    <Box display="flex" gap={1} mt={1}>
                      <Chip label="Custom visuals" size="small" />
                      <Chip label="Unique content" size="small" />
                      <Chip label="AI generated" size="small" />
                      <Chip label={`${formData.video_effect} effect`} size="small" />
                    </Box>
                  </Box>
                )}
              </Box>
            </Grid>

            {/* Create Button */}
            <Grid item xs={12}>
              <Box display="flex" justifyContent="center" mt={2}>
                <Button
                  variant="contained"
                  size="large"
                  onClick={handleCreateVideo}
                  disabled={loading || !formData.script.trim()}
                  startIcon={loading ? <CircularProgress size={20} /> : <PlayIcon />}
                  sx={{ minWidth: 250, py: 1.5 }}
                >
                  {loading ? 'Creating Video...' : `Create ${formData.method === 'stock' ? 'Stock Footage' : 'AI Images'} Video`}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* Result Display */}
      {renderJobResult()}
    </Box>
  );
};

export default VideoCreator;