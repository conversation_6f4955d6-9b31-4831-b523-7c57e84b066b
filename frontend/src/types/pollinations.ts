// Pollinations AI Types

export interface VisionAnalysisParams {
  image_url?: string;
  question: string;
  model: string;
}

export interface TextGenerationParams {
  prompt: string;
  model: string;
  seed?: number;
  temperature?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  system?: string;
  json_mode: boolean;
}

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ChatCompletionParams {
  messages: ChatMessage[];
  model: string;
  seed?: number;
  temperature?: number;
  top_p?: number;
  presence_penalty?: number;
  frequency_penalty?: number;
  json_mode: boolean;
}

export interface ChatCompletionChoice {
  message?: {
    content?: string;
  };
}

export interface TextModelsResponse {
  text_models?: string[];
}

export interface ModelObject {
  name?: string;
  id?: string;
  model?: string;
  [key: string]: unknown;
}

export interface TextGenerationResponse {
  text?: string;
  content?: string;
  _metadata?: {
    generation_time?: number;
  };
  generation_time?: number;
}

export interface ChatCompletionResponse {
  choices?: ChatCompletionChoice[];
  _metadata?: {
    generation_time?: number;
  };
}

export interface JobResult {
  job_id: string;
  status?: string;
  result?: {
    text?: string;
    response?: Record<string, unknown>;
    assistant_message?: string;
    model_used?: string;
    generation_time?: number;
    prompt?: string;
    character_count?: number;
    question?: string;
    image_url?: string;
    file_name?: string;
    file_size?: number;
    message_count?: number;
    has_tool_calls?: boolean;
  };
  error?: string | null;
}

export interface ModelOption {
  name: string;
  label: string;
}

export type JobType = 'vision' | 'text' | 'chat';
export type JobStatus = 'pending' | 'processing' | 'completed' | 'failed' | null;

export interface ExamplePrompts {
  text: string[];
  vision: string[];
  chat: string[];
}