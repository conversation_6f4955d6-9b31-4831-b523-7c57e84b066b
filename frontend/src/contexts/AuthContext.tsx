import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiClient } from '../utils/api';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  apiKey: string | null;
  userRole: 'admin' | 'user';
  login: (apiKeyOrUsername: string, password?: string) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  setApiKey: (key: string, role?: 'admin' | 'user') => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [apiKey, setApiKeyState] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<'admin' | 'user'>('user');

  const setApiKey = (key: string, role: 'admin' | 'user' = 'user') => {
    setApiKeyState(key);
    setUserRole(role);
    localStorage.setItem('ouinhi_api_key', key);
    localStorage.setItem('ouinhi_user_role', role);
    // Update axios defaults
    apiClient.defaults.headers.common['X-API-Key'] = key;
    setIsAuthenticated(true);
  };

  const login = async (apiKeyOrUsername: string, password?: string): Promise<void> => {
    if (password) {
      // Username/password authentication
      try {
        const response = await apiClient.post('/auth/login', {
          username: apiKeyOrUsername,
          password: password
        });
        
        if (response.data.success && response.data.api_key) {
          // Username/password login grants admin access
          setApiKey(response.data.api_key, 'admin');
          return;
        } else {
          throw new Error('Invalid credentials');
        }
      } catch (error: any) {
        throw new Error(error.response?.data?.message || 'Login failed');
      }
    } else {
      // API key authentication
      try {
        // Test the API key by making a simple request
        const response = await apiClient.post('/api/mcp/messages', {
          jsonrpc: '2.0',
          id: 'auth-test',
          method: 'initialize',
          params: {
            protocolVersion: '2024-11-05',
            capabilities: {},
            clientInfo: {
              name: 'Ouinhi Frontend',
              version: '1.0.0'
            }
          }
        }, {
          headers: {
            'X-API-Key': apiKeyOrUsername
          }
        });

        if (response.status === 200 && !response.data.error) {
          // Direct API key login grants user access (unless it's the main admin key)
          const isAdminKey = apiKeyOrUsername === localStorage.getItem('ouinhi_admin_key') || 
                            apiKeyOrUsername.length > 50; // Assume longer keys are admin keys
          setApiKey(apiKeyOrUsername, isAdminKey ? 'admin' : 'user');
          return;
        } else {
          throw new Error('Invalid API key');
        }
      } catch (error: any) {
        if (error.response?.status === 401 || error.response?.status === 403) {
          throw new Error('Invalid API key');
        }
        throw new Error('Authentication failed');
      }
    }
  };

  const logout = () => {
    setApiKeyState(null);
    setUserRole('user');
    localStorage.removeItem('ouinhi_api_key');
    localStorage.removeItem('ouinhi_user_role');
    // Remove from axios defaults
    delete apiClient.defaults.headers.common['X-API-Key'];
    setIsAuthenticated(false);
  };

  const checkAuth = async (): Promise<boolean> => {
    const storedKey = localStorage.getItem('ouinhi_api_key');
    const storedRole = localStorage.getItem('ouinhi_user_role') as 'admin' | 'user' || 'user';
    
    if (!storedKey) {
      setIsLoading(false);
      return false;
    }

    try {
      // Test the API key by making a simple request
      // We'll use the MCP messages endpoint with an initialize request
      const response = await apiClient.post('/api/mcp/messages', {
        jsonrpc: '2.0',
        id: 'auth-check',
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: {
            name: 'Ouinhi Frontend',
            version: '1.0.0'
          }
        }
      }, {
        headers: {
          'X-API-Key': storedKey
        }
      });

      if (response.status === 200 && !response.data.error) {
        setApiKeyState(storedKey);
        setUserRole(storedRole);
        apiClient.defaults.headers.common['X-API-Key'] = storedKey;
        setIsAuthenticated(true);
        return true;
      } else {
        // Invalid key
        localStorage.removeItem('ouinhi_api_key');
        localStorage.removeItem('ouinhi_user_role');
        return false;
      }
    } catch (error: any) {
      console.error('Auth check failed:', error);
      // If it's a 401 or 403, the key is invalid
      if (error.response?.status === 401 || error.response?.status === 403) {
        localStorage.removeItem('ouinhi_api_key');
        localStorage.removeItem('ouinhi_user_role');
      }
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    checkAuth();
  }, []);

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    apiKey,
    userRole,
    login,
    logout,
    checkAuth,
    setApiKey,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};