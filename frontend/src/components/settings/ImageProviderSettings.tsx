import React from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Slider,
} from '@mui/material';
import {
  PhotoLibrary as ImageIcon,
  Security as SafetyIcon,
  HighQuality as QualityIcon,
} from '@mui/icons-material';

interface ImageProviderSettingsProps {
  imageProvider: string;
  searchSafety?: string;
  imageQuality?: string;
  guidanceScale?: number;
  inferenceSteps?: number;
  showAdvancedSettings?: boolean;
  showOnlyAiProviders?: boolean; // New prop to filter out stock providers for generation
  onImageProviderChange: (provider: string) => void; // eslint-disable-line
  onSearchSafetyChange?: (safety: string) => void; // eslint-disable-line
  onImageQualityChange?: (quality: string) => void; // eslint-disable-line
  onGuidanceScaleChange?: (value: number) => void; // eslint-disable-line
  onInferenceStepsChange?: (value: number) => void; // eslint-disable-line
}

const ImageProviderSettings: React.FC<ImageProviderSettingsProps> = ({
  imageProvider,
  searchSafety = 'moderate',
  imageQuality,
  guidanceScale = 3.5,
  inferenceSteps = 4,
  showAdvancedSettings = true,
  showOnlyAiProviders = false,
  onImageProviderChange,
  onSearchSafetyChange,
  onImageQualityChange,
  onGuidanceScaleChange,
  onInferenceStepsChange,
}) => {
  const imageProviders = [
    { 
      value: 'pexels', 
      label: 'Pexels', 
      category: 'stock',
      description: 'Search high-quality stock photos',
      features: ['Diverse Content', 'High Quality', 'Free License']
    },
    { 
      value: 'pixabay', 
      label: 'Pixabay', 
      category: 'stock',
      description: 'Search free images, vectors, and illustrations',
      features: ['Vector Graphics', 'Illustrations', 'Photos']
    },
    { 
      value: 'together', 
      label: 'Together.ai (FLUX.1 Schnell)', 
      category: 'ai',
      description: 'AI-generated images with FLUX model',
      features: ['Custom Content', 'Fast Generation', 'High Quality']
    },
    { 
      value: 'flux', 
      label: 'Flux Kontext Dev', 
      category: 'ai',
      description: 'Advanced AI image generation',
      features: ['Advanced Control', 'High Quality', 'Customizable']
    },
    { 
      value: 'pollinations', 
      label: 'Pollinations AI', 
      category: 'ai',
      description: 'Creative AI image generation',
      features: ['Creative', 'Artistic', 'Unique Styles']
    },
  ];

  const searchSafetyLevels = [
    { 
      value: 'strict', 
      label: 'Strict', 
      description: 'Family-friendly content only',
      color: 'success' as const
    },
    { 
      value: 'moderate', 
      label: 'Moderate', 
      description: 'Balanced content filtering',
      color: 'warning' as const
    },
    { 
      value: 'off', 
      label: 'Off', 
      description: 'No content filtering',
      color: 'error' as const
    },
  ];

  const imageQualities = [
    { value: 'standard', label: 'Standard', description: 'Good quality, faster processing', icon: '📺', recommended: false },
    { value: 'high', label: 'High', description: 'High quality, balanced processing', icon: '🎥', recommended: true },
    { value: 'ultra', label: 'Ultra', description: 'Highest quality, slower processing', icon: '💎', recommended: false },
  ];

  // Filter providers based on showOnlyAiProviders prop
  const filteredProviders = showOnlyAiProviders 
    ? imageProviders.filter(p => p.category === 'ai')
    : imageProviders;

  const selectedProvider = filteredProviders.find(p => p.value === imageProvider);
  const selectedSafety = searchSafetyLevels.find(s => s.value === searchSafety);
  const selectedQuality = imageQualities.find(q => q.value === imageQuality);
  const isAiProvider = selectedProvider?.category === 'ai';

  const getProviderCategoryColor = (category: string) => {
    switch (category) {
      case 'ai': return '#2563eb';
      case 'stock': return '#dc2626';
      default: return '#6b7280';
    }
  };

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'pexels': return '📹';
      case 'pixabay': return '🎨';
      case 'together': return '🤖';
      case 'flux': return '⚡';
      case 'pollinations': return '🌸';
      default: return '📸';
    }
  };

  const getSafetyIcon = (level: string) => {
    switch (level) {
      case 'strict': return '🔒';
      case 'moderate': return '⚖️';
      case 'off': return '🔓';
      default: return '🔒';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <ImageIcon color="primary" />
        Image Provider Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Image Provider Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Image Provider</InputLabel>
            <Select
              value={imageProvider}
              onChange={(e) => onImageProviderChange(e.target.value)}
              label="Image Provider"
            >
              {filteredProviders.map((provider) => (
                <MenuItem key={provider.value} value={provider.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    <span style={{ fontSize: '1.2em' }}>{getProviderIcon(provider.value)}</span>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {provider.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        {provider.description}
                      </Typography>
                    </Box>
                    {provider.value === 'together' && <Chip label="Recommended" size="small" variant="outlined" />}
                    <Chip
                      size="small"
                      label={provider.category === 'ai' ? 'AI' : 'Stock'}
                      sx={{ 
                        backgroundColor: getProviderCategoryColor(provider.category),
                        color: 'white',
                        fontSize: '0.7rem'
                      }}
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Content Safety */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Content Safety</InputLabel>
            <Select
              value={searchSafety}
              onChange={(e) => onSearchSafetyChange?.(e.target.value)}
              label="Content Safety"
            >
              {searchSafetyLevels.map((safety) => (
                <MenuItem key={safety.value} value={safety.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '1.2em' }}>{getSafetyIcon(safety.value)}</span>
                    <SafetyIcon fontSize="small" />
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {safety.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {safety.description}
                      </Typography>
                    </Box>
                    {safety.value === 'moderate' && <Chip label="Recommended" size="small" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Image Quality */}
        {imageQuality !== undefined && onImageQualityChange && (
          <Grid item xs={12} md={6}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Image Quality</InputLabel>
              <Select
                value={imageQuality}
                onChange={(e) => onImageQualityChange(e.target.value)}
                label="Image Quality"
              >
                {imageQualities.map((quality) => (
                  <MenuItem key={quality.value} value={quality.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span style={{ fontSize: '1.2em' }}>{quality.icon}</span>
                      <QualityIcon fontSize="small" />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {quality.label} Quality
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {quality.description}
                        </Typography>
                      </Box>
                      {quality.recommended && <Chip label="Recommended" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* AI Provider Advanced Settings */}
        {isAiProvider && showAdvancedSettings && (
          <>
            {/* Guidance Scale for Flux */}
            {imageProvider === 'flux' && guidanceScale !== undefined && onGuidanceScaleChange && (
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>Guidance Scale: {guidanceScale}</Typography>
                <Slider
                  value={guidanceScale}
                  onChange={(_, value) => onGuidanceScaleChange(Array.isArray(value) ? value[0] : value)}
                  min={1.0}
                  max={20.0}
                  step={0.1}
                  marks={[
                    { value: 1.0, label: 'Creative' },
                    { value: 3.5, label: 'Balanced' },
                    { value: 7.0, label: 'Precise' },
                    { value: 15.0, label: 'Strict' }
                  ]}
                />
              </Grid>
            )}

            {/* Inference Steps */}
            {inferenceSteps !== undefined && onInferenceStepsChange && (
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>Inference Steps: {inferenceSteps}</Typography>
                <Slider
                  value={inferenceSteps}
                  onChange={(_, value) => onInferenceStepsChange(Array.isArray(value) ? value[0] : value)}
                  min={1}
                  max={imageProvider === 'together' ? 12 : 50}
                  step={1}
                  marks={
                    imageProvider === 'together' ? [
                      { value: 1, label: '1 (Fast)' },
                      { value: 4, label: '4 (Default)' },
                      { value: 8, label: '8 (Quality)' },
                      { value: 12, label: '12 (Ultra)' }
                    ] : [
                      { value: 1, label: '1 (Fast)' },
                      { value: 4, label: '4 (Default)' },
                      { value: 20, label: '20 (Quality)' },
                      { value: 50, label: '50 (Ultra)' }
                    ]
                  }
                />
              </Grid>
            )}
          </>
        )}

        {/* Selected Provider Info */}
        {selectedProvider && (
          <Grid item xs={12}>
            <Box sx={{ 
              p: 2, 
              backgroundColor: '#f8f9fa', 
              borderRadius: 1,
              border: '1px solid #e2e8f0'
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <ImageIcon />
                Current Image Provider Configuration
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip 
                  label={selectedProvider.label}
                  size="small" 
                  color="primary"
                />
                <Chip 
                  label={selectedProvider.category === 'ai' ? 'AI Generated' : 'Stock Photos'}
                  size="small" 
                  sx={{ 
                    backgroundColor: getProviderCategoryColor(selectedProvider.category),
                    color: 'white'
                  }}
                />
                {selectedProvider.features.map((feature) => (
                  <Chip 
                    key={feature}
                    label={feature} 
                    size="small" 
                    variant="outlined"
                  />
                ))}
                {selectedSafety && (
                  <Chip 
                    label={`${selectedSafety.label} Safety`}
                    size="small" 
                    variant="outlined"
                  />
                )}
                {selectedQuality && (
                  <Chip 
                    label={`${selectedQuality.label} Quality`}
                    size="small" 
                    variant="outlined"
                  />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Images will be sourced from {selectedProvider.label} with {selectedSafety?.label?.toLowerCase() || 'default'} content filtering
                {selectedQuality && ` in ${selectedQuality.label.toLowerCase()} quality`}.
                {selectedProvider.description}
              </Typography>
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Provider Statistics */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          🎨 <strong>Available:</strong> {filteredProviders.length} image providers{showOnlyAiProviders ? ' (AI only)' : ''}, {searchSafetyLevels.length} safety levels, {imageQualities.length} quality options
          • Current: {selectedProvider?.label || 'None'} with {selectedSafety?.label || 'default'} safety
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          💡 <strong>Tip:</strong> Together.ai offers the best balance of quality and speed for AI-generated images. Use "moderate" safety for balanced results.
        </Typography>
      </Box>
    </Box>
  );
};

export default ImageProviderSettings;