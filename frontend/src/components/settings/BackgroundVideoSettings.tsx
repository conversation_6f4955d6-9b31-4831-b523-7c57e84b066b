import React from 'react';
import {
  <PERSON>,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
} from '@mui/material';
import {
  VideoLibrary as VideoIcon,
  Shield as SafetyIcon,
  HighQuality as QualityIcon,
} from '@mui/icons-material';

import {
  FOOTAGE_PROVIDERS,
  SEARCH_SAFETY_LEVELS,
  FOOTAGE_QUALITIES,
  AI_VIDEO_PROVIDERS
} from '../../types/contentCreation';

interface BackgroundVideoSettingsProps {
  footageProvider: string;
  aiVideoProvider?: string;
  searchSafety: string;
  footageQuality?: string;
  searchTermsPerScene?: number;
  onFootageProviderChange: (provider: string) => void; // eslint-disable-line
  onAiVideoProviderChange?: (provider: string) => void; // eslint-disable-line
  onSearchSafetyChange: (safety: string) => void; // eslint-disable-line
  onFootageQualityChange?: (quality: string) => void; // eslint-disable-line
  onSearchTermsPerSceneChange?: (terms: number) => void; // eslint-disable-line
}

const BackgroundVideoSettings: React.FC<BackgroundVideoSettingsProps> = ({
  footageProvider,
  aiVideoProvider,
  searchSafety,
  footageQuality,
  searchTermsPerScene,
  onFootageProviderChange,
  onAiVideoProviderChange,
  onSearchSafetyChange,
  onFootageQualityChange,
  onSearchTermsPerSceneChange,
}) => {
  const selectedProvider = FOOTAGE_PROVIDERS.find(provider => provider === footageProvider);
  const selectedSafety = SEARCH_SAFETY_LEVELS.find(level => level === searchSafety);
  const selectedQuality = FOOTAGE_QUALITIES.find(quality => quality === footageQuality);

  const getProviderIcon = (provider: string) => {
    switch (provider) {
      case 'pexels': return '📹';
      case 'pixabay': return '🎬';
      case 'ai_generated': return '🤖';
      default: return '📺';
    }
  };

  const getSafetyIcon = (level: string) => {
    switch (level) {
      case 'strict': return '🔒';
      case 'moderate': return '⚖️';
      case 'off': return '🔓';
      default: return '🔒';
    }
  };

  const getQualityIcon = (quality: string) => {
    switch (quality) {
      case 'standard': return '📺';
      case 'high': return '🎥';
      case 'ultra': return '💎';
      default: return '🎥';
    }
  };

  const getAiProviderIcon = (provider: string) => {
    switch (provider) {
      case 'ltx_video': return '⚡';
      case 'wavespeed': return '🌊';
      case 'comfyui': return '🎛️';
      default: return '🤖';
    }
  };

  const getProviderCategoryColor = (provider: string) => {
    switch (provider) {
      case 'ai_generated': return '#2563eb';
      case 'pexels': return '#05A081';
      case 'pixabay': return '#2ec66d';
      default: return '#6b7280';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <VideoIcon color="primary" />
        Background Video Settings
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        Choose how to source background videos for your content. Stock providers (Pexels, Pixabay) offer real footage from creators, 
        while AI generation creates custom videos from text prompts using the WaveSpeed AI model.
      </Typography>

      <Grid container spacing={3}>
        {/* Footage Provider */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Footage Provider</InputLabel>
            <Select
              value={footageProvider}
              onChange={(e) => onFootageProviderChange(e.target.value)}
              label="Footage Provider"
            >
              {FOOTAGE_PROVIDERS.map((provider) => (
                <MenuItem key={provider} value={provider}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    <span style={{ fontSize: '1.2em' }}>{getProviderIcon(provider)}</span>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {provider === 'ai_generated' ? 'AI Generated Videos' : 
                         provider.charAt(0).toUpperCase() + provider.slice(1)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        {provider === 'pexels' && 'High-quality stock footage from professional creators'}
                        {provider === 'pixabay' && 'Free stock videos with diverse content library'}
                        {provider === 'ai_generated' && 'Custom videos generated from text prompts using AI models'}
                      </Typography>
                    </Box>
                    {provider === 'pexels' && <Chip label="Recommended" size="small" variant="outlined" />}
                    {provider === 'ai_generated' && <Chip label="Custom" size="small" variant="outlined" />}
                    <Chip
                      size="small"
                      label={provider === 'ai_generated' ? 'AI' : 'Stock'}
                      sx={{ 
                        backgroundColor: getProviderCategoryColor(provider),
                        color: 'white',
                        fontSize: '0.7rem'
                      }}
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* AI Video Provider (only shown when ai_generated is selected) */}
        {footageProvider === 'ai_generated' && aiVideoProvider !== undefined && onAiVideoProviderChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>AI Video Provider</InputLabel>
              <Select
                value={aiVideoProvider || 'wavespeed'}
                onChange={(e) => onAiVideoProviderChange(e.target.value)}
                label="AI Video Provider"
              >
                {AI_VIDEO_PROVIDERS.map((provider) => (
                  <MenuItem key={provider} value={provider}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <span style={{ fontSize: '1.2em' }}>{getAiProviderIcon(provider)}</span>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {provider === 'ltx_video' ? 'LTX-Video' : 
                           provider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                          {provider === 'ltx_video' && 'High-quality video generation with fine control'}
                          {provider === 'wavespeed' && 'Fast video generation with multiple models'}
                          {provider === 'comfyui' && 'Custom video generation using ComfyUI workflows'}
                        </Typography>
                      </Box>
                      {provider === 'ltx_video' && <Chip label="High Quality" size="small" variant="outlined" />}
                      {provider === 'wavespeed' && <Chip label="Fast" size="small" variant="outlined" />}
                      {provider === 'comfyui' && <Chip label="Custom" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Search Safety */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Content Safety</InputLabel>
            <Select
              value={searchSafety}
              onChange={(e) => onSearchSafetyChange(e.target.value)}
              label="Content Safety"
            >
              {SEARCH_SAFETY_LEVELS.map((level) => (
                <MenuItem key={level} value={level}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '1.2em' }}>{getSafetyIcon(level)}</span>
                    <SafetyIcon fontSize="small" />
                    <Box sx={{ flexGrow: 1 }}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </Box>
                    {level === 'moderate' && <Chip label="Recommended" size="small" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Footage Quality */}
        {footageQuality !== undefined && onFootageQualityChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Footage Quality</InputLabel>
              <Select
                value={footageQuality || 'high'}
                onChange={(e) => onFootageQualityChange(e.target.value)}
                label="Footage Quality"
              >
                {FOOTAGE_QUALITIES.map((quality) => (
                  <MenuItem key={quality} value={quality}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span style={{ fontSize: '1.2em' }}>{getQualityIcon(quality)}</span>
                      <QualityIcon fontSize="small" />
                      <Box sx={{ flexGrow: 1 }}>
                        {quality.charAt(0).toUpperCase() + quality.slice(1)} Quality
                      </Box>
                      {quality === 'high' && <Chip label="Recommended" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Search Terms Per Scene */}
        {searchTermsPerScene !== undefined && onSearchTermsPerSceneChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Search Terms Per Scene</InputLabel>
              <Select
                value={searchTermsPerScene || 3}
                onChange={(e) => onSearchTermsPerSceneChange(Number(e.target.value))}
                label="Search Terms Per Scene"
              >
                {[1, 2, 3, 4, 5].map((count) => (
                  <MenuItem key={count} value={count}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span>{count} search term{count !== 1 ? 's' : ''}</span>
                      {count === 3 && <Chip label="Optimal" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Current Settings Summary */}
        <Grid item xs={12}>
          <Box sx={{ 
            p: 2, 
            backgroundColor: '#f8f9fa', 
            borderRadius: 1,
            border: '1px solid #e2e8f0'
          }}>
            <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <VideoIcon />
              Current Background Video Configuration
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
              <Chip 
                label={selectedProvider ? selectedProvider.charAt(0).toUpperCase() + selectedProvider.slice(1).replace('_', ' ') : 'Unknown'}
                size="small" 
                color="primary"
              />
              <Chip 
                label={`${selectedSafety ? selectedSafety.charAt(0).toUpperCase() + selectedSafety.slice(1) : 'Default'} Safety`}
                size="small" 
                variant="outlined"
              />
              {selectedQuality && (
                <Chip 
                  label={`${selectedQuality.charAt(0).toUpperCase() + selectedQuality.slice(1)} Quality`}
                  size="small" 
                  variant="outlined"
                />
              )}
              {searchTermsPerScene && (
                <Chip
                  label={`${searchTermsPerScene} search terms`}
                  size="small"
                  variant="outlined"
                />
              )}
              {footageProvider === 'ai_generated' && aiVideoProvider && (
                <Chip
                  label={aiVideoProvider === 'ltx_video' ? 'LTX-Video' : 
                         aiVideoProvider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}
                  size="small"
                  sx={{
                    backgroundColor: aiVideoProvider === 'ltx_video' ? '#8b5cf6' : 
                                   aiVideoProvider === 'wavespeed' ? '#06b6d4' : '#f59e0b',
                    color: 'white'
                  }}
                />
              )}
            </Box>
            
            <Typography variant="body2" color="text.secondary">
              Background videos will be sourced from {selectedProvider?.replace('_', ' ') || 'unknown provider'}
              {footageProvider === 'ai_generated' && aiVideoProvider && ` using ${aiVideoProvider === 'ltx_video' ? 'LTX-Video' : aiVideoProvider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}`}
              with {selectedSafety} content filtering
              {selectedQuality && ` in ${selectedQuality} quality`}
              {searchTermsPerScene && `. Using ${searchTermsPerScene} search terms per scene for better relevance.`}
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Provider Statistics */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          🎬 <strong>Available:</strong> {FOOTAGE_PROVIDERS.length} footage providers, {SEARCH_SAFETY_LEVELS.length} safety levels, {FOOTAGE_QUALITIES.length} quality options
          • Current: {selectedProvider?.replace('_', ' ')} with {selectedSafety} safety
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          💡 <strong>Tip:</strong> Pexels offers the best variety and quality for most content. AI Generated creates unique videos but requires more processing time. LTX-Video provides higher quality, WaveSpeed is faster, and ComfyUI offers custom workflows. Use "moderate" safety for balanced results.
        </Typography>
      </Box>
    </Box>
  );
};

export default BackgroundVideoSettings;