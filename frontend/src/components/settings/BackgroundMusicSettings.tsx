import React from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Slider,
  TextField,
} from '@mui/material';
import {
  MusicNote as MusicIcon,
  VolumeUp as VolumeIcon,
  AutoFixHigh as AIIcon,
} from '@mui/icons-material';

import { MUSIC_OPTIONS } from '../../constants/videoSettings';

interface BackgroundMusicSettingsProps {
  backgroundMusic: string;
  backgroundMusicVolume: number;
  musicDuration?: number;
  onBackgroundMusicChange: (music: string) => void; // eslint-disable-line
  onBackgroundMusicVolumeChange: (volume: number) => void; // eslint-disable-line  
  onMusicDurationChange?: (duration: number) => void; // eslint-disable-line
}

const BackgroundMusicSettings: React.FC<BackgroundMusicSettingsProps> = ({
  backgroundMusic,
  backgroundMusicVolume,
  musicDuration,
  onBackgroundMusicChange,
  onBackgroundMusicVolumeChange,
  onMusicDurationChange,
}) => {
  const selectedMusicOption = MUSIC_OPTIONS.find(option => option.value === backgroundMusic);
  const isNoMusicSelected = backgroundMusic === 'none';
  const isAiGenerationSelected = backgroundMusic === 'ai_generate';

  const getMusicCategoryColor = (category: string) => {
    switch (category) {
      case 'ai': return '#2563eb';
      case 'stock': return '#dc2626';
      case 'none': return '#6b7280';
      default: return '#6b7280';
    }
  };

  const getMusicCategoryIcon = (category: string) => {
    switch (category) {
      case 'ai': return <AIIcon fontSize="small" />;
      case 'stock': return <MusicIcon fontSize="small" />;
      default: return <MusicIcon fontSize="small" />;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <MusicIcon color="primary" />
        Background Music Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Music Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Background Music</InputLabel>
            <Select
              value={backgroundMusic}
              onChange={(e) => onBackgroundMusicChange(e.target.value)}
              label="Background Music"
            >
              {MUSIC_OPTIONS.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getMusicCategoryIcon(option.category || 'none')}
                    <Box sx={{ flexGrow: 1 }}>
                      {option.label}
                    </Box>
                    <Chip
                      size="small"
                      label={option.category}
                      sx={{ 
                        backgroundColor: getMusicCategoryColor(option.category || 'none'),
                        color: 'white',
                        fontSize: '0.75rem'
                      }}
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Volume Control */}
        {!isNoMusicSelected && (
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <VolumeIcon fontSize="small" color="action" />
              <Typography>
                Volume: {Math.round(backgroundMusicVolume * 100)}%
              </Typography>
            </Box>
            <Slider
              value={backgroundMusicVolume}
              onChange={(_e, value) => onBackgroundMusicVolumeChange(Array.isArray(value) ? value[0] : value)}
              min={0}
              max={1}
              step={0.05}
              marks={[
                { value: 0.1, label: '10%' },
                { value: 0.2, label: '20%' },
                { value: 0.3, label: '30%' },
                { value: 0.5, label: '50%' },
                { value: 0.8, label: '80%' }
              ]}
            />
          </Grid>
        )}

        {/* AI Music Duration */}
        {isAiGenerationSelected && musicDuration !== undefined && onMusicDurationChange && (
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              type="number"
              label="Music Duration (seconds)"
              value={musicDuration || 60}
              onChange={(e) => onMusicDurationChange(parseInt(e.target.value))}
              inputProps={{ min: 10, max: 300 }}
              helperText="Duration for AI-generated music (10-300s)"
            />
          </Grid>
        )}

        {/* Selected Music Info */}
        {selectedMusicOption && !isNoMusicSelected && (
          <Grid item xs={12}>
            <Box sx={{ 
              p: 2, 
              backgroundColor: '#f8f9fa', 
              borderRadius: 1,
              border: '1px solid #e2e8f0'
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                {getMusicCategoryIcon(selectedMusicOption.category || 'none')}
                {selectedMusicOption.label}
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip 
                  label={selectedMusicOption.category || 'Unknown'} 
                  size="small" 
                  sx={{ 
                    backgroundColor: getMusicCategoryColor(selectedMusicOption.category || 'none'),
                    color: 'white'
                  }}
                />
                <Chip 
                  label={`${Math.round(backgroundMusicVolume * 100)}% Volume`} 
                  size="small" 
                  variant="outlined"
                />
                {isAiGenerationSelected && musicDuration && (
                  <Chip 
                    label={`${musicDuration}s Duration`} 
                    size="small" 
                    variant="outlined"
                  />
                )}
              </Box>
              
              {selectedMusicOption.description && (
                <Typography variant="body2" color="text.secondary">
                  {selectedMusicOption.description}
                </Typography>
              )}
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Music Statistics */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          🎵 <strong>Available:</strong> {MUSIC_OPTIONS.length} music options
          • Current selection: {selectedMusicOption?.label || 'None'} 
          {selectedMusicOption?.category && ` (${selectedMusicOption.category})`}
        </Typography>
      </Box>
    </Box>
  );
};

export default BackgroundMusicSettings;