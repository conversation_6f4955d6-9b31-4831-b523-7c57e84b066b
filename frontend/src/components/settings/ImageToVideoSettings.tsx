import React from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Slider,
  TextField,
  Button,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Movie as VideoIcon,
  ZoomIn as ZoomIcon,
  PanTool as PanIcon,
  Videocam as KenBurnsIcon,
  Speed as SpeedIcon,
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
} from '@mui/icons-material';

import { 
  IMAGE_EFFECT_TYPES, 
  PAN_DIRECTIONS, 
  ZOOM_SPEED_PRESETS 
} from '../../constants/videoSettings';

interface KenBurnsKeypoint {
  time: number;
  x: number;
  y: number;
  zoom: number;
}

interface ImageToVideoSettingsProps {
  effectType: string;
  zoomSpeed: number;
  panDirection?: string;
  kenBurnsKeypoints?: KenBurnsKeypoint[];
  videoLength: number;
  onEffectTypeChange: (type: string) => void; // eslint-disable-line
  onZoomSpeedChange: (speed: number) => void; // eslint-disable-line
  onPanDirectionChange?: (direction: string) => void; // eslint-disable-line
  onKenBurnsKeypointsChange?: (keypoints: KenBurnsKeypoint[]) => void; // eslint-disable-line
}

const ImageToVideoSettings: React.FC<ImageToVideoSettingsProps> = ({
  effectType,
  zoomSpeed,
  panDirection,
  kenBurnsKeypoints,
  videoLength,
  onEffectTypeChange,
  onZoomSpeedChange,
  onPanDirectionChange,
  onKenBurnsKeypointsChange,
}) => {
  const selectedEffectType = IMAGE_EFFECT_TYPES.find(effect => effect.value === effectType);
  const selectedPanDirection = PAN_DIRECTIONS.find(direction => direction.value === panDirection);
  const selectedSpeedPreset = ZOOM_SPEED_PRESETS.find(preset => preset.value === zoomSpeed);

  const isZoomEffect = effectType === 'zoom';
  const isPanEffect = effectType === 'pan';
  const isKenBurnsEffect = effectType === 'ken_burns';
  const hasMotionEffect = effectType !== 'none';

  const getEffectTypeIcon = (type: string) => {
    switch (type) {
      case 'zoom': return <ZoomIcon fontSize="small" />;
      case 'pan': return <PanIcon fontSize="small" />;
      case 'ken_burns': return <KenBurnsIcon fontSize="small" />;
      default: return <VideoIcon fontSize="small" />;
    }
  };

  const addKenBurnsKeypoint = () => {
    if (!kenBurnsKeypoints || !onKenBurnsKeypointsChange) return;
    
    const newKeypoint: KenBurnsKeypoint = {
      time: kenBurnsKeypoints.length * (videoLength / 4), // Distribute evenly
      x: 0.5,
      y: 0.5,
      zoom: 1.0
    };
    
    onKenBurnsKeypointsChange([...kenBurnsKeypoints, newKeypoint]);
  };

  const removeKenBurnsKeypoint = (index: number) => {
    if (!kenBurnsKeypoints || !onKenBurnsKeypointsChange) return;
    
    const updatedKeypoints = kenBurnsKeypoints.filter((_, i) => i !== index);
    onKenBurnsKeypointsChange(updatedKeypoints);
  };

  const updateKenBurnsKeypoint = (index: number, field: keyof KenBurnsKeypoint, value: number) => {
    if (!kenBurnsKeypoints || !onKenBurnsKeypointsChange) return;
    
    const updatedKeypoints = kenBurnsKeypoints.map((keypoint, i) => 
      i === index ? { ...keypoint, [field]: value } : keypoint
    );
    onKenBurnsKeypointsChange(updatedKeypoints);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <VideoIcon color="primary" />
        Image-to-Video Effects
      </Typography>

      <Grid container spacing={3}>
        {/* Effect Type Selection */}
        <Grid item xs={12} md={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Effect Type</InputLabel>
            <Select
              value={effectType}
              onChange={(e) => onEffectTypeChange(e.target.value)}
              label="Effect Type"
            >
              {IMAGE_EFFECT_TYPES.map((effect) => (
                <MenuItem key={effect.value} value={effect.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    <span style={{ fontSize: '1.2em' }}>{effect.icon}</span>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2">{effect.label}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        {effect.description}
                      </Typography>
                    </Box>
                    {effect.value === 'zoom' && <Chip label="Most Popular" size="small" color="primary" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Zoom Speed (for zoom and ken_burns effects) */}
        {(isZoomEffect || isKenBurnsEffect) && (
          <Grid item xs={12} md={6}>
            <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <SpeedIcon fontSize="small" color="primary" />
              Zoom Speed: {zoomSpeed}
              {selectedSpeedPreset && (
                <Chip label={selectedSpeedPreset.label} size="small" variant="outlined" />
              )}
            </Typography>
            <Slider
              value={zoomSpeed}
              onChange={(_, value) => onZoomSpeedChange(Array.isArray(value) ? value[0] : value)}
              min={0}
              max={100}
              step={5}
              marks={ZOOM_SPEED_PRESETS.map(preset => ({ 
                value: preset.value, 
                label: preset.label 
              }))}
              sx={{ mt: 1 }}
              valueLabelDisplay="auto"
            />
            <Typography variant="caption" color="text.secondary">
              {selectedSpeedPreset?.description || 'Custom zoom speed'}
            </Typography>
          </Grid>
        )}

        {/* Pan Direction (for pan effect) */}
        {isPanEffect && panDirection !== undefined && onPanDirectionChange && (
          <Grid item xs={12} md={6}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Pan Direction</InputLabel>
              <Select
                value={panDirection}
                onChange={(e) => onPanDirectionChange(e.target.value)}
                label="Pan Direction"
              >
                {PAN_DIRECTIONS.map((direction) => (
                  <MenuItem key={direction.value} value={direction.value}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span style={{ fontSize: '1.2em' }}>{direction.icon}</span>
                      {direction.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Ken Burns Keypoints (advanced) */}
        {isKenBurnsEffect && kenBurnsKeypoints !== undefined && onKenBurnsKeypointsChange && (
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <KenBurnsIcon />
                  <Typography>Ken Burns Keypoints</Typography>
                  <Chip 
                    label={`${kenBurnsKeypoints.length} keypoints`} 
                    size="small" 
                    variant="outlined" 
                  />
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Define camera movement over time. Each keypoint represents position (x, y) and zoom level at a specific time.
                  </Typography>
                  
                  <Button
                    startIcon={<AddIcon />}
                    onClick={addKenBurnsKeypoint}
                    variant="outlined"
                    size="small"
                    sx={{ mb: 2 }}
                  >
                    Add Keypoint
                  </Button>
                </Box>

                {kenBurnsKeypoints.map((keypoint, index) => (
                  <Box key={index} sx={{ 
                    p: 2, 
                    mb: 2, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 1,
                    backgroundColor: '#fafafa'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                      <Typography variant="subtitle2">
                        Keypoint {index + 1}
                      </Typography>
                      <Tooltip title="Remove keypoint">
                        <IconButton 
                          size="small" 
                          onClick={() => removeKenBurnsKeypoint(index)}
                          disabled={kenBurnsKeypoints.length <= 2} // Keep at least 2 keypoints
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    
                    <Grid container spacing={2}>
                      <Grid item xs={6} md={3}>
                        <TextField
                          label="Time (s)"
                          type="number"
                          size="small"
                          fullWidth
                          value={keypoint.time}
                          onChange={(e) => updateKenBurnsKeypoint(index, 'time', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, max: videoLength, step: 0.1 }}
                        />
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <TextField
                          label="X Position"
                          type="number"
                          size="small"
                          fullWidth
                          value={keypoint.x}
                          onChange={(e) => updateKenBurnsKeypoint(index, 'x', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, max: 1, step: 0.1 }}
                        />
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <TextField
                          label="Y Position"
                          type="number"
                          size="small"
                          fullWidth
                          value={keypoint.y}
                          onChange={(e) => updateKenBurnsKeypoint(index, 'y', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, max: 1, step: 0.1 }}
                        />
                      </Grid>
                      <Grid item xs={6} md={3}>
                        <TextField
                          label="Zoom Level"
                          type="number"
                          size="small"
                          fullWidth
                          value={keypoint.zoom}
                          onChange={(e) => updateKenBurnsKeypoint(index, 'zoom', parseFloat(e.target.value) || 1)}
                          inputProps={{ min: 0.5, max: 3.0, step: 0.1 }}
                        />
                      </Grid>
                    </Grid>
                  </Box>
                ))}
                
                {kenBurnsKeypoints.length < 2 && (
                  <Typography variant="caption" color="error">
                    At least 2 keypoints are required for Ken Burns effect
                  </Typography>
                )}
              </AccordionDetails>
            </Accordion>
          </Grid>
        )}

        {/* Current Settings Summary */}
        {selectedEffectType && (
          <Grid item xs={12}>
            <Box sx={{ 
              p: 2, 
              backgroundColor: '#f8f9fa', 
              borderRadius: 1,
              border: '1px solid #e2e8f0'
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                {getEffectTypeIcon(selectedEffectType.value)}
                {selectedEffectType.label}
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip 
                  label={selectedEffectType.label} 
                  size="small" 
                  color="primary"
                />
                {hasMotionEffect && (
                  <Chip 
                    label={`Speed: ${zoomSpeed}`} 
                    size="small" 
                    variant="outlined"
                  />
                )}
                {selectedPanDirection && (
                  <Chip 
                    label={selectedPanDirection.label} 
                    size="small" 
                    variant="outlined"
                  />
                )}
                {isKenBurnsEffect && kenBurnsKeypoints && (
                  <Chip 
                    label={`${kenBurnsKeypoints.length} keypoints`} 
                    size="small" 
                    variant="outlined"
                  />
                )}
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                {selectedEffectType.description}
                {selectedSpeedPreset && ` • ${selectedSpeedPreset.description}`}
              </Typography>
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Effect Statistics */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          🎬 <strong>Available:</strong> {IMAGE_EFFECT_TYPES.length} effect types, {PAN_DIRECTIONS.length} pan directions
          • Current: {selectedEffectType?.label || 'None'}
          {hasMotionEffect && ` with speed ${zoomSpeed}`}
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          💡 <strong>Tip:</strong> Zoom effects work best with speeds 5-20. Ken Burns creates professional documentary-style movement.
        </Typography>
      </Box>
    </Box>
  );
};

export default ImageToVideoSettings;