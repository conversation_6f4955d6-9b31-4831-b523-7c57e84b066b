import React from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Avatar,
  Slider,
  Tooltip,
} from '@mui/material';
import {
  RecordVoiceOver as VoiceIcon,
  Language as LanguageIcon,
  Person as PersonIcon,
  Speed as SpeedIcon,
} from '@mui/icons-material';

import { VoiceInfo } from '../../types/contentCreation';

// Voice provider constants
const VOICE_PROVIDERS = ['kokoro', 'edge'] as const;

// Language constants
const LANGUAGES = ['en', 'fr', 'es', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'] as const;

interface VoiceSelectorProps {
  voiceProvider: string;
  voiceName: string;
  language: string;
  ttsSpeed?: number;
  voices: VoiceInfo[];
  onVoiceProviderChange: (provider: string) => void; // eslint-disable-line
  onVoiceNameChange: (name: string) => void; // eslint-disable-line  
  onLanguageChange: (language: string) => void; // eslint-disable-line
  onTtsSpeedChange?: (speed: number) => void; // eslint-disable-line
}

const VoiceSelector: React.FC<VoiceSelectorProps> = ({
  voiceProvider,
  voiceName,
  language,
  ttsSpeed = 1.0,
  voices,
  onVoiceProviderChange,
  onVoiceNameChange,
  onLanguageChange,
  onTtsSpeedChange,
}) => {
  const availableVoices = voices.filter(voice => 
    voice.provider === voiceProvider && voice.language.startsWith(language)
  );

  const selectedVoice = voices.find(voice => 
    voice.provider === voiceProvider && voice.name === voiceName
  );

  const handleProviderChange = (newProvider: string) => {
    onVoiceProviderChange(newProvider);
    // Auto-select first voice for the new provider and current language
    const firstVoice = voices.find(voice => 
      voice.provider === newProvider && voice.language === language
    );
    if (firstVoice) {
      onVoiceNameChange(firstVoice.name);
    }
  };

  const handleLanguageChange = (newLanguage: string) => {
    onLanguageChange(newLanguage);
    // Auto-select first voice for the current provider and new language
    const firstVoice = voices.find(voice => 
      voice.provider === voiceProvider && voice.language === newLanguage
    );
    if (firstVoice) {
      onVoiceNameChange(firstVoice.name);
    }
  };

  const getProviderDisplayName = (provider: string) => {
    switch (provider) {
      case 'kokoro': return 'Kokoro TTS';
      case 'edge': return 'Edge TTS';
      default: return provider;
    }
  };

  const getLanguageDisplayName = (lang: string) => {
    const languageNames: { [key: string]: string } = {
      'en': 'English',
      'fr': 'French',
      'es': 'Spanish',
      'de': 'German',
      'it': 'Italian',
      'pt': 'Portuguese',
      'ru': 'Russian',
      'ja': 'Japanese',
      'ko': 'Korean',
      'zh': 'Chinese',
    };
    return languageNames[lang] || lang;
  };

  const getGenderColor = (gender: string) => {
    switch (gender.toLowerCase()) {
      case 'male': return '#2563eb';
      case 'female': return '#dc2626';
      default: return '#6b7280';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <VoiceIcon color="primary" />
        Voice & Language Settings
      </Typography>

      <Grid container spacing={3}>
        {/* Voice Provider */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Voice Provider</InputLabel>
            <Select
              value={voiceProvider}
              onChange={(e) => handleProviderChange(e.target.value)}
              label="Voice Provider"
            >
              {VOICE_PROVIDERS.map((provider) => (
                <MenuItem key={provider} value={provider}>
                  {getProviderDisplayName(provider)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Language */}
        <Grid item xs={12} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Language</InputLabel>
            <Select
              value={language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              label="Language"
            >
              {LANGUAGES.map((lang) => (
                <MenuItem key={lang} value={lang}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LanguageIcon fontSize="small" />
                    {getLanguageDisplayName(lang)}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Speech Speed */}
        {onTtsSpeedChange && (
          <Grid item xs={12} md={4}>
            <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              <SpeedIcon fontSize="small" color="primary" />
              Speech Speed: {ttsSpeed}x
              <Tooltip title="Adjust how fast the voice speaks. 1.0 is normal speed, 0.5 is slower, 2.0 is faster.">
                <SpeedIcon fontSize="small" color="action" />
              </Tooltip>
            </Typography>
            <Slider
              value={ttsSpeed}
              onChange={(_, value) => onTtsSpeedChange(Array.isArray(value) ? value[0] : value)}
              min={0.5}
              max={2.0}
              step={0.1}
              marks={[
                { value: 0.5, label: '0.5x' },
                { value: 1.0, label: '1.0x' },
                { value: 1.5, label: '1.5x' },
                { value: 2.0, label: '2.0x' },
              ]}
              sx={{ mt: 1 }}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => `${value}x`}
            />
          </Grid>
        )}

        {/* Voice Selection */}
        <Grid item xs={12} md={onTtsSpeedChange ? 4 : 6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Voice</InputLabel>
            <Select
              value={voiceName}
              onChange={(e) => onVoiceNameChange(e.target.value)}
              label="Voice"
              disabled={availableVoices.length === 0}
            >
              {availableVoices.map((voice) => (
                <MenuItem key={voice.name} value={voice.name}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Avatar
                      sx={{ 
                        width: 24, 
                        height: 24, 
                        backgroundColor: getGenderColor(voice.gender),
                        fontSize: '0.75rem'
                      }}
                    >
                      <PersonIcon fontSize="small" />
                    </Avatar>
                    {voice.name}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {availableVoices.length === 0 && (
            <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
              No voices available for {getProviderDisplayName(voiceProvider)} in {getLanguageDisplayName(language)}
            </Typography>
          )}
        </Grid>

        {/* Selected Voice Info */}
        {selectedVoice && (
          <Grid item xs={12}>
            <Box sx={{ 
              p: 2, 
              backgroundColor: '#f8f9fa', 
              borderRadius: 1,
              border: '1px solid #e2e8f0'
            }}>
              <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                <Avatar
                  sx={{ 
                    width: 32, 
                    height: 32, 
                    backgroundColor: getGenderColor(selectedVoice.gender),
                    fontSize: '0.875rem'
                  }}
                >
                  <PersonIcon />
                </Avatar>
                {selectedVoice.name}
              </Typography>
              
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
                <Chip 
                  label={selectedVoice.gender} 
                  size="small" 
                  color={selectedVoice.gender.toLowerCase() === 'male' ? 'primary' : 'secondary'}
                />
                <Chip 
                  label={getLanguageDisplayName(selectedVoice.language)} 
                  size="small" 
                  variant="outlined"
                />
                <Chip 
                  label={getProviderDisplayName(selectedVoice.provider)} 
                  size="small" 
                  variant="outlined"
                />
                {selectedVoice.grade && (
                  <Chip 
                    label={`Grade: ${selectedVoice.grade}`} 
                    size="small" 
                    variant="outlined"
                  />
                )}
              </Box>
              
              {selectedVoice.description && (
                <Typography variant="body2" color="text.secondary">
                  {selectedVoice.description}
                </Typography>
              )}
            </Box>
          </Grid>
        )}
      </Grid>

      {/* Voice Statistics */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          📊 <strong>Available:</strong> {voices.length} total voices across {VOICE_PROVIDERS.length} providers
          • Current selection: {availableVoices.length} voices for {getProviderDisplayName(voiceProvider)} in {getLanguageDisplayName(language)}
        </Typography>
      </Box>
    </Box>
  );
};

export default VoiceSelector;