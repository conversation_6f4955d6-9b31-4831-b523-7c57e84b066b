import React from 'react';
import {
  <PERSON>,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Chip,
  Slider,
} from '@mui/material';
import {
  VideoLibrary as VideoIcon,
  PhotoLibrary as ImageIcon,
  Security as SafetyIcon,
  HighQuality as QualityIcon,
  AutoAwesome as AIIcon,
} from '@mui/icons-material';

import {
  SEARCH_SAFETY_LEVELS,
  FOOTAGE_QUALITIES,
  AI_VIDEO_PROVIDERS
} from '../../types/contentCreation';

// Media type options
const MEDIA_TYPES = [
  { value: 'video', label: 'Videos', icon: '🎥' },
  { value: 'image', label: 'Images', icon: '🖼️' },
] as const;

// Define provider types
type StockProvider = {
  value: 'pexels' | 'pixabay';
  label: string;
  supportedTypes: readonly ('video' | 'image')[];
  description: string;
  features: readonly string[];
};

type AIProvider = {
  value: 'ai_generated';
  label: string;
  supportedTypes: readonly ('video' | 'image')[];
  description: string;
  features: readonly string[];
};

type UnifiedProvider = StockProvider | AIProvider;

// Stock providers that support both videos and images
const STOCK_PROVIDERS: StockProvider[] = [
  { 
    value: 'pexels', 
    label: 'Pexels', 
    supportedTypes: ['video', 'image'],
    description: 'High-quality stock content from professional creators',
    features: ['Professional Quality', 'Free License', 'Large Library']
  },
  { 
    value: 'pixabay', 
    label: 'Pixabay', 
    supportedTypes: ['video', 'image'],
    description: 'Free stock content with diverse library',
    features: ['Diverse Content', 'Vector Graphics', 'Free License']
  },
];

// AI providers for image generation (used when media type is 'image' and provider is 'ai_generated')
const AI_IMAGE_PROVIDERS = [
  { 
    value: 'together', 
    label: 'Together.ai (FLUX.1 Schnell)', 
    description: 'AI-generated images with FLUX model',
    features: ['Custom Content', 'Fast Generation', 'High Quality']
  },
  { 
    value: 'flux', 
    label: 'Flux Kontext Dev', 
    description: 'Advanced AI image generation',
    features: ['Advanced Control', 'High Quality', 'Customizable']
  },
  { 
    value: 'pollinations', 
    label: 'Pollinations AI', 
    description: 'Creative AI image generation',
    features: ['Creative', 'Artistic', 'Unique Styles']
  },
] as const;

interface UnifiedMediaProviderSettingsProps {
  mediaType: 'video' | 'image';
  provider: 'pexels' | 'pixabay' | 'ai_generated'; // Union type for all possible providers
  aiVideoProvider?: string; // For AI video generation when mediaType='video' and provider='ai_generated'
  aiImageProvider?: string; // For AI image generation when mediaType='image' and provider='ai_generated'
  searchSafety: string;
  quality?: string;
  searchTermsPerScene?: number;
  // AI-specific settings
  guidanceScale?: number;
  inferenceSteps?: number;
  showAdvancedSettings?: boolean;
  
  onMediaTypeChange: (type: 'video' | 'image') => void; // eslint-disable-line
  onProviderChange: (provider: 'pexels' | 'pixabay' | 'ai_generated') => void; // eslint-disable-line
  onAiVideoProviderChange?: (provider: string) => void; // eslint-disable-line
  onAiImageProviderChange?: (provider: string) => void; // eslint-disable-line
  onSearchSafetyChange: (safety: string) => void; // eslint-disable-line
  onQualityChange?: (quality: string) => void; // eslint-disable-line
  onSearchTermsPerSceneChange?: (terms: number) => void; // eslint-disable-line
  onGuidanceScaleChange?: (value: number) => void; // eslint-disable-line
  onInferenceStepsChange?: (value: number) => void; // eslint-disable-line
}

const UnifiedMediaProviderSettings: React.FC<UnifiedMediaProviderSettingsProps> = ({
  mediaType,
  provider,
  aiVideoProvider,
  aiImageProvider,
  searchSafety,
  quality,
  searchTermsPerScene,
  guidanceScale = 3.5,
  inferenceSteps = 4,
  showAdvancedSettings = true,
  onMediaTypeChange,
  onProviderChange,
  onAiVideoProviderChange,
  onAiImageProviderChange,
  onSearchSafetyChange,
  onQualityChange,
  onSearchTermsPerSceneChange,
  onGuidanceScaleChange,
  onInferenceStepsChange,
}) => {
  const isAiGenerated = provider === 'ai_generated';
  const isAiImageProvider = mediaType === 'image' && isAiGenerated;
  const currentAiImageProvider = aiImageProvider || 'together';

  const getProviderIcon = (providerValue: string) => {
    switch (providerValue) {
      case 'pexels': return '📹';
      case 'pixabay': return '🎬';
      case 'ai_generated': return '🤖';
      default: return '📺';
    }
  };

  const getAiVideoProviderIcon = (providerValue: string) => {
    switch (providerValue) {
      case 'ltx_video': return '⚡';
      case 'wavespeed': return '🌊';
      case 'comfyui': return '🎛️';
      default: return '🤖';
    }
  };

  const getAiImageProviderIcon = (providerValue: string) => {
    switch (providerValue) {
      case 'together': return '🤖';
      case 'flux': return '⚡';
      case 'pollinations': return '🌸';
      default: return '📸';
    }
  };

  const getSafetyIcon = (level: string) => {
    switch (level) {
      case 'strict': return '🔒';
      case 'moderate': return '⚖️';
      case 'off': return '🔓';
      default: return '🔒';
    }
  };

  const getQualityIcon = (qualityValue: string) => {
    switch (qualityValue) {
      case 'standard': return '📺';
      case 'high': return '🎥';
      case 'ultra': return '💎';
      default: return '🎥';
    }
  };

  const getProviderCategoryColor = (providerValue: string) => {
    switch (providerValue) {
      case 'ai_generated': return '#2563eb';
      case 'pexels': return '#05A081';
      case 'pixabay': return '#2ec66d';
      default: return '#6b7280';
    }
  };

  // Get available providers based on selected media type
  const getAvailableProviders = (): UnifiedProvider[] => {
    const stockProviders = STOCK_PROVIDERS.filter(p => p.supportedTypes.includes(mediaType));
    
    const aiProvider: AIProvider = {
      value: 'ai_generated',
      label: `AI Generated ${mediaType === 'video' ? 'Videos' : 'Images'}`,
      supportedTypes: ['video', 'image'],
      description: `Custom ${mediaType}s generated from text prompts using AI models`,
      features: ['Custom Content', 'Unlimited Variety', 'Text-to-Media']
    };
    
    return [...stockProviders, aiProvider];
  };

  const availableProviders = getAvailableProviders();

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Typography 
        variant="h6" 
        sx={{ 
          mb: 3, 
          display: 'flex', 
          alignItems: 'center', 
          gap: 1,
          fontSize: { xs: '1.1rem', sm: '1.25rem' },
          flexWrap: 'wrap'
        }}
      >
        {mediaType === 'video' ? 
          <VideoIcon color="primary" sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }} /> : 
          <ImageIcon color="primary" sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }} />
        }
        <span>Unified Media Provider Settings</span>
      </Typography>

      <Typography 
        variant="body2" 
        color="text.secondary" 
        sx={{ 
          mb: 3,
          fontSize: { xs: '0.8rem', sm: '0.875rem' },
          lineHeight: 1.5
        }}
      >
        Choose your media source and type. Stock providers (Pexels, Pixabay) offer real content from creators, 
        while AI generation creates custom {mediaType}s from text prompts.
      </Typography>

      <Grid container spacing={{ xs: 2, sm: 3 }}>
        {/* Media Type Selection */}
        <Grid item xs={12} sm={6} md={3}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Media Type</InputLabel>
            <Select
              value={mediaType}
              onChange={(e) => onMediaTypeChange(e.target.value as 'video' | 'image')}
              label="Media Type"
            >
              {MEDIA_TYPES.map((type) => (
                <MenuItem key={type.value} value={type.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '1.2em' }}>{type.icon}</span>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontWeight: 500,
                        fontSize: { xs: '0.9rem', sm: '0.875rem' }
                      }}
                    >
                      {type.label}
                    </Typography>
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Provider Selection */}
        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Provider</InputLabel>
            <Select
              value={provider}
              onChange={(e) => onProviderChange(e.target.value as 'pexels' | 'pixabay' | 'ai_generated')}
              label="Provider"
            >
              {availableProviders.map((prov) => (
                <MenuItem key={prov.value} value={prov.value}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                    <span style={{ fontSize: '1.2em' }}>{getProviderIcon(prov.value)}</span>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {prov.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                        {prov.description}
                      </Typography>
                    </Box>
                    {prov.value === 'pexels' && <Chip label="Recommended" size="small" variant="outlined" />}
                    {prov.value === 'ai_generated' && <Chip label="Custom" size="small" variant="outlined" />}
                    <Chip
                      size="small"
                      label={prov.value === 'ai_generated' ? 'AI' : 'Stock'}
                      sx={{ 
                        backgroundColor: getProviderCategoryColor(prov.value),
                        color: 'white',
                        fontSize: '0.7rem'
                      }}
                    />
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* AI Sub-Provider Selection */}
        {isAiGenerated && (
          <Grid item xs={12} md={5}>
            {mediaType === 'video' && aiVideoProvider !== undefined && onAiVideoProviderChange ? (
              <FormControl fullWidth variant="outlined">
                <InputLabel>AI Video Provider</InputLabel>
                <Select
                  value={aiVideoProvider || 'wavespeed'}
                  onChange={(e) => onAiVideoProviderChange(e.target.value)}
                  label="AI Video Provider"
                >
                  {AI_VIDEO_PROVIDERS.map((aiProvider) => (
                    <MenuItem key={aiProvider} value={aiProvider}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>{getAiVideoProviderIcon(aiProvider)}</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {aiProvider === 'ltx_video' ? 'LTX-Video' : 
                             aiProvider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {aiProvider === 'ltx_video' && 'High-quality video generation with fine control'}
                            {aiProvider === 'wavespeed' && 'Fast video generation with multiple models'}
                            {aiProvider === 'comfyui' && 'Custom video generation using ComfyUI workflows'}
                          </Typography>
                        </Box>
                        {aiProvider === 'ltx_video' && <Chip label="High Quality" size="small" variant="outlined" />}
                        {aiProvider === 'wavespeed' && <Chip label="Fast" size="small" variant="outlined" />}
                        {aiProvider === 'comfyui' && <Chip label="Custom" size="small" variant="outlined" />}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : mediaType === 'image' && onAiImageProviderChange ? (
              <FormControl fullWidth variant="outlined">
                <InputLabel>AI Image Provider</InputLabel>
                <Select
                  value={currentAiImageProvider}
                  onChange={(e) => onAiImageProviderChange(e.target.value)}
                  label="AI Image Provider"
                >
                  {AI_IMAGE_PROVIDERS.map((aiProvider) => (
                    <MenuItem key={aiProvider.value} value={aiProvider.value}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>{getAiImageProviderIcon(aiProvider.value)}</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {aiProvider.label}
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            {aiProvider.description}
                          </Typography>
                        </Box>
                        {aiProvider.value === 'together' && <Chip label="Recommended" size="small" variant="outlined" />}
                      </Box>
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            ) : null}
          </Grid>
        )}

        {/* Content Safety */}
        <Grid item xs={12} md={isAiGenerated ? 4 : 4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel>Content Safety</InputLabel>
            <Select
              value={searchSafety}
              onChange={(e) => onSearchSafetyChange(e.target.value)}
              label="Content Safety"
            >
              {SEARCH_SAFETY_LEVELS.map((level) => (
                <MenuItem key={level} value={level}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <span style={{ fontSize: '1.2em' }}>{getSafetyIcon(level)}</span>
                    <SafetyIcon fontSize="small" />
                    <Box sx={{ flexGrow: 1 }}>
                      {level.charAt(0).toUpperCase() + level.slice(1)}
                    </Box>
                    {level === 'moderate' && <Chip label="Recommended" size="small" variant="outlined" />}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* Quality Settings */}
        {quality !== undefined && onQualityChange && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>{mediaType === 'video' ? 'Video' : 'Image'} Quality</InputLabel>
              <Select
                value={quality || 'high'}
                onChange={(e) => onQualityChange(e.target.value)}
                label={`${mediaType === 'video' ? 'Video' : 'Image'} Quality`}
              >
                {FOOTAGE_QUALITIES.map((qual) => (
                  <MenuItem key={qual} value={qual}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span style={{ fontSize: '1.2em' }}>{getQualityIcon(qual)}</span>
                      <QualityIcon fontSize="small" />
                      <Box sx={{ flexGrow: 1 }}>
                        {qual.charAt(0).toUpperCase() + qual.slice(1)} Quality
                      </Box>
                      {qual === 'high' && <Chip label="Recommended" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* Search Terms Per Scene */}
        {searchTermsPerScene !== undefined && onSearchTermsPerSceneChange && !isAiGenerated && (
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>Search Terms Per Scene</InputLabel>
              <Select
                value={searchTermsPerScene || 3}
                onChange={(e) => onSearchTermsPerSceneChange(Number(e.target.value))}
                label="Search Terms Per Scene"
              >
                {[1, 2, 3, 4, 5].map((count) => (
                  <MenuItem key={count} value={count}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <span>{count} search term{count !== 1 ? 's' : ''}</span>
                      {count === 3 && <Chip label="Optimal" size="small" variant="outlined" />}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
        )}

        {/* AI Advanced Settings */}
        {isAiImageProvider && showAdvancedSettings && (
          <>
            {/* Guidance Scale for Flux */}
            {currentAiImageProvider === 'flux' && guidanceScale !== undefined && onGuidanceScaleChange && (
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>
                  <AIIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Guidance Scale: {guidanceScale}
                </Typography>
                <Slider
                  value={guidanceScale}
                  onChange={(_, value) => onGuidanceScaleChange(Array.isArray(value) ? value[0] : value)}
                  min={1.0}
                  max={20.0}
                  step={0.1}
                  marks={[
                    { value: 1.0, label: 'Creative' },
                    { value: 3.5, label: 'Balanced' },
                    { value: 7.0, label: 'Precise' },
                    { value: 15.0, label: 'Strict' }
                  ]}
                />
              </Grid>
            )}

            {/* Inference Steps */}
            {inferenceSteps !== undefined && onInferenceStepsChange && (
              <Grid item xs={12} md={6}>
                <Typography gutterBottom>
                  <AIIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Inference Steps: {inferenceSteps}
                </Typography>
                <Slider
                  value={inferenceSteps}
                  onChange={(_, value) => onInferenceStepsChange(Array.isArray(value) ? value[0] : value)}
                  min={1}
                  max={currentAiImageProvider === 'together' ? 12 : 50}
                  step={1}
                  marks={
                    currentAiImageProvider === 'together' ? [
                      { value: 1, label: '1 (Fast)' },
                      { value: 4, label: '4 (Default)' },
                      { value: 8, label: '8 (Quality)' },
                      { value: 12, label: '12 (Ultra)' }
                    ] : [
                      { value: 1, label: '1 (Fast)' },
                      { value: 4, label: '4 (Default)' },
                      { value: 20, label: '20 (Quality)' },
                      { value: 50, label: '50 (Ultra)' }
                    ]
                  }
                />
              </Grid>
            )}
          </>
        )}

        {/* Current Configuration Summary */}
        <Grid item xs={12}>
          <Box sx={{ 
            p: 2, 
            backgroundColor: '#f8f9fa', 
            borderRadius: 1,
            border: '1px solid #e2e8f0'
          }}>
            <Typography variant="subtitle2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
              {mediaType === 'video' ? <VideoIcon /> : <ImageIcon />}
              Current Media Configuration
            </Typography>
            
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 1 }}>
              <Chip 
                label={`${mediaType.charAt(0).toUpperCase() + mediaType.slice(1)}s`}
                size="small" 
                color="primary"
              />
              <Chip 
                label={provider === 'ai_generated' ? 'AI Generated' : 
                       provider.charAt(0).toUpperCase() + provider.slice(1)}
                size="small" 
                sx={{ 
                  backgroundColor: getProviderCategoryColor(provider),
                  color: 'white'
                }}
              />
              {isAiGenerated && mediaType === 'video' && aiVideoProvider && (
                <Chip
                  label={aiVideoProvider === 'ltx_video' ? 'LTX-Video' : 
                         aiVideoProvider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}
                  size="small"
                  variant="outlined"
                />
              )}
              {isAiGenerated && mediaType === 'image' && aiImageProvider && (
                <Chip
                  label={AI_IMAGE_PROVIDERS.find(p => p.value === aiImageProvider)?.label || aiImageProvider}
                  size="small"
                  variant="outlined"
                />
              )}
              <Chip 
                label={`${searchSafety.charAt(0).toUpperCase() + searchSafety.slice(1)} Safety`}
                size="small" 
                variant="outlined"
              />
              {quality && (
                <Chip 
                  label={`${quality.charAt(0).toUpperCase() + quality.slice(1)} Quality`}
                  size="small" 
                  variant="outlined"
                />
              )}
              {searchTermsPerScene && !isAiGenerated && (
                <Chip
                  label={`${searchTermsPerScene} search terms`}
                  size="small"
                  variant="outlined"
                />
              )}
            </Box>
            
            <Typography variant="body2" color="text.secondary">
              {mediaType === 'video' ? 'Videos' : 'Images'} will be sourced from{' '}
              {provider === 'ai_generated' ? 'AI generation' : provider}
              {isAiGenerated && mediaType === 'video' && aiVideoProvider && ` using ${aiVideoProvider === 'ltx_video' ? 'LTX-Video' : aiVideoProvider === 'wavespeed' ? 'WaveSpeed AI' : 'ComfyUI'}`}
              {isAiGenerated && mediaType === 'image' && aiImageProvider && ` using ${AI_IMAGE_PROVIDERS.find(p => p.value === aiImageProvider)?.label || aiImageProvider}`}
              {' '}with {searchSafety} content filtering
              {quality && ` in ${quality} quality`}
              {searchTermsPerScene && !isAiGenerated && `. Using ${searchTermsPerScene} search terms per scene for better relevance.`}
            </Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Provider Tips */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f0f9ff', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          🎬 <strong>Available:</strong> {availableProviders.length} providers for {mediaType}s
          • Current: {provider === 'ai_generated' ? 'AI Generated' : provider.charAt(0).toUpperCase() + provider.slice(1)} with {searchSafety} safety
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
          💡 <strong>Tip:</strong> Pexels offers the best variety and quality for stock content. 
          AI Generated creates unique {mediaType}s but requires more processing time. 
          Use "moderate" safety for balanced results.
        </Typography>
      </Box>
    </Box>
  );
};

export default UnifiedMediaProviderSettings;