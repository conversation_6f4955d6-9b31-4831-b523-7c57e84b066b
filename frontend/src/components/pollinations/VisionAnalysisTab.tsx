import React, { useState } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  CircularProgress,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip
} from '@mui/material';
import {
  Visibility as VisionIcon,
  Upload as UploadIcon
} from '@mui/icons-material';
import { pollinationsApi } from '../../utils/api';
import { VisionAnalysisParams, ModelOption, ExamplePrompts } from '../../types/pollinations';

interface VisionAnalysisTabProps {
  visionForm: VisionAnalysisParams;
  // eslint-disable-next-line no-unused-vars
  setVisionForm: (form: VisionAnalysisParams) => void;
  textModels: ModelOption[];
  loadingModels: boolean;
  modelError: string | null;
  loading: boolean;
  // eslint-disable-next-line no-unused-vars
  onSubmit: (jobId: string) => void;
  // eslint-disable-next-line no-unused-vars
  onError: (error: string) => void;
  examplePrompts: ExamplePrompts;
}

const VisionAnalysisTab: React.FC<VisionAnalysisTabProps> = ({
  visionForm,
  setVisionForm,
  textModels,
  loadingModels,
  modelError,
  loading,
  onSubmit,
  onError,
  examplePrompts
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (!file.type.startsWith('image/')) {
        onError('Please select a valid image file');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) {
        onError('File size must be less than 10MB');
        return;
      }
      
      setSelectedFile(file);
      
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async () => {
    if (!visionForm.image_url?.trim() && !selectedFile) {
      onError('Please provide an image URL or upload an image file');
      return;
    }

    if (!visionForm.question.trim()) {
      onError('Please provide a question about the image');
      return;
    }

    try {
      let response;
      
      if (selectedFile) {
        response = await pollinationsApi.analyzeUploadedImage(
          selectedFile,
          visionForm.question,
          visionForm.model
        );
      } else {
        response = await pollinationsApi.analyzeImage({
          image_url: visionForm.image_url || '',
          question: visionForm.question,
          model: visionForm.model
        });
      }

      if (response.success && response.data) {
        onSubmit(response.data.job_id);
      } else {
        onError(response.error || 'Failed to analyze image');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      onError(errorMessage);
    }
  };

  return (
    <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <VisionIcon color="primary" />
                  AI Vision Analysis
                </Typography>

                <Grid container spacing={3}>
                  {/* Image Input Section */}
                  <Grid item xs={12} md={6}>
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600 }}>
                        Image Source
                      </Typography>
                      
                      <TextField
                        fullWidth
                        label="Image URL"
                        placeholder="https://example.com/image.jpg"
                        value={visionForm.image_url}
                        onChange={(e) => setVisionForm({ ...visionForm, image_url: e.target.value })}
                        sx={{ mb: 2 }}
                        helperText="Or upload a file below"
                      />

                      <input
                        accept="image/*"
                        style={{ display: 'none' }}
                        id="vision-image-upload"
                        type="file"
                        onChange={handleFileChange}
                      />
                      <label htmlFor="vision-image-upload">
                        <Button
                          variant="outlined"
                          component="span"
                          startIcon={<UploadIcon />}
                          fullWidth
                          sx={{ 
                            py: 2,
                            borderStyle: 'dashed',
                            borderWidth: 2,
                            borderColor: selectedFile ? 'success.main' : 'grey.300'
                          }}
                        >
                          {selectedFile ? `Selected: ${selectedFile.name}` : 'Upload Image File'}
                        </Button>
                      </label>
                    </Box>

                    {/* Image Preview */}
                    {previewUrl && (
                      <Box sx={{ mb: 3 }}>
                        <Typography variant="subtitle2" sx={{ mb: 1 }}>Preview:</Typography>
                        <Paper sx={{ p: 2, textAlign: 'center', bgcolor: '#f8fafc' }}>
                          <img
                            src={previewUrl}
                            alt="Preview"
                            style={{
                              maxWidth: '100%',
                              maxHeight: '200px',
                              borderRadius: '8px',
                              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                            }}
                          />
                        </Paper>
                      </Box>
                    )}
                  </Grid>

                  {/* Analysis Parameters */}
                  <Grid item xs={12} md={6}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          multiline
                          rows={3}
                          label="Question about the image"
                          placeholder="What would you like to know about this image?"
                          value={visionForm.question}
                          onChange={(e) => setVisionForm({ ...visionForm, question: e.target.value })}
                          helperText="Be specific about what you want to analyze"
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <FormControl fullWidth>
                          <InputLabel>AI Model</InputLabel>
                          <Select
                            value={visionForm.model}
                            label="AI Model"
                            onChange={(e) => setVisionForm({ ...visionForm, model: e.target.value })}
                            disabled={loadingModels}
                          >
                            {loadingModels ? (
                              <MenuItem disabled>Loading models...</MenuItem>
                            ) : textModels.length > 0 ? (
                              textModels.map((model) => (
                                <MenuItem key={model.name} value={model.name}>
                                  {model.label} {model.name === 'openai' ? '(Vision)' : model.name === 'anthropic' ? '(Vision)' : model.name === 'mistral' ? '(Vision)' : ''}
                                </MenuItem>
                              ))
                            ) : (
                              <MenuItem disabled>No models available</MenuItem>
                            )}
                          </Select>
                          {modelError && (
                            <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                              {modelError}
                            </Typography>
                          )}
                        </FormControl>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <VisionIcon />}
                  onClick={handleSubmit}
                  disabled={loading || (!visionForm.image_url?.trim() && !selectedFile) || !visionForm.question.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Analyzing...' : 'Analyze Image'}
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Example Questions
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {examplePrompts.vision.map((prompt, index) => (
                    <Chip
                      key={index}
                      label={prompt}
                      onClick={() => setVisionForm({ ...visionForm, question: prompt })}
                      sx={{ 
                        justifyContent: 'flex-start',
                        height: 'auto',
                        cursor: 'pointer',
                        '& .MuiChip-label': {
                          display: 'block',
                          whiteSpace: 'normal',
                          textAlign: 'left',
                          padding: '8px 12px'
                        }
                      }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default VisionAnalysisTab;