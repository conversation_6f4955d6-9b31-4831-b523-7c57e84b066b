import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Alert,
  CircularProgress,
  Paper,
  LinearProgress,
  Grid,
  Button
} from '@mui/material';
import { VideoLibrary as LibraryIcon } from '@mui/icons-material';
import { JobResult, JobStatus } from '../../types/pollinations';

interface ResultsDisplayProps {
  result: JobResult | null;
  error: string | null;
  jobStatus: JobStatus;
  jobProgress: string;
  pollingJobId: string | null;
  activeTab: number;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({
  result,
  error,
  jobStatus,
  jobProgress,
  pollingJobId,
  activeTab
}) => {
  const navigate = useNavigate();

  if (!result && !error && !jobStatus) {
    return null;
  }

  const getTabTitle = () => {
    switch (activeTab) {
      case 0: return '👁️ Vision Analysis Result';
      case 1: return '📝 Text Generation Result';
      case 2: return '💬 Chat Completion Result';
      default: return 'AI Result';
    }
  };

  const getResultLabel = () => {
    switch (activeTab) {
      case 0: return 'Analysis Result:';
      case 1: return 'Generated Text:';
      case 2: return 'Assistant Response:';
      default: return 'Result:';
    }
  };

  return (
    <Box sx={{ mt: 4, mb: 6 }}>
      <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
        <CardContent sx={{ p: 4, pb: 5 }}>
          <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {getTabTitle()}
            {jobStatus && jobStatus !== 'completed' && (
              <CircularProgress size={20} sx={{ ml: 1 }} />
            )}
          </Typography>
          
          {/* Job Status */}
          {jobStatus && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                Job ID: {pollingJobId}
              </Typography>
              <LinearProgress 
                variant={jobStatus === 'completed' ? 'determinate' : 'indeterminate'}
                value={jobStatus === 'completed' ? 100 : undefined}
                sx={{ mb: 1, height: 6, borderRadius: 3 }}
              />
              <Typography variant="body2" sx={{ 
                color: jobStatus === 'completed' ? 'success.main' : 
                       jobStatus === 'failed' ? 'error.main' : 'info.main'
              }}>
                {jobProgress}
              </Typography>
            </Box>
          )}
          
          {/* Error Display */}
          {error && (
            <Alert
              severity="error"
              sx={{
                mb: 2,
                '& .MuiAlert-message': {
                  width: '100%'
                }
              }}
            >
              <Box>
                <Typography variant="body1" sx={{ mb: 1 }}>
                  {error}
                </Typography>
                
                {/* Show helpful suggestions based on error type */}
                {(error.includes('502') || error.includes('Bad Gateway') || error.includes('server issues')) && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> This is a temporary server issue. Wait 1-2 minutes and try again.
                  </Typography>
                )}
                
                {(error.includes('503') || error.includes('temporarily unavailable') || error.includes('circuit breaker')) && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> The service is experiencing high demand. Try again in 3-5 minutes or use the sync mode for faster responses.
                  </Typography>
                )}
                
                {(error.includes('timeout') || error.includes('timed out')) && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> The API is under heavy load. Try again with a shorter prompt or enable sync mode.
                  </Typography>
                )}
                
                {error.includes('rate limit') && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> You've reached the rate limit. Wait 60 seconds before trying again.
                  </Typography>
                )}
                
                {error.includes('network') && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> Check your internet connection and try again.
                  </Typography>
                )}
                
                {error.includes('retry_exhausted') && (
                  <Typography variant="body2" sx={{ color: 'error.dark', opacity: 0.8 }}>
                    💡 <strong>What to do:</strong> The request failed multiple times. Wait 5-10 minutes for the service to stabilize, then try again.
                  </Typography>
                )}
              </Box>
            </Alert>
          )}
          
          {/* Success Results */}
          {result && jobStatus === 'completed' && result.result && (
            <Box>
              <Alert severity="success" sx={{ mb: 2 }}>
                🎉 AI processing completed successfully!
              </Alert>
              
              {/* Text Results */}
              {(result.result.text || result.result.assistant_message) && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 1 }}>
                    {getResultLabel()}
                  </Typography>
                  <Paper sx={{ p: 3, bgcolor: '#f8fafc', maxHeight: 400, overflow: 'auto' }}>
                    <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                      {result.result.text || result.result.assistant_message}
                    </Typography>
                  </Paper>
                  <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => navigator.clipboard.writeText(
                        result.result?.text || result.result?.assistant_message || ''
                      )}
                    >
                      Copy Text
                    </Button>
                    <Button
                      startIcon={<LibraryIcon />}
                      onClick={() => navigate('/dashboard/library')}
                      variant="outlined"
                      size="small"
                      color="primary"
                    >
                      View in Library
                    </Button>
                  </Box>
                </Box>
              )}
              
              {/* Processing Details */}
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>📊 Processing Details:</Typography>
                <Paper sx={{ p: 2, bgcolor: '#f8fafc' }}>
                  <Grid container spacing={2} sx={{ fontSize: '0.875rem' }}>
                    {result.result.model_used && (
                      <Grid item xs={12} md={6}>
                        <strong>Model:</strong> {result.result.model_used}
                      </Grid>
                    )}
                    {result.result.generation_time && (
                      <Grid item xs={12} md={6}>
                        <strong>Processing Time:</strong> {result.result.generation_time.toFixed(1)}s
                      </Grid>
                    )}
                    {result.result.character_count && (
                      <Grid item xs={12} md={6}>
                        <strong>Characters:</strong> {result.result.character_count}
                      </Grid>
                    )}
                    {result.result.message_count && (
                      <Grid item xs={12} md={6}>
                        <strong>Messages:</strong> {result.result.message_count}
                      </Grid>
                    )}
                    {result.result.question && (
                      <Grid item xs={12}>
                        <strong>Question:</strong> {result.result.question}
                      </Grid>
                    )}
                    {result.result.prompt && (
                      <Grid item xs={12}>
                        <strong>Prompt:</strong> {result.result.prompt}
                      </Grid>
                    )}
                  </Grid>
                </Paper>
              </Box>
            </Box>
          )}
          
          {/* Initial Job Created Message */}
          {result && !result.result && jobStatus !== 'completed' && (
            <Box>
              <Alert severity="info" sx={{ mb: 2 }}>
                AI processing job created successfully!
              </Alert>
              <Typography variant="body2" color="text.secondary">
                Job ID: <code style={{ padding: '2px 4px', backgroundColor: '#f1f3f4', borderRadius: '3px' }}>
                  {result.job_id}
                </code>
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default ResultsDisplay;