import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>rid,
  Card,
  CardContent,
  TextField,
  Button,
  CircularProgress,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Chip,
  Switch,
  FormControlLabel
} from '@mui/material';
import { TextFields as TextIcon } from '@mui/icons-material';
import { pollinationsApi } from '../../utils/api';
import { 
  TextGenerationParams, 
  ModelOption, 
  ExamplePrompts,
  TextGenerationResponse 
} from '../../types/pollinations';

interface TextGenerationTabProps {
  textForm: TextGenerationParams;
  // eslint-disable-next-line no-unused-vars
  setTextForm: (form: TextGenerationParams) => void;
  textModels: ModelOption[];
  loadingModels: boolean;
  modelError: string | null;
  loading: boolean;
  useSync: boolean;
  // eslint-disable-next-line no-unused-vars
  setUseSync: (sync: boolean) => void;
  // eslint-disable-next-line no-unused-vars
  onSubmit: (jobId: string) => void;
  // eslint-disable-next-line no-unused-vars
  onSyncResult: (result: {
    job_id: string;
    result: {
      text: string;
      response: unknown;
      model_used: string;
      generation_time: number;
      prompt: string;
      character_count: number;
    };
    status: string;
  }) => void;
  // eslint-disable-next-line no-unused-vars
  onError: (error: string) => void;
  examplePrompts: ExamplePrompts;
}

const TextGenerationTab: React.FC<TextGenerationTabProps> = ({
  textForm,
  setTextForm,
  textModels,
  loadingModels,
  modelError,
  loading,
  useSync,
  setUseSync,
  onSubmit,
  onSyncResult,
  onError,
  examplePrompts
}) => {
  const handleSubmit = async () => {
    if (!textForm.prompt.trim()) {
      onError('Text prompt is required');
      return;
    }

    try {
      let response;
      
      if (useSync) {
        // Use synchronous endpoint for quick responses
        response = await pollinationsApi.generateTextSync({
          prompt: textForm.prompt,
          model: textForm.model,
          seed: textForm.seed,
          temperature: textForm.temperature,
          top_p: textForm.top_p,
          presence_penalty: textForm.presence_penalty,
          frequency_penalty: textForm.frequency_penalty,
          system: textForm.system,
          json_mode: textForm.json_mode
        });
        
        if (response.success && response.data) {
          // Transform response data to match expected structure
          const textResponse = response.data as TextGenerationResponse;
          const transformedResult = {
            job_id: 'sync',
            result: {
              text: textResponse.text || textResponse.content || '',
              response: response.data,
              model_used: textForm.model,
              generation_time: textResponse._metadata?.generation_time || textResponse.generation_time || 0,
              prompt: textForm.prompt,
              character_count: (textResponse.text || textResponse.content || '').length
            },
            status: 'completed'
          };
          onSyncResult(transformedResult);
        } else {
          // Enhanced error handling for different error types
          const errorMessage = response.error || 'Failed to generate text';
          if (errorMessage.includes('503') || errorMessage.includes('service temporarily unavailable') || errorMessage.includes('circuit breaker')) {
            onError('🔧 The text generation service is temporarily unavailable due to high demand or maintenance. Please try again in a few minutes.');
          } else if (errorMessage.includes('502') || errorMessage.includes('Bad Gateway') || errorMessage.includes('server issues')) {
            onError('🌐 The Pollinations API is experiencing temporary server issues. This usually resolves quickly - please try again in a moment.');
          } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
            onError('⏰ Rate limit exceeded. Please wait a moment before trying again.');
          } else if (errorMessage.includes('timeout') || errorMessage.includes('Request timeout')) {
            onError('⏱️ The request timed out. The API may be experiencing high load - please try again.');
          } else if (errorMessage.includes('connection') || errorMessage.includes('Connection error')) {
            onError('🔌 Connection failed. Please check your internet connection and try again.');
          } else if (errorMessage.includes('400') || errorMessage.includes('Invalid request')) {
            onError('❌ Invalid request. Please check your input parameters and try again.');
          } else if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
            onError('🔑 Authentication failed. Please check your API credentials.');
          } else if (errorMessage.includes('500')) {
            onError('🚫 Server error: The text generation service is temporarily unavailable. Please try again.');
          } else {
            onError(`⚠️ ${errorMessage}`);
          }
        }
      } else {
        // Use async endpoint
        response = await pollinationsApi.generateText({
          prompt: textForm.prompt,
          model: textForm.model,
          seed: textForm.seed,
          temperature: textForm.temperature,
          top_p: textForm.top_p,
          presence_penalty: textForm.presence_penalty,
          frequency_penalty: textForm.frequency_penalty,
          system: textForm.system,
          json_mode: textForm.json_mode
        });

        if (response.success && response.data) {
          onSubmit(response.data.job_id);
        } else {
          // Enhanced error handling for different error types
          const errorMessage = response.error || 'Failed to generate text';
          if (errorMessage.includes('503') || errorMessage.includes('service temporarily unavailable') || errorMessage.includes('circuit breaker'))  {
            onError('🔧 The text generation service is temporarily unavailable due to high demand or maintenance. Please try again in a few minutes.');
          } else if (errorMessage.includes('502') || errorMessage.includes('Bad Gateway') || errorMessage.includes('server issues')) {
            onError('🌐 The Pollinations API is experiencing temporary server issues. This usually resolves quickly - please try again in a moment.');
          } else if (errorMessage.includes('429') || errorMessage.includes('rate limit')) {
            onError('⏰ Rate limit exceeded. Please wait a moment before trying again.');
          } else if (errorMessage.includes('timeout') || errorMessage.includes('Request timeout')) {
            onError('⏱️ The request timed out. The API may be experiencing high load - please try again.');
          } else if (errorMessage.includes('connection') || errorMessage.includes('Connection error')) {
            onError('🔌 Connection failed. Please check your internet connection and try again.');
          } else if (errorMessage.includes('400') || errorMessage.includes('Invalid request')) {
            onError('❌ Invalid request. Please check your input parameters and try again.');
          } else if (errorMessage.includes('401') || errorMessage.includes('unauthorized')) {
            onError('🔑 Authentication failed. Please check your API credentials.');
          } else if (errorMessage.includes('500')) {
            onError('🚫 Server error: The text generation service is temporarily unavailable. Please try again.');
          } else {
            onError(`⚠️ ${errorMessage}`);
          }
        }
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      
      // Handle network and other errors
      if (errorMessage.includes('NetworkError') || errorMessage.includes('Failed to fetch')) {
        onError('🔌 Network error: Please check your internet connection and try again.');
      } else if (errorMessage.includes('AbortError')) {
        onError('⏹️ Request was cancelled. Please try again.');
      } else if (errorMessage.includes('TimeoutError')) {
        onError('⏱️ Request timed out. Please try again.');
      } else {
        onError(`💥 Unexpected error: ${errorMessage}`);
      }
    }
  };

  return (
    <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TextIcon color="primary" />
                  AI Text Generation
                </Typography>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      multiline
                      rows={4}
                      label="Text Generation Prompt"
                      placeholder="Enter your prompt for text generation..."
                      value={textForm.prompt}
                      onChange={(e) => setTextForm({ ...textForm, prompt: e.target.value })}
                      helperText="Describe what kind of text you want to generate"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>AI Model</InputLabel>
                      <Select
                        value={textForm.model}
                        label="AI Model"
                        onChange={(e) => setTextForm({ ...textForm, model: e.target.value })}
                        disabled={loadingModels}
                      >
                        {loadingModels ? (
                          <MenuItem disabled>Loading models...</MenuItem>
                        ) : textModels.length > 0 ? (
                          textModels.map((model) => (
                            <MenuItem key={model.name} value={model.name}>
                              {model.label}
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem disabled>No models available</MenuItem>
                        )}
                      </Select>
                      {modelError && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {modelError}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="System Prompt (Optional)"
                      placeholder="You are a helpful assistant..."
                      value={textForm.system || ''}
                      onChange={(e) => setTextForm({ ...textForm, system: e.target.value })}
                      helperText="Define the AI's role and behavior"
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Temperature: {textForm.temperature}</Typography>
                    <Slider
                      value={textForm.temperature || 0.7}
                      onChange={(_e, value) => setTextForm({ 
                        ...textForm, 
                        temperature: Array.isArray(value) ? value[0] : value 
                      })}
                      min={0.0}
                      max={2.0}
                      step={0.1}
                      marks={[
                        { value: 0.0, label: 'Focused' },
                        { value: 0.7, label: 'Balanced' },
                        { value: 1.0, label: 'Creative' },
                        { value: 2.0, label: 'Chaotic' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Top P: {textForm.top_p}</Typography>
                    <Slider
                      value={textForm.top_p || 0.9}
                      onChange={(_e, value) => setTextForm({ 
                        ...textForm, 
                        top_p: Array.isArray(value) ? value[0] : value 
                      })}
                      min={0.0}
                      max={1.0}
                      step={0.1}
                      marks={[
                        { value: 0.1, label: 'Narrow' },
                        { value: 0.5, label: 'Medium' },
                        { value: 0.9, label: 'Wide' },
                        { value: 1.0, label: 'Full' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={textForm.json_mode}
                            onChange={(e) => setTextForm({ ...textForm, json_mode: e.target.checked })}
                          />
                        }
                        label="JSON Mode"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={useSync}
                            onChange={(e) => setUseSync(e.target.checked)}
                          />
                        }
                        label="Quick Response (Sync)"
                      />
                    </Box>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <TextIcon />}
                  onClick={handleSubmit}
                  disabled={loading || !textForm.prompt.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Generating...' : 'Generate Text'}
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Example Prompts
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {examplePrompts.text.map((prompt, index) => (
                    <Chip
                      key={index}
                      label={prompt.length > 50 ? prompt.substring(0, 50) + '...' : prompt}
                      onClick={() => setTextForm({ ...textForm, prompt })}
                      sx={{ 
                        justifyContent: 'flex-start',
                        height: 'auto',
                        cursor: 'pointer',
                        '& .MuiChip-label': {
                          display: 'block',
                          whiteSpace: 'normal',
                          textAlign: 'left',
                          padding: '8px 12px'
                        }
                      }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default TextGenerationTab;