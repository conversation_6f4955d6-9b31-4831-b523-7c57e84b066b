import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  CircularProgress,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Chip,
  Switch,
  FormControlLabel,
  IconButton
} from '@mui/material';
import {
  Chat as ChatIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { pollinationsApi } from '../../utils/api';
import {
  ChatCompletionParams,
  ModelOption,
  ExamplePrompts,
  ChatCompletionResponse,
  JobResult
} from '../../types/pollinations';

interface ChatCompletionTabProps {
  chatForm: ChatCompletionParams;
  // eslint-disable-next-line no-unused-vars
  setChatForm: (form: ChatCompletionParams | ((prev: ChatCompletionParams) => ChatCompletionParams)) => void;
  textModels: ModelOption[];
  loadingModels: boolean;
  modelError: string | null;
  loading: boolean;
  useSync: boolean;
  setUseSync: (sync: boolean) => void; // eslint-disable-line no-unused-vars
  onSubmit: (jobId: string) => void; // eslint-disable-line no-unused-vars
  onSyncResult: (result: JobResult) => void; // eslint-disable-line no-unused-vars
  onError: (error: string) => void; // eslint-disable-line no-unused-vars
  examplePrompts: ExamplePrompts;
}

const ChatCompletionTab: React.FC<ChatCompletionTabProps> = ({
  chatForm,
  setChatForm,
  textModels,
  loadingModels,
  modelError,
  loading,
  useSync,
  setUseSync,
  onSubmit,
  onSyncResult,
  onError,
  examplePrompts
}) => {
  const addChatMessage = () => {
    setChatForm((prev: ChatCompletionParams) => ({
      ...prev,
      messages: [...prev.messages, { role: 'user', content: '' }]
    }));
  };

  const updateChatMessage = (index: number, content: string) => {
    setChatForm((prev: ChatCompletionParams) => ({
      ...prev,
      messages: prev.messages.map((msg, i) =>
        i === index ? { ...msg, content } : msg
      )
    }));
  };

  const removeChatMessage = (index: number) => {
    if (chatForm.messages.length > 1) {
      setChatForm((prev: ChatCompletionParams) => ({
        ...prev,
        messages: prev.messages.filter((_, i: number) => i !== index)
      }));
    }
  };

  const handleSubmit = async () => {
    if (!chatForm.messages[0]?.content.trim()) {
      onError('Chat message is required');
      return;
    }

    try {
      let response;
      
      if (useSync) {
        // Use synchronous endpoint for quick responses
        response = await pollinationsApi.createChatCompletionSync({
          messages: chatForm.messages,
          model: chatForm.model,
          seed: chatForm.seed,
          temperature: chatForm.temperature,
          top_p: chatForm.top_p,
          presence_penalty: chatForm.presence_penalty,
          frequency_penalty: chatForm.frequency_penalty,
          json_mode: chatForm.json_mode
        });
        
        if (response.success && response.data) {
          const chatResponse = response.data as ChatCompletionResponse;
          const transformedResult = {
            job_id: 'sync',
            result: {
              response: response.data,
              assistant_message: chatResponse.choices?.[0]?.message?.content || '',
              model_used: chatForm.model,
              generation_time: chatResponse._metadata?.generation_time || 0,
              message_count: chatForm.messages.length,
              has_tool_calls: false
            },
            status: 'completed'
          };
          onSyncResult(transformedResult);
        } else {
          onError(response.error || 'Failed to generate chat response');
        }
      } else {
        // Use async endpoint
        response = await pollinationsApi.createChatCompletion({
          messages: chatForm.messages,
          model: chatForm.model,
          seed: chatForm.seed,
          temperature: chatForm.temperature,
          top_p: chatForm.top_p,
          presence_penalty: chatForm.presence_penalty,
          frequency_penalty: chatForm.frequency_penalty,
          json_mode: chatForm.json_mode
        });

        if (response.success && response.data) {
          onSubmit(response.data.job_id);
        } else {
          onError(response.error || 'Failed to generate chat response');
        }
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An error occurred';
      onError(errorMessage);
    }
  };

  return (
    <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 3 }}>
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0' }}>
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ChatIcon color="primary" />
                  AI Chat Completion
                </Typography>

                <Grid container spacing={3}>
                  {/* Chat Messages */}
                  <Grid item xs={12}>
                    <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                        Chat Messages ({chatForm.messages.length})
                      </Typography>
                      <Button
                        startIcon={<SendIcon />}
                        onClick={addChatMessage}
                        variant="outlined"
                        size="small"
                      >
                        Add Message
                      </Button>
                    </Box>

                    {chatForm.messages.map((message, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Grid container spacing={2} alignItems="center">
                          <Grid item xs={3}>
                            <FormControl fullWidth size="small">
                              <InputLabel>Role</InputLabel>
                              <Select
                                value={message.role}
                                label="Role"
                                onChange={(e) => {
                                  const newMessages = [...chatForm.messages];
                                  newMessages[index].role = e.target.value as 'user' | 'assistant' | 'system';
                                  setChatForm({ ...chatForm, messages: newMessages });
                                }}
                              >
                                <MenuItem value="user">User</MenuItem>
                                <MenuItem value="assistant">Assistant</MenuItem>
                                <MenuItem value="system">System</MenuItem>
                              </Select>
                            </FormControl>
                          </Grid>
                          <Grid item xs={8}>
                            <TextField
                              fullWidth
                              multiline
                              rows={2}
                              label="Message Content"
                              placeholder="Enter message content..."
                              value={message.content}
                              onChange={(e) => updateChatMessage(index, e.target.value)}
                              size="small"
                            />
                          </Grid>
                          <Grid item xs={1}>
                            {chatForm.messages.length > 1 && (
                              <IconButton
                                color="error"
                                onClick={() => removeChatMessage(index)}
                                size="small"
                              >
                                ×
                              </IconButton>
                            )}
                          </Grid>
                        </Grid>
                      </Box>
                    ))}
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>AI Model</InputLabel>
                      <Select
                        value={chatForm.model}
                        label="AI Model"
                        onChange={(e) => setChatForm({ ...chatForm, model: e.target.value })}
                        disabled={loadingModels}
                      >
                        {loadingModels ? (
                          <MenuItem disabled>Loading models...</MenuItem>
                        ) : textModels.length > 0 ? (
                          textModels.map((model) => (
                            <MenuItem key={model.name} value={model.name}>
                              {model.label}
                            </MenuItem>
                          ))
                        ) : (
                          <MenuItem disabled>No models available</MenuItem>
                        )}
                      </Select>
                      {modelError && (
                        <Typography variant="caption" color="error" sx={{ mt: 1 }}>
                          {modelError}
                        </Typography>
                      )}
                    </FormControl>
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Typography gutterBottom>Temperature: {chatForm.temperature}</Typography>
                    <Slider
                      value={chatForm.temperature || 0.7}
                      onChange={(_e, value) => setChatForm({ 
                        ...chatForm, 
                        temperature: Array.isArray(value) ? value[0] : value 
                      })}
                      min={0.0}
                      max={2.0}
                      step={0.1}
                      marks={[
                        { value: 0.0, label: 'Focused' },
                        { value: 0.7, label: 'Balanced' },
                        { value: 1.0, label: 'Creative' },
                        { value: 2.0, label: 'Chaotic' }
                      ]}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={chatForm.json_mode}
                            onChange={(e) => setChatForm({ ...chatForm, json_mode: e.target.checked })}
                          />
                        }
                        label="JSON Mode"
                      />
                      <FormControlLabel
                        control={
                          <Switch
                            checked={useSync}
                            onChange={(e) => setUseSync(e.target.checked)}
                          />
                        }
                        label="Quick Response (Sync)"
                      />
                    </Box>
                  </Grid>
                </Grid>

                <Button
                  variant="contained"
                  size="large"
                  startIcon={loading ? <CircularProgress size={20} /> : <ChatIcon />}
                  onClick={handleSubmit}
                  disabled={loading || !chatForm.messages[0]?.content.trim()}
                  sx={{ mt: 3, px: 4 }}
                >
                  {loading ? 'Processing...' : 'Send Chat'}
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card elevation={0} sx={{ border: '1px solid #e2e8f0', height: 'fit-content' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Example Chats
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {examplePrompts.chat.map((prompt, index) => (
                    <Chip
                      key={index}
                      label={prompt.length > 50 ? prompt.substring(0, 50) + '...' : prompt}
                      onClick={() => updateChatMessage(0, prompt)}
                      sx={{ 
                        justifyContent: 'flex-start',
                        height: 'auto',
                        cursor: 'pointer',
                        '& .MuiChip-label': {
                          display: 'block',
                          whiteSpace: 'normal',
                          textAlign: 'left',
                          padding: '8px 12px'
                        }
                      }}
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default ChatCompletionTab;