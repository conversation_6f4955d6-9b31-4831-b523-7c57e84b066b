// Content Creation Components
export { default as VideoCreatorTab } from './tabs/VideoCreatorTab';
export { default as TopicResearchTab } from './tabs/TopicResearchTab';
export { default as AutoTopicTab } from './tabs/AutoTopicTab';
export { default as VideoGeneratorTab } from './tabs/VideoGeneratorTab';
export { default as SceneEditor } from './SceneEditor';
export { default as ScriptEditor } from './ScriptEditor';
export { default as VoiceSelector } from '../settings/VoiceSelectorSettings';
export { default as MediaSettingsPanel } from '../settings/MediaSettings';
export { default as JobStatusDisplay } from './JobStatusDisplay';

// Settings Components
export { default as CaptionSettings } from '../settings/CaptionSettings';
export { default as ImageProviderSettingsPanel } from '../settings/ImageProviderSettings';

// Legacy components (deprecated - use MediaSettingsPanel instead)
// Note: These are the actual settings components

// Re-export types for convenience
export type {
  VideoScene,
  VideoCreationRequest,
  VideoCreationResult,
  TopicResearchRequest,
  TopicResearchResult,
  VoiceInfo,
  VoiceProvider,
  ContentCreationJobStatus,
  ContentCreationJobResult,
  VideoCreatorFormState,
  TopicResearchFormState,
  MediaSettings,
  VideoSettings,
  ImageProviderSettings,
  ContentCreationUIState,
} from '../../types/contentCreation';

// Re-export hooks for convenience
export {
  useVideoCreation,
  useTopicResearch,
  useVoices,
} from '../../hooks/useContentCreation';