import React from 'react';
import {
  <PERSON>,
  Typography,
  TextField,
  Button,
  IconButton,
  Grid,
  Chip,
  Slider,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as DragIcon,
  Timer as TimerIcon,
  Image as ImageIcon,
} from '@mui/icons-material';

import { VideoScene } from '../../types/contentCreation';

interface SceneEditorProps {
  scenes: VideoScene[]; // eslint-disable-line @typescript-eslint/no-unused-vars
  onChange: (scenes: VideoScene[]) => void;
}

const SceneEditor: React.FC<SceneEditorProps> = ({ scenes, onChange }) => {
  const handleSceneChange = (index: number, field: keyof VideoScene, value: VideoScene[keyof VideoScene]) => {
    const updatedScenes = scenes.map((scene, i) => 
      i === index ? { ...scene, [field]: value } : scene
    );
    onChange(updatedScenes);
  };

  const handleAddScene = () => {
    onChange([...scenes, { text: '', duration: 3, searchTerms: [] }]);
  };

  const handleRemoveScene = (index: number) => {
    if (scenes.length > 1) {
      onChange(scenes.filter((_, i) => i !== index));
    }
  };

  const handleSearchTermsChange = (index: number, value: string) => {
    const searchTerms = value.split(',').map(term => term.trim()).filter(term => term);
    handleSceneChange(index, 'searchTerms', searchTerms);
  };

  const getTotalDuration = () => {
    return scenes.reduce((total, scene) => total + (scene.duration || 0), 0);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <DragIcon color="primary" />
          Video Scenes
        </Typography>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Total Duration: {getTotalDuration()}s
          </Typography>
          <Button
            variant="outlined"
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAddScene}
          >
            Add Scene
          </Button>
        </Box>
      </Box>

      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
        {scenes.map((scene, index) => (
          <Box
            key={index}
            sx={{
              border: '1px solid #e2e8f0',
              borderRadius: 2,
              p: 3,
              backgroundColor: '#fafafa',
              position: 'relative',
            }}
          >
            {/* Scene Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 600, color: 'primary.main' }}>
                Scene {index + 1}
              </Typography>
              {scenes.length > 1 && (
                <IconButton
                  onClick={() => handleRemoveScene(index)}
                  size="small"
                  color="error"
                >
                  <DeleteIcon />
                </IconButton>
              )}
            </Box>

            <Grid container spacing={3}>
              {/* Scene Text */}
              <Grid item xs={12}>
                <TextField
                  label="Scene Text"
                  fullWidth
                  multiline
                  minRows={3}
                  maxRows={6}
                  variant="outlined"
                  value={scene.text}
                  onChange={(e) => handleSceneChange(index, 'text', e.target.value)}
                  placeholder="Enter the text/narration for this scene..."
                  helperText={`${scene.text.length} characters`}
                />
              </Grid>

              {/* Duration Slider */}
              <Grid item xs={12} md={6}>
                <Typography variant="body2" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
                  <TimerIcon fontSize="small" />
                  Duration: {scene.duration}s
                </Typography>
                <Slider
                  value={scene.duration}
                  onChange={(_, value) => handleSceneChange(index, 'duration', Array.isArray(value) ? value[0] : value)}
                  min={1}
                  max={30}
                  step={0.5}
                  marks={[
                    { value: 1, label: '1s' },
                    { value: 5, label: '5s' },
                    { value: 10, label: '10s' },
                    { value: 20, label: '20s' },
                    { value: 30, label: '30s' },
                  ]}
                  sx={{ mt: 1 }}
                />
              </Grid>

              {/* Search Terms */}
              <Grid item xs={12} md={6}>
                <TextField
                  label="Image Search Terms (Optional)"
                  fullWidth
                  variant="outlined"
                  value={scene.searchTerms?.join(', ') || ''}
                  onChange={(e) => handleSearchTermsChange(index, e.target.value)}
                  placeholder="nature, landscape, sunset"
                  helperText="Comma-separated keywords for image search"
                  InputProps={{
                    startAdornment: <ImageIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
                {scene.searchTerms && scene.searchTerms.length > 0 && (
                  <Box sx={{ mt: 1, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {scene.searchTerms.map((term, termIndex) => (
                      <Chip
                        key={termIndex}
                        label={term}
                        size="small"
                        variant="outlined"
                        onDelete={() => {
                          const newTerms = scene.searchTerms?.filter((_, i) => i !== termIndex) || [];
                          handleSceneChange(index, 'searchTerms', newTerms);
                        }}
                      />
                    ))}
                  </Box>
                )}
              </Grid>
            </Grid>
          </Box>
        ))}
      </Box>

      {/* Footer Info */}
      <Box sx={{ mt: 3, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
        <Typography variant="body2" color="text.secondary">
          💡 <strong>Tips:</strong> Keep scene text concise and engaging. 
          Add specific search terms to get better background images. 
          Aim for 3-10 seconds per scene for optimal pacing.
        </Typography>
      </Box>
    </Box>
  );
};

export default SceneEditor;