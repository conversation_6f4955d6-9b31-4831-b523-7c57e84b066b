import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Card,
  CardMedia,
  CardActions,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
} from '@mui/material';
import {
  Search as SearchIcon,
  Add as AddIcon,
  Remove as RemoveIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

import { directApi } from '../../utils/api';

interface ImageResult {
  id: string;
  url: string;
  download_url: string;
  width: number;
  height: number;
  photographer?: string;
  photographer_url?: string;
  alt?: string;
  tags?: string;
  source: string;
  aspect_ratio: number;
}

interface ImageSearchResult {
  images: ImageResult[];
  total_results: number;
  page: number;
  per_page: number;
  query_used: string;
  provider_used: string;
}

interface ImageBrowserProps {
  open: boolean;
  onClose: () => void;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onImagesSelected: (images: ImageResult[]) => void; // eslint-disable-line no-unused-vars
  provider: 'pexels' | 'pixabay';
  maxSelection?: number;
  selectedImages: ImageResult[];
}

const ImageBrowser: React.FC<ImageBrowserProps> = ({
  open,
  onClose,
  onImagesSelected,
  provider,
  maxSelection = 5,
  selectedImages
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<ImageSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [orientation, setOrientation] = useState('landscape');
  const [quality, setQuality] = useState('high');
  const [color, setColor] = useState('');
  const [selectedImageIds, setSelectedImageIds] = useState<Set<string>>(
    new Set(selectedImages.map(img => img.id))
  );

  const perPage = 20;

  useEffect(() => {
    setSelectedImageIds(new Set(selectedImages.map(img => img.id)));
  }, [selectedImages]);

  const handleSearch = useCallback(async (query?: string, page = 1) => {
    const searchTerm = query || searchQuery;
    if (!searchTerm.trim()) return;

    setLoading(true);
    setError(null);

    try {
      // Use the image browse endpoint for pagination
      const response = await directApi.post('/api/v1/ai/image-browse', {
        query: searchTerm,
        orientation,
        quality,
        per_page: perPage,
        page,
        color: color || undefined,
        provider
      });

      if (!response.data?.job_id) {
        throw new Error('Failed to start image search');
      }

      // Poll for results
      const jobId = response.data.job_id;
      let attempts = 0;
      const maxAttempts = 30;

      const pollResults = async () => {
        const statusResponse = await directApi.get(`/api/v1/ai/image-browse/${jobId}`);
        const jobData = statusResponse.data;

        if (jobData.status === 'completed' && jobData.result) {
          setSearchResults(jobData.result);
          setCurrentPage(page);
          setLoading(false);
        } else if (jobData.status === 'failed') {
          throw new Error(jobData.error || 'Image search failed');
        } else if (attempts < maxAttempts) {
          attempts++;
          setTimeout(pollResults, 1000);
        } else {
          throw new Error('Image search timed out');
        }
      };

      await pollResults();

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to search images');
      setLoading(false);
    }
  }, [searchQuery, orientation, quality, color, provider]);

  useEffect(() => {
    if (open && !searchResults) {
      // Load some default images when opening
      handleSearch('nature landscape', 1);
    }
  }, [open, searchResults, handleSearch]);

  const toggleImageSelection = (image: ImageResult) => {
    const newSelected = new Set(selectedImageIds);
    
    if (newSelected.has(image.id)) {
      newSelected.delete(image.id);
    } else if (newSelected.size < maxSelection) {
      newSelected.add(image.id);
    }
    
    setSelectedImageIds(newSelected);
    
    // Update selected images array
    const allImages = searchResults?.images || [];
    const selectedImagesArray = allImages.filter(img => newSelected.has(img.id));
    // Add previously selected images that aren't in current results
    const previouslySelected = selectedImages.filter(img => 
      !allImages.some(result => result.id === img.id) && newSelected.has(img.id)
    );
    
    onImagesSelected([...selectedImagesArray, ...previouslySelected]);
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    if (searchQuery.trim()) {
      handleSearch(searchQuery, page);
    }
  };

  const handleFilterChange = () => {
    if (searchQuery.trim()) {
      setCurrentPage(1);
      handleSearch(searchQuery, 1);
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="lg" 
      fullWidth
      PaperProps={{
        sx: { height: '90vh' }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="h6">
            Browse {provider === 'pexels' ? 'Pexels' : 'Pixabay'} Images
          </Typography>
          <Chip 
            label={`${selectedImageIds.size}/${maxSelection} selected`} 
            color={selectedImageIds.size > 0 ? 'primary' : 'default'}
            size="small"
          />
        </Box>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        {/* Search Controls */}
        <Box sx={{ mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search for images..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                InputProps={{
                  endAdornment: (
                    <IconButton onClick={() => handleSearch()}>
                      <SearchIcon />
                    </IconButton>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Orientation</InputLabel>
                <Select
                  value={orientation}
                  onChange={(e) => setOrientation(e.target.value)}
                  label="Orientation"
                  onClose={handleFilterChange}
                >
                  <MenuItem value="landscape">Landscape</MenuItem>
                  <MenuItem value="portrait">Portrait</MenuItem>
                  <MenuItem value="square">Square</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Quality</InputLabel>
                <Select
                  value={quality}
                  onChange={(e) => setQuality(e.target.value)}
                  label="Quality"
                  onClose={handleFilterChange}
                >
                  <MenuItem value="standard">Standard</MenuItem>
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="ultra">Ultra</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth size="small">
                <InputLabel>Color</InputLabel>
                <Select
                  value={color}
                  onChange={(e) => setColor(e.target.value)}
                  label="Color"
                  onClose={handleFilterChange}
                >
                  <MenuItem value="">Any Color</MenuItem>
                  <MenuItem value="red">Red</MenuItem>
                  <MenuItem value="orange">Orange</MenuItem>
                  <MenuItem value="yellow">Yellow</MenuItem>
                  <MenuItem value="green">Green</MenuItem>
                  <MenuItem value="turquoise">Turquoise</MenuItem>
                  <MenuItem value="blue">Blue</MenuItem>
                  <MenuItem value="violet">Violet</MenuItem>
                  <MenuItem value="pink">Pink</MenuItem>
                  <MenuItem value="brown">Brown</MenuItem>
                  <MenuItem value="black">Black</MenuItem>
                  <MenuItem value="gray">Gray</MenuItem>
                  <MenuItem value="white">White</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        {/* Error Display */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Loading State */}
        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}

        {/* Search Results */}
        {searchResults && !loading && (
          <>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Found {searchResults.total_results.toLocaleString()} images for "{searchResults.query_used}"
            </Typography>

            <Grid container spacing={2}>
              {searchResults.images.map((image) => (
                <Grid item xs={6} sm={4} md={3} key={image.id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      border: selectedImageIds.has(image.id) ? 2 : 0,
                      borderColor: 'primary.main',
                      position: 'relative'
                    }}
                  >
                    <CardMedia
                      component="img"
                      height={200}
                      image={image.url}
                      alt={image.alt || 'Stock image'}
                      sx={{ objectFit: 'cover' }}
                    />
                    <CardActions sx={{ p: 1, justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {image.width}×{image.height}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {image.source}
                        </Typography>
                      </Box>
                      <IconButton
                        size="small"
                        onClick={() => toggleImageSelection(image)}
                        color={selectedImageIds.has(image.id) ? 'primary' : 'default'}
                      >
                        {selectedImageIds.has(image.id) ? <RemoveIcon /> : <AddIcon />}
                      </IconButton>
                    </CardActions>
                    {image.photographer && (
                      <Typography
                        variant="caption"
                        sx={{
                          position: 'absolute',
                          bottom: 4,
                          left: 4,
                          background: 'rgba(0,0,0,0.7)',
                          color: 'white',
                          px: 0.5,
                          borderRadius: 0.5
                        }}
                      >
                        © {image.photographer}
                      </Typography>
                    )}
                  </Card>
                </Grid>
              ))}
            </Grid>

            {/* Pagination */}
            {searchResults.total_results > perPage && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Pagination
                  count={Math.ceil(searchResults.total_results / perPage)}
                  page={currentPage}
                  onChange={handlePageChange}
                  color="primary"
                />
              </Box>
            )}
          </>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={onClose}
          disabled={selectedImageIds.size === 0}
        >
          Use Selected Images ({selectedImageIds.size})
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ImageBrowser;
