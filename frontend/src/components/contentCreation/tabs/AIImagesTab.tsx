import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Slider,
  Paper,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  AutoAwesome as AIIcon,
  ExpandMore as ExpandMoreIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import { useVideoCreation, useVoices } from '../../../hooks/useContentCreation';
import {
  VideoCreatorFormState
} from '../../../types/contentCreation';

import VoiceSelector from '../../settings/VoiceSelectorSettings';
import UnifiedMediaProviderSettings from '../../settings/UnifiedMediaProviderSettings';
import JobStatusDisplay from '../JobStatusDisplay';

// Constants for AI Images generation
const SCRIPT_TYPES = [
  { value: 'facts', label: 'Facts' },
  { value: 'story', label: 'Story' },
  { value: 'educational', label: 'Educational' },
  { value: 'motivation', label: 'Motivation' },
  { value: 'conspiracy', label: 'Conspiracy' },
  { value: 'life_hacks', label: 'Life Hacks' },
  { value: 'shower_thoughts', label: 'Shower Thoughts' },
  { value: 'prayer', label: 'Prayer' },
  { value: 'pov', label: 'POV' },
  { value: 'would_you_rather', label: 'Would You Rather' },
  { value: 'before_you_die', label: 'Before You Die' },
  { value: 'dark_psychology', label: 'Dark Psychology' },
  { value: 'reddit_stories', label: 'Reddit Stories' },
  { value: 'daily_news', label: 'Daily News' },
] as const;

const VIDEO_ORIENTATIONS = [
  { value: 'portrait', label: 'Portrait (9:16)', width: 1080, height: 1920 },
  { value: 'landscape', label: 'Landscape (16:9)', width: 1920, height: 1080 },
  { value: 'square', label: 'Square (1:1)', width: 1080, height: 1080 },
] as const;

const CAPTION_STYLES = [
  { value: 'viral_bounce', label: 'Viral Bounce' },
  { value: 'standard_bottom', label: 'Standard Bottom' },
  { value: 'mobile_optimized', label: 'Mobile Optimized' },
  { value: 'highlight_words', label: 'Highlight Words' },
] as const;

const VIDEO_EFFECTS = [
  { value: 'ken_burns', label: 'Ken Burns' },
  { value: 'zoom_in', label: 'Zoom In' },
  { value: 'zoom_out', label: 'Zoom Out' },
  { value: 'pan_left', label: 'Pan Left' },
  { value: 'pan_right', label: 'Pan Right' },
  { value: 'static', label: 'Static' },
] as const;

// IMAGE_PROVIDERS moved to UnifiedMediaProviderSettings

interface AIImagesFormState extends Omit<VideoCreatorFormState, 'topic' | 'useCustomScript'> {
  script: string;
  scriptType: string;
  maxDuration: number;
  ttsSpeed: number;
  segmentDuration: number;
  captionColor: string;
  backgroundMusic: string;
  backgroundMusicVolume: number;
  generateBackgroundMusic: boolean;
  frameRate: number;
  videoEffect: string;
  inferenceSteps: number;
  guidanceScale: number;
  musicDuration: number;
  // Unified media provider properties
  mediaType: 'video' | 'image';
  aiImageProvider: string;
}

// Helper function to estimate script duration
const estimateScriptDuration = (script: string): number => {
  if (!script.trim()) return 0;
  // Average speaking rate: ~2.5 words per second (150 words per minute)
  const words = script.trim().split(/\s+/).length;
  return Math.ceil(words / 2.5);
};

const AIImagesTab: React.FC = () => {
  const {
    result,
    jobStatus,
    jobProgress,
    loading,
    error,
    createVideo,
    resetState,
    setError
  } = useVideoCreation();

  const {
    voices,
    fetchVoices,
    loading: voicesLoading
  } = useVoices();

  const [formState, setFormState] = useState<AIImagesFormState>({
    // Custom script fields (AI Images specific)
    script: '',
    scriptType: 'facts',
    maxDuration: 120,
    ttsSpeed: 1.0,
    segmentDuration: 3.0,
    generateBackgroundMusic: false,
    frameRate: 30,
    videoEffect: 'ken_burns',
    inferenceSteps: 4,
    guidanceScale: 3.5,
    musicDuration: 60,
    
    // Base VideoCreatorFormState properties (excluding topic, useCustomScript)
    voiceProvider: 'kokoro',
    voiceName: 'af_bella',
    language: 'en',
    imageWidth: 1024,
    imageHeight: 1024,
    aspectRatio: '9:16',
    captionStyle: 'viral_bounce',
    captionColor: '#00FFFF',
    footageProvider: 'ai_generated', // Default to AI generated for this tab
    aiVideoProvider: 'wavespeed',
    searchSafety: 'moderate',
    enableCaptions: true,
    backgroundMusic: 'none',
    backgroundMusicVolume: 0.3,
    footageQuality: 'high',
    searchTermsPerScene: 3,
    
    // Unified media provider properties  
    mediaType: 'image', // Default to image for AI Images tab
    aiImageProvider: 'together',
  });

  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  const handleFormChange = <K extends keyof AIImagesFormState>(
    field: K,
    value: AIImagesFormState[K]
  ) => {
    setFormState(prev => ({ ...prev, [field]: value }));
    
    // Auto-adjust resolution based on aspect ratio
    if (field === 'aspectRatio') {
      const orientation = VIDEO_ORIENTATIONS.find(o => o.value === value);
      if (orientation) {
        setFormState(prev => ({
          ...prev,
          imageWidth: orientation.width,
          imageHeight: orientation.height,
        }));
      }
    }
  };

  const handleCreateVideo = async () => {
    if (!formState.script.trim()) {
      setError('Please enter a script for the video.');
      return;
    }

    // Create AiimageToVideoRequest format - MANUAL MODE (user provides script)
    const request = {
      // Manual script mode - pass script as custom_script for the pipeline
      custom_script: formState.script, // User's custom script
      language: formState.language,
      
      // Script generation options (still needed even for custom script)
      script_type: formState.scriptType,
      max_duration: formState.maxDuration,
      
      // TTS options
      voice: formState.voiceName,
      tts_provider: formState.voiceProvider,
      tts_speed: formState.ttsSpeed,
      
      // Image generation options (AI images)
      image_provider: formState.aiImageProvider, // Use unified AI image provider
      image_width: formState.imageWidth,
      image_height: formState.imageHeight,
      image_steps: formState.inferenceSteps,
      guidance_scale: formState.guidanceScale,
      
      // Video effects options (these were missing!)
      effect_type: formState.videoEffect, // Maps to effect_type in backend
      segment_duration: formState.segmentDuration,
      frame_rate: formState.frameRate || 30,
      
      // Music options (these were missing!)
      generate_background_music: formState.generateBackgroundMusic,
      background_music: formState.backgroundMusic,
      background_music_volume: formState.backgroundMusicVolume,
      music_duration: formState.musicDuration,
      
      // Caption options (these were missing!)
      add_captions: formState.enableCaptions, // Backend uses 'add_captions', not 'enable_captions'
      caption_style: formState.captionStyle,
      caption_color: formState.captionColor,
    };

    try {
      await createVideo(request); // Using aiimage-to-video format
    } catch (err) {
      console.error('Failed to create AI images video:', err);
    }
  };

  const handleReset = () => {
    resetState();
    setFormState({
      // Custom script fields (AI Images specific)
      script: '',
      scriptType: 'facts',
      maxDuration: 120,
      ttsSpeed: 1.0,
      segmentDuration: 3.0,
      generateBackgroundMusic: false,
      frameRate: 30,
      videoEffect: 'ken_burns',
      inferenceSteps: 4,
      guidanceScale: 3.5,
      musicDuration: 60,
      
      // Base VideoCreatorFormState properties (excluding topic, useCustomScript)
      voiceProvider: 'kokoro',
      voiceName: 'af_bella',
      language: 'en',
      imageWidth: 1024,
      imageHeight: 1024,
      aspectRatio: '9:16',
      captionStyle: 'viral_bounce',
      captionColor: '#00FFFF',
      footageProvider: 'ai_generated',
      aiVideoProvider: 'wavespeed',
      searchSafety: 'moderate',
      enableCaptions: true,
      backgroundMusic: 'none',
      backgroundMusicVolume: 0.3,
      footageQuality: 'high',
      searchTermsPerScene: 3,
      
      // Unified media provider properties  
      mediaType: 'image',
      aiImageProvider: 'together',
    });
  };

  const estimatedDuration = estimateScriptDuration(formState.script);

  if (voicesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading voices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, display: 'flex', alignItems: 'center' }}>
          <AIIcon sx={{ mr: 1, color: '#8b5cf6' }} />
          AI Images Video Generator
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Generate videos with AI-created images from your script. Perfect for educational, storytelling, and engaging content.
        </Typography>
      </Box>

      {/* LTX Video Generator Promotion */}
      <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 2, mb: 3, backgroundColor: '#f0f9ff' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: '#0369a1' }}>
              Try AI Video Generator
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Generate high-quality AI videos directly from text prompts or animate your images using advanced AI models.
            </Typography>
          </Box>
          <Button
            variant="contained"
            href="/dashboard/ai-video"
            sx={{
              backgroundColor: '#0ea5e9',
              '&:hover': { backgroundColor: '#0284c7' },
              borderRadius: 2,
              px: 3,
              py: 1,
            }}
          >
Go to AI Video Generator
          </Button>
        </Box>
      </Paper>

      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Job Status */}
      {(loading || jobStatus) && (
        <JobStatusDisplay
          loading={loading}
          jobStatus={jobStatus}
          jobProgress={jobProgress}
          result={result}
          onReset={handleReset}
        />
      )}

      {/* Form Content */}
      {!loading && !jobStatus && (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Script Input */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Script Content
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Video Script"
                  multiline
                  rows={6}
                  value={formState.script}
                  onChange={(e) => handleFormChange('script', e.target.value)}
                  placeholder="Enter your video script here. Each sentence will become a scene with AI-generated images..."
                  variant="outlined"
                />
                {formState.script && (
                  <Box sx={{ mt: 1, display: 'flex', gap: 2 }}>
                    <Chip 
                      size="small" 
                      label={`${formState.script.split(/\s+/).length} words`} 
                      color="primary" 
                      variant="outlined"
                    />
                    <Chip 
                      size="small" 
                      label={`~${estimatedDuration}s duration`} 
                      color="secondary" 
                      variant="outlined"
                    />
                  </Box>
                )}
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Script Type</InputLabel>
                  <Select
                    value={formState.scriptType}
                    label="Script Type"
                    onChange={(e) => handleFormChange('scriptType', e.target.value)}
                  >
                    {SCRIPT_TYPES.map((type) => (
                      <MenuItem key={type.value} value={type.value}>
                        {type.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography gutterBottom>Max Duration (seconds)</Typography>
                  <Slider
                    value={formState.maxDuration}
                    onChange={(_, value) => handleFormChange('maxDuration', value as number)}
                    min={30}
                    max={300}
                    step={15}
                    marks={[
                      { value: 30, label: '30s' },
                      { value: 60, label: '1m' },
                      { value: 120, label: '2m' },
                      { value: 180, label: '3m' },
                      { value: 300, label: '5m' }
                    ]}
                    valueLabelDisplay="on"
                  />
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Voice Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}>
            <VoiceSelector
              voiceProvider={formState.voiceProvider}
              voiceName={formState.voiceName}
              language={formState.language}
              voices={voices}
              onVoiceProviderChange={(provider) => handleFormChange('voiceProvider', provider)}
              onVoiceNameChange={(name) => handleFormChange('voiceName', name)}
              onLanguageChange={(lang) => handleFormChange('language', lang)}
            />
            
            {/* TTS Speed Control */}
            <Box sx={{ p: 3, pt: 0 }}>
              <Typography gutterBottom>Speech Speed</Typography>
              <Slider
                value={formState.ttsSpeed}
                onChange={(_, value) => handleFormChange('ttsSpeed', value as number)}
                min={0.5}
                max={2.0}
                step={0.1}
                marks={[
                  { value: 0.5, label: '0.5x' },
                  { value: 1.0, label: '1x' },
                  { value: 1.5, label: '1.5x' },
                  { value: 2.0, label: '2x' }
                ]}
                valueLabelDisplay="on"
              />
            </Box>
          </Paper>

          {/* Video Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Video Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Video Orientation</InputLabel>
                  <Select
                    value={formState.aspectRatio}
                    label="Video Orientation"
                    onChange={(e) => handleFormChange('aspectRatio', e.target.value)}
                  >
                    {VIDEO_ORIENTATIONS.map((orientation) => (
                      <MenuItem key={orientation.value} value={orientation.value}>
                        {orientation.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Video Effect</InputLabel>
                  <Select
                    value={formState.videoEffect}
                    label="Video Effect"
                    onChange={(e) => handleFormChange('videoEffect', e.target.value)}
                  >
                    {VIDEO_EFFECTS.map((effect) => (
                      <MenuItem key={effect.value} value={effect.value}>
                        {effect.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box>
                  <Typography gutterBottom>Segment Duration (seconds)</Typography>
                  <Slider
                    value={formState.segmentDuration}
                    onChange={(_, value) => handleFormChange('segmentDuration', value as number)}
                    min={1}
                    max={10}
                    step={0.5}
                    marks={[
                      { value: 1, label: '1s' },
                      { value: 3, label: '3s' },
                      { value: 5, label: '5s' },
                      { value: 10, label: '10s' }
                    ]}
                    valueLabelDisplay="on"
                  />
                </Box>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Caption Style</InputLabel>
                  <Select
                    value={formState.captionStyle}
                    label="Caption Style"
                    onChange={(e) => handleFormChange('captionStyle', e.target.value)}
                  >
                    {CAPTION_STYLES.map((style) => (
                      <MenuItem key={style.value} value={style.value}>
                        {style.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* Unified Media Provider Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}>
            <UnifiedMediaProviderSettings
              mediaType={formState.mediaType}
              provider={formState.footageProvider}
              aiVideoProvider={formState.aiVideoProvider}
              aiImageProvider={formState.aiImageProvider}
              searchSafety={formState.searchSafety}
              quality={formState.footageQuality}
              searchTermsPerScene={formState.searchTermsPerScene}
              guidanceScale={formState.guidanceScale}
              inferenceSteps={formState.inferenceSteps}
              showAdvancedSettings={true}
              onMediaTypeChange={(type) => handleFormChange('mediaType', type)}
              onProviderChange={(provider) => handleFormChange('footageProvider', provider as string)}
              onAiVideoProviderChange={(provider) => handleFormChange('aiVideoProvider', provider)}
              onAiImageProviderChange={(provider) => handleFormChange('aiImageProvider', provider)}
              onSearchSafetyChange={(safety) => handleFormChange('searchSafety', safety)}
              onQualityChange={(quality) => handleFormChange('footageQuality', quality)}
              onSearchTermsPerSceneChange={(terms) => handleFormChange('searchTermsPerScene', terms)}
              onGuidanceScaleChange={(scale) => handleFormChange('guidanceScale', scale)}
              onInferenceStepsChange={(steps) => handleFormChange('inferenceSteps', steps)}
            />
          </Paper>

          {/* Additional Advanced Settings */}
          <Accordion 
            expanded={showAdvancedSettings} 
            onChange={(_, expanded) => setShowAdvancedSettings(expanded)}
            elevation={0}
            sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SettingsIcon sx={{ mr: 1, color: '#6b7280' }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Additional Settings
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography gutterBottom>Background Music Volume</Typography>
                    <Slider
                      value={formState.backgroundMusicVolume}
                      onChange={(_, value) => handleFormChange('backgroundMusicVolume', value as number)}
                      min={0}
                      max={1}
                      step={0.1}
                      marks={[
                        { value: 0, label: '0%' },
                        { value: 0.3, label: '30%' },
                        { value: 0.7, label: '70%' },
                        { value: 1, label: '100%' }
                      ]}
                      valueLabelDisplay="on"
                      valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
                    />
                  </Box>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Create Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayIcon />}
              onClick={handleCreateVideo}
              disabled={loading || !formState.script.trim()}
              sx={{
                backgroundColor: '#8b5cf6',
                '&:hover': { backgroundColor: '#7c3aed' },
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              Generate AI Images Video
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default AIImagesTab;