import React, { useState } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Grid,
  Alert,
  Paper,
  Slider,
  Switch,
  FormControlLabel,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  AutoAwesome as AIIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { directApi } from '../../../utils/api';
import { useVideoCreation } from '../../../hooks/useContentCreation';

interface VideoGeneratorFormState {
  prompt: string;
  provider: string;
  negativePrompt: string;
  width: number;
  height: number;
  numFrames: number;
  numInferenceSteps: number;
  guidanceScale: number;
  seed: number | null;
  imageFile: File | null;
  imageUrl: string;
}

interface VideoGeneratorResult {
  video_url: string;
  prompt_used: string;
  negative_prompt_used: string;
  dimensions: {
    width: number;
    height: number;
  };
  num_frames: number;
  processing_time: number;
  provider_used: string;
  original_image_url?: string; // Only present for image-to-video results
}

const VideoGeneratorTab: React.FC = () => {
  const [formState, setFormState] = useState<VideoGeneratorFormState>({
    prompt: '',
    provider: 'wavespeed', // Default to WaveSpeed
    negativePrompt: '',
    width: 832,
    height: 480,
    numFrames: 150,
    numInferenceSteps: 200,
    guidanceScale: 4.5,
    seed: null,
    imageFile: null,
    imageUrl: '',
  });

  const [useImageInput, setUseImageInput] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [jobId, setJobId] = useState<string | null>(null);
  const [jobStatus, setJobStatus] = useState<string | null>(null);
  const [jobResult, setJobResult] = useState<VideoGeneratorResult | null>(null);

  const { resetState } = useVideoCreation();

  const handleFormChange = <K extends keyof VideoGeneratorFormState>(
    field: K,
    value: VideoGeneratorFormState[K]
  ) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      handleFormChange('imageFile', file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setImagePreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleCreateVideo = async () => {
    if (!formState.prompt.trim()) {
      setError('Please enter a prompt for the video.');
      return;
    }

    if (useImageInput && !formState.imageFile) {
      setError('Please select an image file.');
      return;
    }

    // Validate dimensions based on provider
    if (formState.provider === 'ltx_video') {
      if (formState.width % 32 !== 0 || formState.height % 32 !== 0) {
        setError('For LTX-Video, width and height must be divisible by 32.');
        return;
      }
    }

    setLoading(true);
    setError(null);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);

    try {
      if (useImageInput && formState.imageFile) {
        // Image-to-video generation
        const params = {
          prompt: formState.prompt,
          provider: formState.provider,
          image: formState.imageFile,
          negative_prompt: formState.negativePrompt,
          width: formState.width,
          height: formState.height,
          num_frames: formState.numFrames,
          num_inference_steps: formState.numInferenceSteps,
          guidance_scale: formState.guidanceScale,
          seed: formState.seed || undefined
        };

        const response = await directApi.generateVideoFromImage(params);
        if (response.success && response.data?.job_id) {
          setJobId(response.data.job_id);
          pollJobStatus(response.data.job_id, 'image-to-video');
        } else {
          setError(response.error || 'Failed to generate video from image');
          setLoading(false);
        }
      } else {
        // Text-to-video generation
        const params = {
          prompt: formState.prompt,
          provider: formState.provider,
          negative_prompt: formState.negativePrompt,
          width: formState.width,
          height: formState.height,
          num_frames: formState.numFrames,
          num_inference_steps: formState.numInferenceSteps,
          guidance_scale: formState.guidanceScale,
          seed: formState.seed || undefined
        };

        const response = await directApi.generateVideo(params);
        if (response.success && response.data?.job_id) {
          setJobId(response.data.job_id);
          pollJobStatus(response.data.job_id, 'text-to-video');
        } else {
          setError(response.error || 'Failed to generate video');
          setLoading(false);
        }
      }
    } catch (err) {
      console.error('Failed to create video:', err);
      setError('An error occurred while generating the video');
      setLoading(false);
    }
  };

  const pollJobStatus = async (jobId: string, jobType: string) => {
    setJobStatus('pending');
    
    try {
      let attempts = 0;
      const maxAttempts = 120; // 10 minutes max
      const pollInterval = 5000; // 5 seconds

      const poll = async () => {
        attempts++;
        
        try {
          let response;
          if (jobType === 'image-to-video') {
            response = await directApi.getVideoFromImageStatus(jobId);
          } else {
            response = await directApi.getVideoStatus(jobId);
          }
          
          if (response.success && response.data) {
            const job = response.data;
            setJobStatus(job.status);
            
            if (job.status === 'completed') {
              setJobResult(job.result);
              setLoading(false);
              return;
            } else if (job.status === 'failed') {
              setError(job.error || 'Video generation failed');
              setLoading(false);
              return;
            }
          }
          
          if (attempts >= maxAttempts) {
            setError('Video generation timed out');
            setLoading(false);
            return;
          }
          
          // Continue polling
          setTimeout(poll, pollInterval);
        } catch (err) {
          console.error('Error polling job status:', err);
          if (attempts >= maxAttempts) {
            setError('Failed to get job status');
            setLoading(false);
          } else {
            setTimeout(poll, pollInterval);
          }
        }
      };
      
      poll();
    } catch (err) {
      console.error('Error starting job polling:', err);
      setError('Failed to start job monitoring');
      setLoading(false);
    }
  };

  const handleReset = () => {
    setFormState({
      prompt: '',
      provider: 'wavespeed',
      negativePrompt: '',
      width: 832,
      height: 480,
      numFrames: 150,
      numInferenceSteps: 200,
      guidanceScale: 4.5,
      seed: null,
      imageFile: null,
      imageUrl: '',
    });
    setImagePreview(null);
    setUseImageInput(false);
    setError(null);
    setLoading(false);
    setJobId(null);
    setJobStatus(null);
    setJobResult(null);
    resetState();
  };

  const estimatedDuration = Math.round(formState.numFrames / 24);

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, display: 'flex', alignItems: 'center' }}>
          <AIIcon sx={{ mr: 1, color: '#8b5cf6' }} />
          AI Video Generator
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Generate high-quality videos from text prompts or animate images using AI models (LTX-Video, WaveSpeed, ComfyUI).
        </Typography>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert
          severity="error"
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Job Status */}
      {(loading || jobStatus) && (
        <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3, mb: 3 }}>
          <Box sx={{ textAlign: 'center' }}>
            {loading && <CircularProgress sx={{ mb: 2 }} />}
            <Typography variant="h6" sx={{ mb: 1 }}>
              {jobStatus === 'pending' ? 'Job Queued' :
               jobStatus === 'processing' ? 'Generating Video...' :
               jobStatus === 'completed' ? 'Video Generated Successfully!' :
               jobStatus === 'failed' ? 'Generation Failed' : 'Processing...'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {jobId && `Job ID: ${jobId}`}
            </Typography>
            {jobResult && jobResult.video_url && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="contained"
                  href={jobResult.video_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{ mt: 1 }}
                >
                  Download Video
                </Button>
              </Box>
            )}
            <Button
              variant="outlined"
              onClick={handleReset}
              sx={{ mt: 2 }}
            >
              Create Another Video
            </Button>
          </Box>
        </Paper>
      )}

      {/* Form Content */}
      {!loading && !jobStatus && (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Provider Selection */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              AI Video Provider
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Provider</InputLabel>
                  <Select
                    value={formState.provider}
                    onChange={(e) => handleFormChange('provider', e.target.value)}
                    label="Provider"
                  >
                    <MenuItem value="wavespeed">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>🌊</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            WaveSpeed AI
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            Fast video generation with multiple models
                          </Typography>
                        </Box>
                        <Chip label="Fast" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                    <MenuItem value="ltx_video">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>⚡</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            LTX-Video
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            High-quality video generation with fine control
                          </Typography>
                        </Box>
                        <Chip label="High Quality" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                    <MenuItem value="comfyui">
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                        <span style={{ fontSize: '1.2em' }}>🎛️</span>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            ComfyUI
                          </Typography>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
                            Custom video generation using ComfyUI workflows
                          </Typography>
                        </Box>
                        <Chip label="Custom" size="small" variant="outlined" />
                      </Box>
                    </MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box sx={{ 
                  p: 2, 
                  backgroundColor: '#f8f9fa', 
                  borderRadius: 1,
                  border: '1px solid #e2e8f0'
                }}>
                  <Typography variant="subtitle2" sx={{ mb: 1 }}>
                    {formState.provider === 'wavespeed' ? '🌊 WaveSpeed Features' : 
                     formState.provider === 'comfyui' ? '🎛️ ComfyUI Features' : '⚡ LTX-Video Features'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {formState.provider === 'wavespeed' 
                      ? 'Duration: 1-8 seconds • Models: WAN-2.2, MiniMax • Fast processing'
                      : formState.provider === 'comfyui'
                      ? 'Custom workflows • WAN-2.2 model • Variable processing time'
                      : 'Frames: 1-257 • Dimensions: divisible by 32 • Fine control'}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Input Mode Toggle */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={useImageInput}
                  onChange={(e) => setUseImageInput(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {useImageInput ? 'Image-to-Video Mode' : 'Text-to-Video Mode'}
                </Typography>
              }
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {useImageInput
                ? 'Animate an existing image with a text prompt'
                : 'Generate a video from a text prompt'}
            </Typography>
          </Paper>

          {/* Prompt Input */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              {useImageInput ? 'Animation Prompt' : 'Video Prompt'}
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label={useImageInput ? "Describe how you want the image to animate" : "Describe the video you want to generate"}
                  multiline
                  rows={4}
                  value={formState.prompt}
                  onChange={(e) => handleFormChange('prompt', e.target.value)}
                  placeholder={useImageInput
                    ? "e.g., make the flowers bloom and butterflies fly around"
                    : "e.g., A cat walking through a garden with flowers blooming"}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Negative Prompt (What to avoid)"
                  multiline
                  rows={2}
                  value={formState.negativePrompt}
                  onChange={(e) => handleFormChange('negativePrompt', e.target.value)}
                  placeholder="e.g., blurry, low quality, distorted"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Image Input (only shown in image-to-video mode) */}
          {useImageInput && (
            <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Input Image
              </Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Button
                    variant="outlined"
                    component="label"
                    fullWidth
                    sx={{ height: '100%' }}
                  >
                    <ImageIcon sx={{ mr: 1 }} />
                    {formState.imageFile ? 'Change Image' : 'Select Image'}
                    <input
                      type="file"
                      hidden
                      accept="image/*"
                      onChange={handleImageChange}
                    />
                  </Button>
                  {formState.imageFile && (
                    <Typography variant="body2" sx={{ mt: 1, textAlign: 'center' }}>
                      {formState.imageFile.name}
                    </Typography>
                  )}
                </Grid>
                
                <Grid item xs={12} md={6}>
                  {imagePreview ? (
                    <Box sx={{ textAlign: 'center' }}>
                      <img
                        src={imagePreview}
                        alt="Preview"
                        style={{
                          maxWidth: '100%',
                          maxHeight: '200px',
                          borderRadius: '8px',
                          border: '1px solid #e2e8f0'
                        }}
                      />
                    </Box>
                  ) : (
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '200px',
                      border: '2px dashed #e2e8f0',
                      borderRadius: '8px',
                      color: '#94a3b8'
                    }}>
                      <Typography>No image selected</Typography>
                    </Box>
                  )}
                </Grid>
              </Grid>
            </Paper>
          )}

          {/* Video Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Video Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label={formState.provider === 'ltx_video' ? 'Width (must be divisible by 32)' : 'Width'}
                  type="number"
                  value={formState.width}
                  onChange={(e) => handleFormChange('width', parseInt(e.target.value) || (formState.provider === 'wavespeed' ? 832 : 704))}
                  InputProps={{ 
                    inputProps: formState.provider === 'ltx_video' 
                      ? { min: 32, max: 1024, step: 32 }
                      : { min: 256, max: 1024 }
                  }}
                  variant="outlined"
                  helperText={formState.provider === 'wavespeed' ? 'Common sizes: 832x480 (landscape), 480x832 (portrait)' : undefined}
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label={formState.provider === 'ltx_video' ? 'Height (must be divisible by 32)' : 'Height'}
                  type="number"
                  value={formState.height}
                  onChange={(e) => handleFormChange('height', parseInt(e.target.value) || 480)}
                  InputProps={{ 
                    inputProps: formState.provider === 'ltx_video' 
                      ? { min: 32, max: 1024, step: 32 }
                      : { min: 256, max: 1024 }
                  }}
                  variant="outlined"
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Box>
                  {formState.provider === 'ltx_video' ? (
                    <>
                      <Typography gutterBottom>Number of Frames (~{estimatedDuration}s)</Typography>
                      <Slider
                        value={formState.numFrames}
                        onChange={(_, value) => handleFormChange('numFrames', value as number)}
                        min={1}
                        max={257}
                        step={1}
                        marks={[
                          { value: 19, label: '19 (~0.8s)' },
                          { value: 75, label: '75 (~3s)' },
                          { value: 150, label: '150 (~6s)' },
                          { value: 257, label: '257 (~11s)' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </>
                  ) : (
                    <>
                      <Typography gutterBottom>Duration (seconds)</Typography>
                      <Slider
                        value={Math.min(Math.round(formState.numFrames / 15), 8)}
                        onChange={(_, value) => handleFormChange('numFrames', (value as number) * 15)}
                        min={1}
                        max={8}
                        step={1}
                        marks={[
                          { value: 1, label: '1s' },
                          { value: 3, label: '3s' },
                          { value: 5, label: '5s' },
                          { value: 8, label: '8s' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </>
                  )}
                </Box>
              </Grid>
              
              {formState.provider === 'ltx_video' && (
                <>
                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography gutterBottom>Inference Steps</Typography>
                      <Slider
                        value={formState.numInferenceSteps}
                        onChange={(_, value) => handleFormChange('numInferenceSteps', value as number)}
                        min={1}
                        max={500}
                        step={1}
                        marks={[
                          { value: 20, label: '20' },
                          { value: 100, label: '100' },
                          { value: 200, label: '200' },
                          { value: 500, label: '500' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <Box>
                      <Typography gutterBottom>Guidance Scale</Typography>
                      <Slider
                        value={formState.guidanceScale}
                        onChange={(_, value) => handleFormChange('guidanceScale', value as number)}
                        min={1}
                        max={20}
                        step={0.5}
                        marks={[
                          { value: 1, label: '1' },
                          { value: 4.5, label: '4.5' },
                          { value: 10, label: '10' },
                          { value: 20, label: '20' }
                        ]}
                        valueLabelDisplay="on"
                      />
                    </Box>
                  </Grid>
                </>
              )}
              
              {formState.provider === 'wavespeed' && (
                <Grid item xs={12} md={6}>
                  <Box sx={{ 
                    p: 2, 
                    backgroundColor: '#f0f9ff', 
                    borderRadius: 1,
                    border: '1px solid #e2e8f0'
                  }}>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      🌊 WaveSpeed Settings
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      • Model: WAN-2.2 (ultra-fast)
                      <br />• Auto-optimized inference steps
                      <br />• Smart quality balancing
                    </Typography>
                  </Box>
                </Grid>
              )}
              
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Seed (optional)"
                  type="number"
                  value={formState.seed || ''}
                  onChange={(e) => handleFormChange('seed', e.target.value ? parseInt(e.target.value) : null)}
                  placeholder="Leave blank for random"
                  variant="outlined"
                />
              </Grid>
            </Grid>
          </Paper>

          {/* Create Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<PlayIcon />}
              onClick={handleCreateVideo}
              disabled={loading || !formState.prompt.trim() || (useImageInput && !formState.imageFile)}
              sx={{
                backgroundColor: '#8b5cf6',
                '&:hover': { backgroundColor: '#7c3aed' },
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              {useImageInput ? 'Generate Video from Image' : 'Generate Video from Text'}
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default VideoGeneratorTab;