import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  Slider,
} from '@mui/material';
import {
  TrendingUp as TrendingIcon,
  AutoAwesome as AIIcon,
  VideoLibrary as StockIcon,
} from '@mui/icons-material';

import { useTopicResearch, useVoices } from '../../../hooks/useContentCreation';
import VoiceSelector from '../../settings/VoiceSelectorSettings';
import JobStatusDisplay from '../JobStatusDisplay';

// Script types for auto topic discovery
const SCRIPT_TYPES = [
  { value: 'facts', label: 'Interesting Facts', description: 'Discover trending facts and trivia' },
  { value: 'story', label: 'Stories', description: 'Find compelling stories from current events' },
  { value: 'educational', label: 'Educational', description: 'Trending educational topics' },
  { value: 'motivation', label: 'Motivational', description: 'Inspiring and motivational content' },
  { value: 'conspiracy', label: 'Conspiracy', description: 'Popular conspiracy theories' },
  { value: 'life_hacks', label: 'Life Hacks', description: 'Trending life tips and hacks' },
  { value: 'shower_thoughts', label: 'Shower Thoughts', description: 'Deep thoughts and philosophy' },
  { value: 'pov', label: 'POV Content', description: 'Point of view scenarios' },
  { value: 'would_you_rather', label: 'Would You Rather', description: 'Choice-based content' },
  { value: 'dark_psychology', label: 'Dark Psychology', description: 'Psychology insights' },
  { value: 'reddit_stories', label: 'Reddit Stories', description: 'Popular Reddit content' },
  { value: 'daily_news', label: 'Daily News', description: 'Current news and events' },
] as const;

const VIDEO_METHODS = [
  {
    value: 'stock',
    label: 'Stock Footage',
    description: 'Auto-discover trending topics and create videos with real stock footage',
    features: ['Real footage', 'Professional quality', 'Trending topics']
  },
  {
    value: 'ai_images',
    label: 'AI Images',
    description: 'Auto-discover trending topics and create videos with AI-generated images',
    features: ['Custom visuals', 'Unique content', 'Trending topics']
  },
] as const;

interface AutoTopicFormState {
  scriptType: string;
  videoMethod: 'stock' | 'ai_images';
  maxDuration: number;
  language: string;
  voiceProvider: string;
  voiceName: string;
  ttsSpeed: number;
  aspectRatio: string;
  enableCaptions: boolean;
  captionStyle: string;
  enableBackgroundMusic: boolean;
  musicVolume: number;
}

const AutoTopicTab: React.FC = () => {
  const {
    result,
    jobStatus,
    jobProgress,
    loading,
    error,
    researchTopic,
    resetState,
    setError
  } = useTopicResearch();

  const {
    voices,
    fetchVoices,
    loading: voicesLoading
  } = useVoices();

  const [formState, setFormState] = useState<AutoTopicFormState>({
    scriptType: 'facts',
    videoMethod: 'stock',
    maxDuration: 120,
    language: 'en',
    voiceProvider: 'kokoro',
    voiceName: 'af_bella',
    ttsSpeed: 1.0,
    aspectRatio: '9:16',
    enableCaptions: true,
    captionStyle: 'viral_bounce',
    enableBackgroundMusic: false,
    musicVolume: 0.3,
  });

  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  const handleFormChange = <K extends keyof AutoTopicFormState>(
    field: K,
    value: AutoTopicFormState[K]
  ) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleAutoTopicGenerate = async () => {
    const request = {
      topic: '', // Empty topic for auto-discovery
      auto_topic: true, // This is the key flag for auto topic discovery
      method: formState.videoMethod,
      script_type: formState.scriptType,
      max_duration: formState.maxDuration,
      language: formState.language,
      voice_provider: formState.voiceProvider,
      voice_name: formState.voiceName,
      tts_speed: formState.ttsSpeed,
      aspect_ratio: formState.aspectRatio,
      enable_captions: formState.enableCaptions,
      caption_style: formState.captionStyle,
      enable_background_music: formState.enableBackgroundMusic,
      music_volume: formState.musicVolume,
    };

    try {
      await researchTopic(request);
    } catch (err) {
      console.error('Failed to generate auto topic video:', err);
    }
  };

  const handleReset = () => {
    resetState();
    setFormState({
      scriptType: 'facts',
      videoMethod: 'stock',
      maxDuration: 120,
      language: 'en',
      voiceProvider: 'kokoro',
      voiceName: 'af_bella',
      ttsSpeed: 1.0,
      aspectRatio: '9:16',
      enableCaptions: true,
      captionStyle: 'viral_bounce',
      enableBackgroundMusic: false,
      musicVolume: 0.3,
    });
  };

  const selectedScriptType = SCRIPT_TYPES.find(type => type.value === formState.scriptType);

  if (voicesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading voices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, display: 'flex', alignItems: 'center' }}>
          <TrendingIcon sx={{ mr: 1, color: '#f59e0b' }} />
          Auto Topic Discovery
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Let AI automatically discover trending topics and create engaging videos without manual research.
        </Typography>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Job Status */}
      {(loading || jobStatus) && (
        <JobStatusDisplay
          loading={loading}
          jobStatus={jobStatus}
          jobProgress={jobProgress}
          result={result}
          onReset={handleReset}
        />
      )}

      {/* Form Content */}
      {!loading && !jobStatus && (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Auto Topic Explanation */}
          <Paper elevation={0} sx={{ border: '2px solid #fbbf24', borderRadius: 2, p: 3, backgroundColor: '#fffbeb' }}>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <TrendingIcon color="warning" />
              <Typography variant="h6">How Auto Topic Works</Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" mb={2}>
              AI will automatically find trending topics related to your selected script type and create engaging videos 
              without manual topic input. Perfect for discovering current trends and viral content ideas.
            </Typography>
            <Box display="flex" gap={1}>
              <Chip label="Trending topics" size="small" color="warning" variant="outlined" />
              <Chip label="No manual research" size="small" color="warning" variant="outlined" />
              <Chip label="Current events" size="small" color="warning" variant="outlined" />
              <Chip label="AI-powered" size="small" color="warning" variant="outlined" />
            </Box>
          </Paper>

          {/* Script Type Selection */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Content Category
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Choose what type of trending content you want AI to discover and create videos about.
            </Typography>
            
            <FormControl fullWidth>
              <InputLabel>Script Type</InputLabel>
              <Select
                value={formState.scriptType}
                label="Script Type"
                onChange={(e) => handleFormChange('scriptType', e.target.value)}
              >
                {SCRIPT_TYPES.map((type) => (
                  <MenuItem key={type.value} value={type.value}>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600 }}>
                        {type.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {type.description}
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            
            {selectedScriptType && (
              <Box sx={{ mt: 2, p: 2, backgroundColor: '#f8f9fa', borderRadius: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  <strong>Selected:</strong> {selectedScriptType.description}
                </Typography>
              </Box>
            )}
          </Paper>

          {/* Video Method Selection */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Video Style
            </Typography>
            
            <FormControl component="fieldset">
              <RadioGroup
                value={formState.videoMethod}
                onChange={(e) => handleFormChange('videoMethod', e.target.value as 'stock' | 'ai_images')}
              >
                <Grid container spacing={2}>
                  {VIDEO_METHODS.map((method) => (
                    <Grid item xs={12} md={6} key={method.value}>
                      <Card 
                        sx={{ 
                          cursor: 'pointer',
                          border: formState.videoMethod === method.value ? '2px solid #3b82f6' : '1px solid #e2e8f0',
                          backgroundColor: formState.videoMethod === method.value ? '#f0f9ff' : 'transparent'
                        }}
                        onClick={() => handleFormChange('videoMethod', method.value)}
                      >
                        <CardContent>
                          <FormControlLabel
                            value={method.value}
                            control={<Radio />}
                            label={
                              <Box>
                                <Box display="flex" alignItems="center" gap={1} mb={1}>
                                  {method.value === 'stock' ? <StockIcon color="primary" /> : <AIIcon color="primary" />}
                                  <Typography variant="h6">{method.label}</Typography>
                                </Box>
                                <Typography variant="body2" color="text.secondary" mb={1}>
                                  {method.description}
                                </Typography>
                                <Box display="flex" gap={1}>
                                  {method.features.map((feature) => (
                                    <Chip key={feature} label={feature} size="small" />
                                  ))}
                                </Box>
                              </Box>
                            }
                          />
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              </RadioGroup>
            </FormControl>
          </Paper>

          {/* Video Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              Video Settings
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Aspect Ratio</InputLabel>
                  <Select
                    value={formState.aspectRatio}
                    label="Aspect Ratio"
                    onChange={(e) => handleFormChange('aspectRatio', e.target.value)}
                  >
                    <MenuItem value="9:16">Portrait (9:16) - TikTok/Shorts</MenuItem>
                    <MenuItem value="16:9">Landscape (16:9) - YouTube</MenuItem>
                    <MenuItem value="1:1">Square (1:1) - Instagram</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Caption Style</InputLabel>
                  <Select
                    value={formState.captionStyle}
                    label="Caption Style"
                    onChange={(e) => handleFormChange('captionStyle', e.target.value)}
                  >
                    <MenuItem value="viral_bounce">Viral Bounce</MenuItem>
                    <MenuItem value="standard_bottom">Standard Bottom</MenuItem>
                    <MenuItem value="mobile_optimized">Mobile Optimized</MenuItem>
                    <MenuItem value="highlight_words">Highlight Words</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12}>
                <Typography gutterBottom>Max Duration (seconds)</Typography>
                <Slider
                  value={formState.maxDuration}
                  onChange={(_, value) => handleFormChange('maxDuration', value as number)}
                  min={30}
                  max={300}
                  step={15}
                  marks={[
                    { value: 30, label: '30s' },
                    { value: 60, label: '1m' },
                    { value: 120, label: '2m' },
                    { value: 180, label: '3m' },
                    { value: 300, label: '5m' }
                  ]}
                  valueLabelDisplay="on"
                />
              </Grid>
              
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formState.enableCaptions}
                        onChange={(e) => handleFormChange('enableCaptions', e.target.checked)}
                      />
                    }
                    label="Enable Captions"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formState.enableBackgroundMusic}
                        onChange={(e) => handleFormChange('enableBackgroundMusic', e.target.checked)}
                      />
                    }
                    label="Background Music"
                  />
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {/* Voice Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}>
            <VoiceSelector
              voiceProvider={formState.voiceProvider}
              voiceName={formState.voiceName}
              language={formState.language}
              voices={voices}
              onVoiceProviderChange={(provider: string) => handleFormChange('voiceProvider', provider)}
              onVoiceNameChange={(name: string) => handleFormChange('voiceName', name)}
              onLanguageChange={(lang: string) => handleFormChange('language', lang)}
            />
          </Paper>

          {/* Generate Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<TrendingIcon />}
              onClick={handleAutoTopicGenerate}
              disabled={loading}
              sx={{
                backgroundColor: '#f59e0b',
                '&:hover': { backgroundColor: '#d97706' },
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              Auto-Discover & Create {formState.videoMethod === 'stock' ? 'Stock' : 'AI'} Video
            </Button>
          </Box>

          {/* How it Works */}
          <Paper elevation={0} sx={{ border: '1px solid #e5e7eb', borderRadius: 2, p: 3, backgroundColor: '#f9fafb' }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
              🎯 What happens when you click "Auto-Discover"?
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    1. Topic Discovery
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    AI finds trending {selectedScriptType?.label.toLowerCase()} topics
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    2. Script Generation
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Creates engaging script from trending topic
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    3. Visual Creation
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Generates {formState.videoMethod === 'stock' ? 'stock footage' : 'AI images'}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} md={3}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                    4. Final Video
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Combines everything into viral-ready video
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Paper>
        </Box>
      )}
    </Box>
  );
};

export default AutoTopicTab;