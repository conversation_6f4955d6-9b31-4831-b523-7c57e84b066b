import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Paper,
  Grid,
  Chip,
} from '@mui/material';
import {
  Search as ResearchIcon,
  AutoAwesome as AIIcon,
  TrendingUp as TrendingIcon,
} from '@mui/icons-material';

import { useTopicResearch, useVoices } from '../../../hooks/useContentCreation';
import {
  TopicResearchFormState,
  TopicResearchRequest,
  RESEARCH_DEPTHS,
  CONTENT_STYLES,
} from '../../../types/contentCreation';

import VoiceSelector from '../../settings/VoiceSelectorSettings';
import { JobStatusDisplay } from '../index';

const TopicResearchTab: React.FC = () => {
  const {
    result,
    jobStatus,
    jobProgress,
    loading,
    error,
    researchTopic,
    resetState,
    setError
  } = useTopicResearch();

  const {
    voices,
    fetchVoices,
    loading: voicesLoading
  } = useVoices();

  const [formState, setFormState] = useState<TopicResearchFormState>({
    topic: '',
    language: 'en',
    videoLength: 60,
    voiceProvider: 'kokoro',
    voiceName: 'af_bella',
    researchDepth: 'detailed',
    targetAudience: '',
    contentStyle: 'educational',
  });

  const [topicSuggestions] = useState([
    'Climate Change Impact',
    'AI in Healthcare',
    'Space Exploration 2024',
    'Sustainable Technology',
    'Mental Health Awareness',
    'Cryptocurrency Trends',
    'Remote Work Future',
    'Renewable Energy Solutions'
  ]);

  useEffect(() => {
    fetchVoices();
  }, [fetchVoices]);

  const handleFormChange = <K extends keyof TopicResearchFormState>(
    field: K,
    value: TopicResearchFormState[K]
  ) => {
    setFormState(prev => ({ ...prev, [field]: value }));
  };

  const handleResearchTopic = async () => {
    if (!formState.topic.trim()) {
      setError('Please enter a topic to research.');
      return;
    }

    const request: TopicResearchRequest = {
      topic: formState.topic.trim(),
      language: formState.language,
      video_length: formState.videoLength,
      voice_provider: formState.voiceProvider,
      voice_name: formState.voiceName,
      research_depth: formState.researchDepth,
      target_audience: formState.targetAudience || undefined,
      content_style: formState.contentStyle,
    };

    try {
      await researchTopic(request);
    } catch (err) {
      console.error('Failed to start topic research:', err);
    }
  };

  const handleTopicSuggestionClick = (suggestion: string) => {
    handleFormChange('topic', suggestion);
  };

  const handleReset = () => {
    resetState();
    setFormState({
      topic: '',
      language: 'en',
      videoLength: 60,
      voiceProvider: 'kokoro',
      voiceName: 'af_bella',
      researchDepth: 'detailed',
      targetAudience: '',
      contentStyle: 'educational',
    });
  };

  if (voicesLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading voices...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h5" sx={{ fontWeight: 600, mb: 1 }}>
          AI Topic Research & Video Creation
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Let AI research any topic and automatically create an engaging video with script generation.
        </Typography>
      </Box>

      {/* Error Display */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Job Status */}
      {(loading || jobStatus) && (
        <JobStatusDisplay
          loading={loading}
          jobStatus={jobStatus}
          jobProgress={jobProgress}
          result={result}
          onReset={handleReset}
        />
      )}

      {/* Form Content */}
      {!loading && !jobStatus && (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
          {/* Topic Input */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2, p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <AIIcon color="primary" />
              Topic Research
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Research Topic"
                  fullWidth
                  variant="outlined"
                  value={formState.topic}
                  onChange={(e) => handleFormChange('topic', e.target.value)}
                  placeholder="e.g., The future of artificial intelligence, Climate change solutions, Space exploration..."
                  multiline
                  minRows={2}
                  maxRows={4}
                  helperText="Describe the topic you want to research and create a video about"
                />
              </Grid>

              {/* Topic Suggestions */}
              <Grid item xs={12}>
                <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                  Popular Topics:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {topicSuggestions.map((suggestion) => (
                    <Chip
                      key={suggestion}
                      label={suggestion}
                      onClick={() => handleTopicSuggestionClick(suggestion)}
                      variant="outlined"
                      size="small"
                      icon={<TrendingIcon />}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { backgroundColor: 'primary.light', color: 'white' }
                      }}
                    />
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  label="Target Audience (Optional)"
                  fullWidth
                  variant="outlined"
                  value={formState.targetAudience}
                  onChange={(e) => handleFormChange('targetAudience', e.target.value)}
                  placeholder="e.g., Students, Professionals, General audience..."
                  helperText="Who is this video for?"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <TextField
                  label="Video Length (seconds)"
                  type="number"
                  fullWidth
                  variant="outlined"
                  value={formState.videoLength}
                  onChange={(e) => handleFormChange('videoLength', parseInt(e.target.value) || 60)}
                  inputProps={{ min: 30, max: 300 }}
                  helperText="30-300 seconds recommended"
                />
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Research Depth</InputLabel>
                  <Select
                    value={formState.researchDepth}
                    onChange={(e) => handleFormChange('researchDepth', e.target.value as typeof formState.researchDepth)}
                    label="Research Depth"
                  >
                    {RESEARCH_DEPTHS.map((depth) => (
                      <MenuItem key={depth} value={depth}>
                        {depth.charAt(0).toUpperCase() + depth.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>

              <Grid item xs={12} md={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>Content Style</InputLabel>
                  <Select
                    value={formState.contentStyle}
                    onChange={(e) => handleFormChange('contentStyle', e.target.value as typeof formState.contentStyle)}
                    label="Content Style"
                  >
                    {CONTENT_STYLES.map((style) => (
                      <MenuItem key={style} value={style}>
                        {style.charAt(0).toUpperCase() + style.slice(1)}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* Voice Settings */}
          <Paper elevation={0} sx={{ border: '1px solid #e2e8f0', borderRadius: 2 }}>
            <VoiceSelector
              voiceProvider={formState.voiceProvider}
              voiceName={formState.voiceName}
              language={formState.language}
              voices={voices}
              onVoiceProviderChange={(provider) => handleFormChange('voiceProvider', provider)}
              onVoiceNameChange={(name) => handleFormChange('voiceName', name)}
              onLanguageChange={(lang) => handleFormChange('language', lang)}
            />
          </Paper>

          {/* Research Button */}
          <Box sx={{ display: 'flex', justifyContent: 'center', pt: 2 }}>
            <Button
              variant="contained"
              size="large"
              startIcon={<ResearchIcon />}
              onClick={handleResearchTopic}
              disabled={loading || !formState.topic.trim()}
              sx={{
                backgroundColor: '#10b981',
                '&:hover': { backgroundColor: '#059669' },
                borderRadius: 2,
                px: 4,
                py: 1.5,
                fontSize: '1.1rem',
                fontWeight: 600,
              }}
            >
              Research & Create Video
            </Button>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default TopicResearchTab;