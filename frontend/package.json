{"name": "ouinhi-frontend", "version": "1.0.0", "description": "React frontend for Ouinhi API video creation platform", "main": "index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/lab": "^5.0.0-alpha.158", "@mui/material": "^5.15.10", "@mui/system": "^5.15.10", "@mui/x-date-pickers": "^8.9.0", "@tanstack/react-query": "^5.18.0", "axios": "^1.9.0", "date-fns": "^4.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.5.3"}, "devDependencies": {"@types/babel__core": "^7.20.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.8.3", "vite": "^6.3.4"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/ouinhi.git"}, "keywords": ["react", "video", "ai", "content-creation", "ou<PERSON>hi", "material-ui"], "author": "Ouinhi Team", "license": "MIT"}