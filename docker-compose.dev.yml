version: "3.8"

# Development override - use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  api:
    build:
      target: development
    volumes:
      # Mount source code for hot reload (backend)
      - ./app:/app/app:ro
      # Mount frontend source for development builds
      - ./frontend:/app/frontend:ro
    environment:
      - DEBUG=true
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload --reload-dir /app/app