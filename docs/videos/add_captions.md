# Video Captions

Add advanced captions to videos with comprehensive styling, positioning, and animation effects inspired by modern social media platforms.

## Overview

The Video Captions API provides powerful captioning capabilities with support for multiple styles, precise positioning, text replacements, and modern visual effects. Perfect for creating engaging social media content, professional presentations, and accessible video content.

## Features

- **🎨 Multiple Caption Styles**: Classic, karaoke, highlight, underline, word-by-word, bounce, viral bounce, viral cyan, viral yellow, viral green, modern neon, cinematic glow, social pop, typewriter, fade-in
- **📍 Flexible Positioning**: 9-grid positioning system plus custom X,Y coordinates
- **🎭 Advanced Styling**: Font customization, colors, outlines, shadows, gradients, glow effects
- **🔄 Text Processing**: Find/replace rules, auto-capitalization, language support
- **⚡ Smart Transcription**: Auto-generate captions from video audio with Whisper
- **🎬 Modern Effects**: TikTok-style animations, bouncing text, character reveals
- **🌐 Multi-language**: Support for multiple languages with auto-detection

## Quick Start

### Basic Caption Addition

```bash
curl -X POST "http://localhost:8000/v1/videos/add-captions" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://s3.amazonaws.com/bucket/video.mp4",
    "captions": "Welcome to our amazing product demo!",
    "caption_properties": {
      "style": "viral_bounce",
      "font_size": 48,
      "line_color": "white",
      "word_color": "yellow",
      "position": "bottom_center"
    }
  }'
```

**Response:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### Check Job Status

```bash
curl "http://localhost:8000/v1/videos/add-captions/550e8400-e29b-41d4-a716-446655440000" \
  -H "X-API-Key: your_api_key"
```

**Response (when completed):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "result": {
    "url": "https://s3.amazonaws.com/bucket/captioned_video.mp4",
    "path": "videos/captioned_12345.mp4",
    "duration": 30.5,
    "width": 1920,
    "height": 1080,
    "srt_url": "https://s3.amazonaws.com/bucket/captions.srt"
  }
}
```

## API Reference

### POST `/v1/videos/add-captions`

Add captions to a video with advanced styling and positioning options.

#### Request Body

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `video_url` | string | **required** | URL of the video to add captions to |
| `captions` | string | `null` | Caption text, SRT/ASS file URL, or null for auto-transcription |
| `caption_properties` | object | `null` | Advanced styling properties |
| `replace` | array | `[]` | Text replacement rules |
| `language` | string | `"auto"` | Language for transcription |

#### Caption Properties

##### Basic Styling
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `style` | string | `"classic"` | Caption style: `classic`, `karaoke`, `highlight`, `underline`, `word_by_word`, `bounce`, `viral_bounce`, `viral_cyan`, `viral_yellow`, `viral_green`, `modern_neon`, `cinematic_glow`, `social_pop`, `typewriter`, `fade_in` |
| `font_family` | string | `"Arial"` | Font family name |
| `font_size` | integer | `48` | Font size in pixels |
| `line_color` | string | `"white"` | Main text color |
| `word_color` | string | `"yellow"` | Highlight/accent color |
| `outline_color` | string | `"black"` | Text outline color |
| `highlight_color` | string | `null` | Custom highlight color for viral styles |

##### Text Formatting
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `bold` | boolean | `false` | Apply bold formatting |
| `italic` | boolean | `false` | Apply italic formatting |
| `underline` | boolean | `false` | Apply underline formatting |
| `strikeout` | boolean | `false` | Apply strikeout formatting |
| `all_caps` | boolean | `false` | Convert text to uppercase |
| `max_words_per_line` | integer | `10` | Maximum words per caption line (1-20) |

##### Positioning
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `position` | string | `"bottom_center"` | Predefined position: `bottom_left`, `bottom_center`, `bottom_right`, `middle_left`, `middle_center`, `middle_right`, `top_left`, `top_center`, `top_right` |
| `caption_position` | string | `"center"` | Caption position: `top`, `center`, `bottom` |
| `alignment` | string | `"center"` | Text alignment: `left`, `center`, `right` |
| `x` | integer | `null` | Custom X coordinate (overrides position) |
| `y` | integer | `null` | Custom Y coordinate (overrides position) |

##### Visual Effects
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `outline_width` | integer | `2` | Outline width in pixels |
| `shadow_offset` | integer | `1` | Shadow offset in pixels |
| `spacing` | integer | `0` | Letter spacing in pixels |
| `angle` | integer | `0` | Text rotation angle in degrees |

##### Background
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `background_color` | string | `null` | Background color |
| `background_opacity` | float | `0.8` | Background opacity (0.0-1.0) |
| `background_padding` | integer | `10` | Background padding in pixels |
| `background_radius` | integer | `0` | Background corner radius |

##### Modern Effects
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `gradient_colors` | array | `null` | Array of colors for gradient effect |
| `glow_effect` | boolean | `false` | Enable neon glow effect |
| `glow_color` | string | `"#00FFFF"` | Glow color |
| `glow_intensity` | float | `1.0` | Glow intensity (0.0-2.0) |

##### Animation Controls
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `animation_speed` | float | `1.0` | Animation speed multiplier (0.1-3.0) |
| `bounce_intensity` | float | `1.0` | Bounce effect intensity (0.1-2.0) |
| `typewriter_speed` | float | `2.0` | Typewriter effect speed (0.1-5.0) |

##### AI Features
| Property | Type | Default | Description |
|----------|------|---------|-------------|
| `auto_emoji` | boolean | `false` | Auto-replace words with emojis |
| `auto_capitalization` | boolean | `false` | Smart emphasis capitalization |
| `confidence_styling` | boolean | `false` | Style based on transcription confidence |

#### Text Replacements

The `replace` parameter accepts an array of replacement rules:

```json
{
  "replace": [
    {"find": "um", "replace": ""},
    {"find": "AI", "replace": "Artificial Intelligence"},
    {"find": "API", "replace": "Application Programming Interface"}
  ]
}
```

### GET `/v1/videos/add-captions/{job_id}`

Get the status and results of a video captioning job.

#### Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `url` | string | URL to the captioned video |
| `path` | string | S3 storage path |
| `duration` | float | Video duration in seconds |
| `width` | integer | Video width in pixels |
| `height` | integer | Video height in pixels |
| `srt_url` | string | URL to SRT subtitle file (if available) |

## Caption Styles Guide

### Classic
Standard captions with full styling support but no special effects.

```json
{
  "style": "classic",
  "font_size": 48,
  "line_color": "white",
  "outline_color": "black",
  "position": "bottom_center"
}
```

### Karaoke
Word-by-word highlighting with smooth transitions.

```json
{
  "style": "karaoke",
  "font_size": 56,
  "line_color": "white",
  "word_color": "yellow",
  "position": "bottom_center"
}
```

### Highlight
Sequential word highlighting that builds up text.

```json
{
  "style": "highlight",
  "font_size": 52,
  "line_color": "white",
  "word_color": "cyan",
  "outline_width": 3
}
```

### Bounce & Viral Bounce
TikTok-style bouncing text effects with customizable colors.

```json
{
  "style": "viral_bounce",
  "font_size": 60,
  "line_color": "white",
  "highlight_color": "#00FFFF",
  "glow_effect": true,
  "glow_color": "#00FFFF",
  "bounce_intensity": 1.5,
  "animation_speed": 1.2,
  "caption_position": "center"
}
```

### Viral Color Styles
Predefined viral styles with optimized color schemes:

#### Viral Cyan
```json
{
  "style": "viral_cyan",
  "font_size": 64,
  "line_color": "white",
  "highlight_color": "#00FFFF",
  "glow_effect": true,
  "glow_color": "#00FFFF",
  "bounce_intensity": 1.8,
  "caption_position": "center",
  "all_caps": true
}
```

#### Viral Yellow
```json
{
  "style": "viral_yellow",
  "font_size": 64,
  "line_color": "white",
  "highlight_color": "#FFFF00",
  "glow_effect": true,
  "glow_color": "#FFFF00",
  "bounce_intensity": 1.8,
  "caption_position": "center",
  "all_caps": true
}
```

#### Viral Green
```json
{
  "style": "viral_green",
  "font_size": 64,
  "line_color": "white",
  "highlight_color": "#00FF00",
  "glow_effect": true,
  "glow_color": "#00FF00",
  "bounce_intensity": 1.8,
  "caption_position": "center",
  "all_caps": true
}
```

### Modern Styles
Contemporary caption styles for professional content:

#### Modern Neon
```json
{
  "style": "modern_neon",
  "font_size": 56,
  "line_color": "white",
  "highlight_color": "#FF0080",
  "glow_effect": true,
  "glow_intensity": 1.5,
  "caption_position": "center"
}
```

#### Cinematic Glow
```json
{
  "style": "cinematic_glow",
  "font_size": 52,
  "line_color": "white",
  "highlight_color": "#FFD700",
  "glow_effect": true,
  "glow_color": "#FFD700",
  "glow_intensity": 1.2,
  "caption_position": "bottom"
}
```

#### Social Pop
```json
{
  "style": "social_pop",
  "font_size": 60,
  "line_color": "white",
  "highlight_color": "#FF4500",
  "bounce_intensity": 1.3,
  "caption_position": "center",
  "all_caps": true
}
```

### Typewriter
Character-by-character text reveal.

```json
{
  "style": "typewriter",
  "font_size": 48,
  "line_color": "lime",
  "typewriter_speed": 3.0,
  "position": "middle_center"
}
```

### Fade In
Gradual opacity animation for smooth appearance.

```json
{
  "style": "fade_in",
  "font_size": 50,
  "line_color": "white",
  "background_color": "black",
  "background_opacity": 0.7
}
```

## Advanced Examples

### Social Media Content

```json
{
  "video_url": "https://s3.amazonaws.com/bucket/tiktok_video.mp4",
  "captions": null,
  "caption_properties": {
    "style": "viral_cyan",
    "font_family": "Arial Black",
    "font_size": 64,
    "bold": true,
    "highlight_color": "#00FFFF",
    "glow_effect": true,
    "glow_color": "#00FFFF",
    "glow_intensity": 1.8,
    "caption_position": "center",
    "bounce_intensity": 2.0,
    "animation_speed": 1.3,
    "max_words_per_line": 4,
    "all_caps": true
  },
  "replace": [
    {"find": "awesome", "replace": "AMAZING 🔥"},
    {"find": "great", "replace": "INCREDIBLE ✨"}
  ],
  "language": "en"
}
```

### Professional Presentation

```json
{
  "video_url": "https://s3.amazonaws.com/bucket/presentation.mp4",
  "captions": "Key findings from our quarterly analysis",
  "caption_properties": {
    "style": "fade_in",
    "font_family": "Roboto",
    "font_size": 42,
    "line_color": "white",
    "background_color": "black",
    "background_opacity": 0.8,
    "background_padding": 15,
    "background_radius": 10,
    "position": "bottom_center",
    "alignment": "center",
    "max_words_per_line": 8
  }
}
```

### Multi-language Content

```json
{
  "video_url": "https://s3.amazonaws.com/bucket/spanish_video.mp4",
  "captions": null,
  "caption_properties": {
    "style": "highlight",
    "font_family": "Open Sans",
    "font_size": 48,
    "line_color": "white",
    "word_color": "yellow",
    "outline_color": "black",
    "outline_width": 2,
    "position": "bottom_center",
    "max_words_per_line": 6
  },
  "replace": [
    {"find": "eh", "replace": ""},
    {"find": "um", "replace": ""}
  ],
  "language": "es"
}
```

### Custom Positioning

```json
{
  "video_url": "https://s3.amazonaws.com/bucket/video.mp4",
  "captions": "Custom positioned caption",
  "caption_properties": {
    "style": "classic",
    "font_size": 40,
    "line_color": "cyan",
    "outline_color": "black",
    "x": 100,
    "y": 200,
    "alignment": "left"
  }
}
```

## Integration Examples

### Python: Automated Caption Processing

```python
import requests
import time

class VideoCaptionProcessor:
    def __init__(self, api_key, base_url="http://localhost:8000"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {"X-API-Key": api_key}
    
    def add_captions(self, video_url, config=None):
        """Add captions to a video with custom configuration."""
        config = config or {}
        
        payload = {
            "video_url": video_url,
            **config
        }
        
        # Create job
        response = requests.post(
            f"{self.base_url}/v1/videos/add-captions",
            headers=self.headers,
            json=payload
        )
        job_id = response.json()["job_id"]
        
        # Poll for completion
        while True:
            status_response = requests.get(
                f"{self.base_url}/v1/videos/add-captions/{job_id}",
                headers=self.headers
            )
            status = status_response.json()
            
            if status["status"] == "completed":
                return status["result"]
            elif status["status"] == "failed":
                raise Exception(status["error"])
            
            time.sleep(3)
    
    def create_social_media_captions(self, video_url, style="viral_bounce"):
        """Create engaging captions for social media."""
        return self.add_captions(video_url, {
            "captions": None,  # Auto-transcribe
            "caption_properties": {
                "style": style,
                "font_family": "Arial Black",
                "font_size": 64,
                "bold": True,
                "line_color": "white",
                "word_color": "hotpink",
                "glow_effect": True,
                "glow_color": "#FF0080",
                "bounce_intensity": 1.8,
                "position": "bottom_center",
                "max_words_per_line": 4,
                "all_caps": True
            },
            "replace": [
                {"find": "um", "replace": ""},
                {"find": "uh", "replace": ""},
                {"find": "amazing", "replace": "AMAZING 🔥"}
            ]
        })
    
    def create_professional_captions(self, video_url, caption_text=None):
        """Create professional-style captions."""
        return self.add_captions(video_url, {
            "captions": caption_text,
            "caption_properties": {
                "style": "fade_in",
                "font_family": "Roboto",
                "font_size": 42,
                "line_color": "white",
                "background_color": "black",
                "background_opacity": 0.8,
                "background_padding": 15,
                "background_radius": 8,
                "position": "bottom_center",
                "max_words_per_line": 8
            }
        })

# Usage
processor = VideoCaptionProcessor("your_api_key")

# Social media style
social_result = processor.create_social_media_captions(
    "https://s3.amazonaws.com/bucket/tiktok_video.mp4"
)

# Professional style
professional_result = processor.create_professional_captions(
    "https://s3.amazonaws.com/bucket/presentation.mp4",
    "Welcome to our quarterly review"
)

print(f"Social media video: {social_result['url']}")
print(f"Professional video: {professional_result['url']}")
```

### JavaScript: Real-time Caption Preview

```javascript
class VideoCaptionAPI {
  constructor(apiKey, baseUrl = 'http://localhost:8000') {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.headers = {
      'X-API-Key': apiKey,
      'Content-Type': 'application/json'
    };
  }

  async addCaptions(videoUrl, config = {}) {
    // Create job
    const response = await fetch(`${this.baseUrl}/v1/videos/add-captions`, {
      method: 'POST',
      headers: this.headers,
      body: JSON.stringify({ video_url: videoUrl, ...config })
    });
    
    const { job_id } = await response.json();
    
    // Poll for completion
    while (true) {
      const statusResponse = await fetch(
        `${this.baseUrl}/v1/videos/add-captions/${job_id}`,
        { headers: { 'X-API-Key': this.apiKey } }
      );
      
      const status = await statusResponse.json();
      
      if (status.status === 'completed') {
        return status.result;
      } else if (status.status === 'failed') {
        throw new Error(status.error);
      }
      
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }

  createTikTokStyle(videoUrl, customText = null) {
    return this.addCaptions(videoUrl, {
      captions: customText,
      caption_properties: {
        style: 'viral_bounce',
        font_family: 'Arial Black',
        font_size: 72,
        bold: true,
        gradient_colors: ['#FF0080', '#00FFFF', '#FFFF00'],
        glow_effect: true,
        glow_intensity: 2.0,
        bounce_intensity: 2.0,
        position: 'bottom_center',
        max_words_per_line: 3,
        all_caps: true
      },
      replace: [
        { find: 'awesome', replace: 'AWESOME 🔥' },
        { find: 'cool', replace: 'COOL ✨' },
        { find: 'amazing', replace: 'AMAZING 🚀' }
      ]
    });
  }

  createKaraokeStyle(videoUrl, customText = null) {
    return this.addCaptions(videoUrl, {
      captions: customText,
      caption_properties: {
        style: 'karaoke',
        font_family: 'Comic Sans MS',
        font_size: 58,
        line_color: 'white',
        word_color: 'yellow',
        outline_color: 'purple',
        outline_width: 3,
        position: 'bottom_center',
        max_words_per_line: 6
      }
    });
  }
}

// Usage
const captionAPI = new VideoCaptionAPI('your_api_key');

document.getElementById('processTikTok').addEventListener('click', async () => {
  const videoUrl = document.getElementById('videoUrl').value;
  
  try {
    document.getElementById('status').textContent = 'Creating TikTok-style captions...';
    
    const result = await captionAPI.createTikTokStyle(videoUrl);
    
    document.getElementById('result').innerHTML = `
      <h3>TikTok-Style Video Ready!</h3>
      <video controls width="400">
        <source src="${result.url}" type="video/mp4">
      </video>
      <p>Duration: ${result.duration}s | Size: ${result.width}x${result.height}</p>
    `;
    
  } catch (error) {
    document.getElementById('status').textContent = `Error: ${error.message}`;
  }
});
```

### React: Caption Style Selector

```jsx
import React, { useState } from 'react';

function CaptionStyleSelector({ videoUrl, onCaptionComplete }) {
  const [selectedStyle, setSelectedStyle] = useState('viral_bounce');
  const [customText, setCustomText] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState(null);

  const stylePresets = {
    classic: {
      name: 'Classic',
      description: 'Traditional subtitles',
      properties: {
        style: 'classic',
        font_size: 48,
        line_color: 'white',
        outline_color: 'black',
        position: 'bottom_center'
      }
    },
    viral_bounce: {
      name: 'Viral Bounce',
      description: 'TikTok-style bouncing text',
      properties: {
        style: 'viral_bounce',
        font_size: 64,
        bold: true,
        highlight_color: '#00FFFF',
        glow_effect: true,
        glow_color: '#00FFFF',
        bounce_intensity: 2.0,
        caption_position: 'center',
        max_words_per_line: 4,
        all_caps: true
      }
    },
    viral_cyan: {
      name: 'Viral Cyan',
      description: 'Cyan viral effect with glow',
      properties: {
        style: 'viral_cyan',
        font_size: 64,
        bold: true,
        highlight_color: '#00FFFF',
        glow_effect: true,
        glow_color: '#00FFFF',
        bounce_intensity: 1.8,
        caption_position: 'center',
        all_caps: true
      }
    },
    viral_yellow: {
      name: 'Viral Yellow',
      description: 'Yellow viral effect with bounce',
      properties: {
        style: 'viral_yellow',
        font_size: 64,
        bold: true,
        highlight_color: '#FFFF00',
        glow_effect: true,
        glow_color: '#FFFF00',
        bounce_intensity: 1.8,
        caption_position: 'center',
        all_caps: true
      }
    },
    modern_neon: {
      name: 'Modern Neon',
      description: 'Professional neon glow effect',
      properties: {
        style: 'modern_neon',
        font_size: 56,
        line_color: 'white',
        highlight_color: '#FF0080',
        glow_effect: true,
        glow_intensity: 1.5,
        caption_position: 'center'
      }
    },
    karaoke: {
      name: 'Karaoke',
      description: 'Word-by-word highlighting',
      properties: {
        style: 'karaoke',
        font_size: 56,
        line_color: 'white',
        word_color: 'yellow',
        outline_color: 'purple',
        position: 'bottom_center'
      }
    },
    typewriter: {
      name: 'Typewriter',
      description: 'Character-by-character reveal',
      properties: {
        style: 'typewriter',
        font_size: 48,
        line_color: 'lime',
        typewriter_speed: 3.0,
        position: 'middle_center'
      }
    }
  };

  const processVideo = async () => {
    setIsProcessing(true);
    
    try {
      const captionAPI = new VideoCaptionAPI('your_api_key');
      
      const config = {
        captions: customText || null,
        caption_properties: stylePresets[selectedStyle].properties
      };
      
      const result = await captionAPI.addCaptions(videoUrl, config);
      
      setResult(result);
      if (onCaptionComplete) {
        onCaptionComplete(result);
      }
      
    } catch (error) {
      console.error('Caption processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="caption-style-selector">
      <h3>Choose Caption Style</h3>
      
      <div className="style-grid">
        {Object.entries(stylePresets).map(([key, preset]) => (
          <div
            key={key}
            className={`style-card ${selectedStyle === key ? 'selected' : ''}`}
            onClick={() => setSelectedStyle(key)}
          >
            <h4>{preset.name}</h4>
            <p>{preset.description}</p>
          </div>
        ))}
      </div>

      <div className="custom-text">
        <label>
          Custom Caption Text (leave empty for auto-transcription):
          <textarea
            value={customText}
            onChange={(e) => setCustomText(e.target.value)}
            placeholder="Enter your caption text..."
            rows={3}
          />
        </label>
      </div>

      <button 
        onClick={processVideo}
        disabled={!videoUrl || isProcessing}
        className="process-button"
      >
        {isProcessing ? 'Processing...' : 'Add Captions'}
      </button>

      {result && (
        <div className="result">
          <h4>Caption Processing Complete!</h4>
          <video controls width="400">
            <source src={result.url} type="video/mp4" />
          </video>
          <div className="result-info">
            <p>Duration: {result.duration}s</p>
            <p>Resolution: {result.width}x{result.height}</p>
            {result.srt_url && (
              <a href={result.srt_url} download>Download SRT File</a>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default CaptionStyleSelector;
```

## Best Practices

### Style Selection

1. **Social Media**: Use `viral_bounce` or `bounce` with bright colors
2. **Professional**: Use `fade_in` or `classic` with subtle styling
3. **Educational**: Use `highlight` for emphasis on key terms
4. **Entertainment**: Use `karaoke` for sing-along content

### Text Optimization

1. **Reading Speed**: Keep captions visible for 2-4 seconds
2. **Word Count**: 4-8 words per line for readability
3. **Timing**: Use word-level timing for precision
4. **Language**: Specify language for better transcription accuracy

### Visual Design

1. **Contrast**: Ensure text stands out against video background
2. **Size**: Use larger fonts (60px+) for mobile viewing
3. **Position**: Bottom-center is most accessible
4. **Effects**: Use sparingly for professional content

### Performance

1. **Video Length**: Keep videos under 5 minutes for faster processing
2. **Resolution**: Higher resolution videos take longer to process
3. **Style Complexity**: Simple styles process faster than animated ones
4. **Audio Quality**: Better audio improves auto-transcription accuracy

## Troubleshooting

### Common Issues

**Slow Processing:**
- Use simpler caption styles for faster processing
- Reduce video resolution if possible
- Check video file accessibility

**Poor Transcription Quality:**
- Ensure good audio quality in source video
- Specify the correct language parameter
- Consider providing manual caption text

**Styling Not Applied:**
- Verify font names are system-available
- Check color format (hex, named colors, or rgb())
- Ensure positioning values are within valid ranges

**Text Replacement Issues:**
- Use exact text matching in find/replace rules
- Consider case sensitivity in replacements
- Test with simple replacements first

### Quality Tips

```python
def optimize_caption_config(video_duration, target_platform):
    """Optimize caption configuration based on video characteristics."""
    
    config = {
        "caption_properties": {},
        "replace": []
    }
    
    if target_platform == "tiktok":
        config["caption_properties"] = {
            "style": "viral_cyan",
            "font_size": 72,
            "bold": True,
            "highlight_color": "#00FFFF",
            "glow_effect": True,
            "glow_color": "#00FFFF",
            "caption_position": "center",
            "max_words_per_line": 3,
            "all_caps": True
        }
    elif target_platform == "youtube":
        config["caption_properties"] = {
            "style": "classic",
            "font_size": 48,
            "line_color": "white",
            "background_color": "black",
            "background_opacity": 0.8,
            "position": "bottom_center",
            "max_words_per_line": 8
        }
    elif target_platform == "instagram":
        config["caption_properties"] = {
            "style": "highlight",
            "font_size": 56,
            "line_color": "white",
            "word_color": "hotpink",
            "outline_width": 3,
            "position": "bottom_center",
            "max_words_per_line": 5
        }
    
    # Add common text cleaning rules
    config["replace"] = [
        {"find": "um", "replace": ""},
        {"find": "uh", "replace": ""},
        {"find": "  ", "replace": " "}  # Remove double spaces
    ]
    
    return config
```

## Next Steps

- Combine with [Video Generation](./generations.md) for complete video creation
- Use with [Text Overlay](./text_overlay.md) for additional text elements
- Integrate with [Topic-to-Video Pipeline](../ai-videos/footage-to-video-pipeline.md)
- Export SRT files for use in video editing software

---

*For more video processing capabilities, see the [Videos Documentation](./README.md).*