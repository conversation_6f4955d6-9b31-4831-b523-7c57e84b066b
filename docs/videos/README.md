# Video Routes Documentation

This section documents all video-related endpoints provided by the Media Master API following OpenAI conventions.

## Available Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| [/v1/videos/generations](./generations.md) | POST | Generate videos from images with animation effects |
| [/v1/videos/generations/{job_id}](./generations.md#get-job-status) | GET | Get the status of a video generation job |
| [/v1/videos/generate](./ltx_generations.md) | POST | Generate videos from text prompts using LTX-Video |
| [/v1/videos/generate/{job_id}](./ltx_generations.md#get-text-to-video-job-status) | GET | Get the status of a text-to-video generation job |
| [/v1/videos/from_image](./ltx_generations.md) | POST | Generate videos from images with text prompts using LTX-Video |
| [/v1/videos/from_image/{job_id}](./ltx_generations.md#get-image-to-video-job-status) | GET | Get the status of an image-to-video generation job |
| [/v1/videos/wavespeed/generate](./wavespeed_generations.md) | POST | Generate videos from text prompts using WaveSpeedAI |
| [/v1/videos/wavespeed/generate/{job_id}](./wavespeed_generations.md#get-text-to-video-job-status) | GET | Get the status of a WaveSpeedAI text-to-video generation job |
| [/v1/videos/wavespeed/image_to_video](./wavespeed_generations.md) | POST | Generate videos from images using WaveSpeedAI motion synthesis |
| [/v1/videos/wavespeed/image_to_video/{job_id}](./wavespeed_generations.md#get-image-to-video-job-status) | GET | Get the status of a WaveSpeedAI image-to-video generation job |
| [/v1/videos/edit](./edit.md) | POST | Edit videos by overlaying videos on base images |
| [/v1/videos/edit/{job_id}](./edit.md#get-job-status) | GET | Get the status of a video edit job |
| [/v1/videos/concatenate](./concatenate.md) | POST | Concatenate multiple videos into a single video |
| [/v1/videos/concatenate/{job_id}](./concatenate.md#get-job-status) | GET | Get the status of a video concatenation job |
| [/v1/videos/add-audio](./add_audio.md) | POST | Add audio to a video with volume control |
| [/v1/videos/add-audio/{job_id}](./add_audio.md#get-job-status) | GET | Get the status of an add audio job |
| [/v1/videos/merge](./merge.md) | POST | Merge multiple videos with optional background audio |
| [/v1/videos/merge/{job_id}](./merge.md#get-job-status) | GET | Get the status of a video merge job |
| [/v1/videos/add-captions](./add_captions.md) | POST | Add advanced captions with styling and animations |
| [/v1/videos/add-captions/{job_id}](./add_captions.md#get-job-status) | GET | Get the status of a caption job |
| [/v1/videos/text-overlay](./text_overlay.md) | POST | Add customizable text overlays to videos |
| [/v1/videos/text-overlay/{job_id}](./text_overlay.md#get-job-status) | GET | Get the status of a text overlay job |
| [/v1/videos/text-overlay/presets](./text_overlay.md#get-available-presets) | GET | Get available text overlay presets |
| [/v1/videos/text-overlay/preset/{preset_name}](./text_overlay.md#create-text-overlay-with-preset) | POST | Add text overlay using a preset |

## Common Use Cases

### Video Concatenation

The video concatenation endpoint allows you to join multiple video files into a single continuous video. This is useful for:

- Combining multiple video clips into a single file
- Creating compilations from shorter video segments
- Merging different parts of a video that were recorded separately
- Creating sequential video content from separate scenes or shots

### Video Merge

The video merge endpoint combines video concatenation with audio overlay in a single operation. This is useful for:

- Creating complete video productions with background music
- Merging multiple clips with synchronized soundtrack
- Building social media content with smooth transitions and audio
- Creating professional video compilations with audio overlay
- Combining video segments with fade effects and background music
- One-step creation of finished videos from multiple sources

### Audio Addition

The add audio endpoint allows you to add background music or other audio to videos. This is useful for:

- Adding background music to silent videos
- Replacing or enhancing existing audio tracks
- Adding voiceovers to video content
- Creating multimedia presentations with synchronized audio
- Using YouTube audio sources directly with videos

### Video Captions

The video captions endpoint provides advanced captioning with modern styling and animation effects. This is useful for:

- Creating TikTok-style viral content with bouncing text effects
- Adding professional subtitles with precise timing
- Auto-generating captions from video audio using AI transcription
- Creating karaoke-style word-by-word highlighting
- Adding multi-language caption support with text replacements
- Implementing accessibility features with customizable positioning
- Creating engaging social media content with gradient colors and glow effects

### Text Overlay

The text overlay endpoints allow you to add professional text overlays to videos with advanced styling options. This is useful for:

- Adding titles and headers to video content
- Creating subtitles and captions for accessibility
- Adding watermarks and branding to videos
- Creating engaging social media content with text
- Adding call-to-action messages and alerts
- Using predefined presets for quick, professional results

## Supported Video Formats

The Media Master API supports various video formats for both input and output:

- MP4 (.mp4)
- WebM (.webm)
- AVI (.avi)
- MOV (.mov)
- MKV (.mkv)

## Error Handling

All video endpoints follow standard HTTP status codes:
- 200: Successful operation
- 400: Bad request (invalid parameters)
- 401: Unauthorized (invalid API key)
- 404: Resource not found
- 500: Internal server error

Detailed error messages are provided in the response body. 