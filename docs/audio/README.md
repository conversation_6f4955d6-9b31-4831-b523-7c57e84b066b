# Audio Routes Documentation

This section documents all audio-related endpoints provided by the Media Master API following OpenAI conventions.

## Available Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| [/v1/audio/speech](./speech.md) | POST | Generate speech from text using Kokoro TTS |
| [/v1/audio/speech/{job_id}](./speech.md#get-job-status) | GET | Get the status of a speech generation job |
| [/v1/audio/music](./music.md) | POST | Generate music from text descriptions using MusicGen |
| [/v1/audio/music/{job_id}](./music.md#get-job-status) | GET | Get the status of a music generation job |
| [/v1/audio/music/info](./music.md#get-endpoint-information) | GET | Get information about music generation capabilities |
| [/v1/audio/transcriptions](./transcriptions.md) | POST | Transcribe audio/video content to text |
| [/v1/audio/transcriptions/{job_id}](./transcriptions.md#get-job-status) | GET | Get the status of a transcription job |

## Common Use Cases

### Speech Generation

The speech endpoint lets you convert any text into natural-sounding speech with various voices. This is useful for:

- Adding narration to videos
- Creating audio content for podcasts
- Generating voice-overs for presentations
- Creating accessible content for users with reading difficulties

### Music Generation

The music endpoint allows you to generate original music from text descriptions using AI. This is useful for:

- Creating background music for videos and presentations
- Generating musical ideas and inspiration for composers
- Producing royalty-free music for content creation
- Creating custom soundtracks for games and applications
- Generating music for podcasts and social media content

### Audio Transcription

The transcriptions endpoint converts audio and video content into text. This is useful for:

- Creating captions for videos
- Converting meetings and interviews to text
- Making audio content searchable
- Generating subtitles for accessibility

## Error Handling

All audio endpoints follow standard HTTP status codes:
- 200: Successful operation
- 400: Bad request (invalid parameters)
- 401: Unauthorized (invalid API key)
- 404: Resource not found
- 500: Internal server error

Detailed error messages are provided in the response body. 