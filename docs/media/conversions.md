# Media Conversions

This endpoint provides a universal media conversion service using FFmpeg, supporting a wide range of audio, video, and image formats.

## Get Supported Formats

Get a comprehensive list of all supported conversion formats with codec details and quality presets.

### Endpoint

```
GET /v1/conversions/formats
```

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |

### Response

```json
{
  "object": "formats",
  "supported_formats": {
    "audio": {
      "mp3": {"codec": "libmp3lame", "description": "MP3 Audio"},
      "wav": {"codec": "pcm_s16le", "description": "WAV Audio"}
    },
    "video": {
      "mp4": {"codec": "libx264", "description": "MP4 Video"},
      "webm": {"codec": "libvpx-vp9", "description": "WebM Video"}
    },
    "image": {
      "jpg": {"description": "JPEG Image"},
      "png": {"description": "PNG Image"}
    }
  },
  "quality_presets": ["low", "medium", "high", "lossless"],
  "total_formats": 53
}
```

### Example

#### Request

```bash
cURL -X GET \
  https://localhost:8000/v1/conversions/formats \
  -H 'X-API-Key: your-api-key'
```

## Convert Media

Convert media files between formats using FFmpeg.

### Endpoint

```
POST /v1/conversions/
```

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |
| Content-Type | Yes | multipart/form-data |

### Request Body

This endpoint accepts `multipart/form-data`.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | file | No | Media file to convert (audio, video, or image) - either `file` OR `url` required. |
| url | string | No | URL of media file to convert - either `file` OR `url` required. |
| output_format | string | Yes | Target format for conversion (e.g., `mp3`, `mp4`, `webp`). |
| quality | string | No | Quality preset for conversion (`low`, `medium`, `high`, `lossless`). Defaults to `medium`. |
| custom_options | string | No | Custom FFmpeg options (e.g., `-vf scale=1280:-1`). |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

### Example

#### File Upload Request

```bash
cURL -X POST \
  https://localhost:8000/v1/conversions/ \
  -H 'Content-Type: multipart/form-data' \
  -H 'X-API-Key: your-api-key' \
  -F 'file=@/path/to/your/video.mp4' \
  -F 'output_format=mp3' \
  -F 'quality=high'
```

#### URL Conversion Request

```bash
cURL -X POST \
  https://localhost:8000/v1/conversions/ \
  -H 'Content-Type: application/json' \
  -H 'X-API-Key: your-api-key' \
  -d '{
    "url": "https://example.com/audio.wav",
    "output_format": "mp3",
    "quality": "medium"
  }'
```

#### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

## Get Job Status

Check the status of a media conversion job.

### Endpoint

```
GET /v1/conversions/{job_id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| job_id | string | Yes | ID of the job to get status for |

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "file_url": "https://your-bucket.s3.your-region.amazonaws.com/conversions/converted.mp3"
  },
  "error": null
}
```