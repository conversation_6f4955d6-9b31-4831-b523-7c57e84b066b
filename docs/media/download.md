# Media Download

The media download endpoint allows you to download media from a given URL, including YouTube videos.

## Download Media

Download media from a URL.

### Endpoint

```
POST /v1/media/download
```

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |
| Content-Type | Yes | application/json |

### Request Body

```json
{
  "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
  "file_name": "rick_roll.mp4"
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| url | string | Yes | The URL of the media to download. |
| file_name | string | No | An optional file name. If not provided, yt-dlp will determine the file name. |
| cookies_url | string | No | URL to a Netscape-formatted cookies file for authentication (e.g., for YouTube). |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

### Example

#### Request

```bash
cURL -X POST \
  https://localhost:8000/v1/media/download \
  -H 'Content-Type: application/json' \
  -H 'X-API-Key: your-api-key' \
  -d '{
    "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
    "file_name": "my_downloaded_video.mp4",
    "cookies_url": "https://example.com/my_cookies.txt"
  }'
```

#### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

## Get Job Status

Check the status of a media download job.

### Endpoint

```
GET /v1/media/download/{job_id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| job_id | string | Yes | ID of the job to get status for |

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "file_url": "https://your-bucket.s3.your-region.amazonaws.com/my_downloaded_video.mp4"
  },
  "error": null
}
```