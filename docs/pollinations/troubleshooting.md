# Pollinations.AI Troubleshooting

Common issues and solutions when using the Pollinations.AI integration.

## Quick Diagnostics

### Health Check Endpoint

First, verify your integration is working:

```bash
# Check if endpoints are available
curl -H "X-API-Key: your_api_key" \
  "http://localhost:8000/api/pollinations/models/image"

# Expected: {"models": ["flux", ...]}
```

### Environment Verification

```bash
# Check if API key is set
echo $POLLINATIONS_API_KEY

# Verify Docker services are running
docker-compose ps

# Check logs
docker-compose logs api
```

## Common Issues

### 1. Authentication Problems

#### Issue: "401 Unauthorized" or "403 Forbidden"

**Symptoms:**
```json
{
  "detail": "Invalid API key"
}
```

**Solutions:**

1. **Missing Ouinhi API Key:**
```bash
# ✅ Correct
curl -H "X-API-Key: your_ouinhi_api_key" \
  "http://localhost:8000/api/pollinations/image/generate"

# ❌ Wrong - missing header
curl "http://localhost:8000/api/pollinations/image/generate"
```

2. **Check API Key Configuration:**
```bash
# Verify in environment
grep API_KEY .env

# Should show:
# API_KEY=your_ouinhi_key
# POLLINATIONS_API_KEY=your_pollinations_key
```

3. **API Key Format Issues:**
```bash
# Pollinations API keys typically start with "poll_"
POLLINATIONS_API_KEY=poll_abc123def456...
```

#### Issue: "Pollinations API token found" not appearing in logs

**Diagnosis:**
```bash
# Check if environment variable is loaded
docker-compose exec api python -c "import os; print('API Key:', os.environ.get('POLLINATIONS_API_KEY', 'NOT SET'))"
```

**Solutions:**
1. Add to `.env` file:
```bash
echo "POLLINATIONS_API_KEY=your_key_here" >> .env
```

2. Restart services:
```bash
docker-compose down && docker-compose up
```

### 2. Job Processing Issues

#### Issue: Jobs stuck in "pending" or "processing" status

**Symptoms:**
```json
{
  "job_id": "abc-123",
  "status": "processing",
  "result": null
}
```

**Diagnosis Steps:**

1. **Check Redis Connection:**
```bash
# Test Redis connectivity
docker-compose exec api python -c "
from app.services.redis_service import redis_service
import asyncio
async def test(): 
    await redis_service.set('test', 'value')
    result = await redis_service.get('test')
    print('Redis test:', result)
asyncio.run(test())
"
```

2. **Check Job Queue Status:**
```bash
# View job queue logs
docker-compose logs api | grep -i "job\|queue"

# Check specific job in Redis
docker-compose exec redis redis-cli get "job:abc-123"
```

**Solutions:**

1. **Restart Job Workers:**
```bash
docker-compose restart api
```

2. **Clear Stuck Jobs:**
```python
# In Python console
from app.services.job_queue import job_queue
import asyncio

async def clear_stuck_jobs():
    # Get all pending jobs older than 1 hour
    stuck_jobs = await job_queue.get_stuck_jobs(max_age_hours=1)
    for job_id in stuck_jobs:
        await job_queue.mark_job_failed(job_id, "Job timeout - cleared manually")

asyncio.run(clear_stuck_jobs())
```

3. **Check S3 Configuration:**
```bash
# Test S3 connectivity
docker-compose exec api python -c "
from app.services.s3_service import S3Service
s3 = S3Service()
print('S3 Config:', s3.bucket_name, s3.region)
"
```

#### Issue: Jobs failing with "Pollinations API error"

**Symptoms:**
```json
{
  "status": "failed",
  "error": "Pollinations API error 429: Rate limit exceeded"
}
```

**Solutions by Error Code:**

1. **429 - Rate Limited:**
```python
# Add retry logic with exponential backoff
async def generate_with_retry(prompt, max_retries=3):
    for attempt in range(max_retries):
        try:
            return await pollinations_service.generate_image(prompt)
        except Exception as e:
            if "429" in str(e) and attempt < max_retries - 1:
                wait_time = (2 ** attempt) * 60  # 1, 2, 4 minutes
                await asyncio.sleep(wait_time)
                continue
            raise
```

2. **400 - Bad Request:**
```bash
# Check prompt content and parameters
curl -X POST "http://localhost:8000/api/pollinations/image/generate" \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "test",
    "width": 512,
    "height": 512
  }'
```

3. **500 - Server Error:**
```bash
# Check Pollinations service status
curl -I "https://image.pollinations.ai/models"

# If down, wait and retry
# If up, check your request format
```

### 3. Image Generation Issues

#### Issue: Generated images have Pollinations logo

**Symptoms:**
Images contain watermark even with API key configured.

**Solutions:**

1. **Verify API Key is Active:**
```python
# Check if API key is being used
from app.services.pollinations_service import pollinations_service
print("API Token configured:", bool(pollinations_service.api_token))
```

2. **Explicitly Set nologo Parameter:**
```json
{
  "prompt": "your prompt",
  "nologo": true
}
```

3. **Check Account Status:**
Visit [auth.pollinations.ai](https://auth.pollinations.ai) to verify your account tier.

#### Issue: Poor Image Quality

**Symptoms:**
- Blurry or low-quality images
- Images don't match prompt
- Inconsistent results

**Solutions:**

1. **Improve Prompt Quality:**
```json
{
  "prompt": "A professional photograph of a red apple on a white background, high resolution, studio lighting, macro lens, detailed texture",
  "enhance": true,
  "width": 1024,
  "height": 1024
}
```

2. **Use Appropriate Model:**
```json
{
  "model": "flux",  // Best general-purpose model
  "enhance": true   // Let AI improve your prompt
}
```

3. **Set Quality Parameters:**
```json
{
  "prompt": "your prompt, high quality, detailed, professional",
  "safe": false,     // Only if appropriate
  "enhance": true,   // Improve prompt
  "seed": 42        // For reproducible results
}
```

#### Issue: Transparent Background Not Working

**Symptoms:**
Images have white background despite `transparent: true`.

**Solutions:**

1. **Use Compatible Model:**
```json
{
  "prompt": "logo design on transparent background",
  "model": "gptimage",     // Only certain models support transparency
  "transparent": true
}
```

2. **Adjust Prompt:**
```json
{
  "prompt": "company logo, transparent background, PNG format, no background",
  "transparent": true
}
```

### 4. Text Generation Issues

#### Issue: Text Generation Returns Empty or Poor Results

**Symptoms:**
- Empty responses
- Nonsensical text
- Doesn't follow instructions

**Solutions:**

1. **Improve Prompt Structure:**
```json
{
  "system": "You are a professional copywriter with expertise in marketing.",
  "prompt": "Write a compelling product description for wireless headphones targeting fitness enthusiasts. Include key features and benefits in 2-3 paragraphs.",
  "temperature": 0.7,
  "model": "openai"
}
```

2. **Use Appropriate Parameters:**
```json
{
  "temperature": 0.7,        // Balance creativity and coherence
  "top_p": 0.9,             // Focus on likely tokens
  "presence_penalty": 0.1,   // Reduce repetition
  "frequency_penalty": 0.1   // Vary word choice
}
```

3. **Try Different Models:**
```json
{
  "model": "openai",    // Best for creative content
  "model": "mistral",   // Good for technical content
  "model": "claude"     // Excellent for analysis
}
```

#### Issue: Chat Completions Not Working

**Symptoms:**
- Function calling not triggered
- Context not maintained
- Unexpected responses

**Solutions:**

1. **Proper Message Format:**
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user",
      "content": "What's the weather like?"
    }
  ],
  "tools": [...],
  "model": "openai"
}
```

2. **Function Calling Setup:**
```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "Get current weather for a location",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {"type": "string"}
          },
          "required": ["location"]
        }
      }
    }
  ],
  "tool_choice": "auto"
}
```

### 5. Audio Processing Issues

#### Issue: TTS Audio Quality Problems

**Symptoms:**
- Robotic or unnatural speech
- Mispronunciations
- Poor audio quality

**Solutions:**

1. **Optimize Text for TTS:**
```python
def optimize_for_tts(text):
    """Prepare text for optimal TTS results"""
    
    # Expand abbreviations
    text = text.replace("AI", "Artificial Intelligence")
    text = text.replace("ML", "Machine Learning") 
    text = text.replace("API", "A P I")
    
    # Add pauses for better pacing
    text = text.replace(". ", ". ... ")
    text = text.replace("! ", "! ... ")
    text = text.replace("? ", "? ... ")
    
    # Remove problematic characters
    text = re.sub(r'[^\w\s\.\!\?\,\-\:\;]', '', text)
    
    return text

optimized_text = optimize_for_tts("Your text here...")
```

2. **Choose Appropriate Voice:**
```json
{
  "text": "Your optimized text",
  "voice": "nova",      // Energetic for casual content
  "voice": "onyx",      // Authoritative for serious content
  "voice": "alloy"      // Neutral for general use
}
```

3. **Break Long Text:**
```python
def chunk_text_for_tts(text, max_length=800):
    """Break text into TTS-friendly chunks"""
    sentences = text.split('. ')
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        if len(current_chunk + sentence) > max_length:
            if current_chunk:
                chunks.append(current_chunk.strip() + '.')
                current_chunk = sentence
            else:
                chunks.append(sentence + '.')
        else:
            current_chunk += sentence + '. '
    
    if current_chunk:
        chunks.append(current_chunk.strip())
    
    return chunks
```

#### Issue: Speech-to-Text Accuracy Problems

**Symptoms:**
- Inaccurate transcriptions
- Missing words
- Poor handling of technical terms

**Solutions:**

1. **Prepare Audio Files:**
```bash
# Convert to supported format with good quality
ffmpeg -i input.mp3 -ar 16000 -ac 1 -c:a pcm_s16le output.wav

# Reduce background noise (if possible)
ffmpeg -i input.wav -af "highpass=f=200, lowpass=f=3000" output_clean.wav
```

2. **Use Context in Prompts:**
```json
{
  "audio_format": "wav",
  "question": "Transcribe this technical presentation about machine learning algorithms. Include technical terms and acronyms."
}
```

3. **Handle Common Issues:**
```python
def improve_transcription(text):
    """Post-process transcription for common errors"""
    
    # Fix common technical term errors
    corrections = {
        "ay eye": "AI",
        "machine learning": "machine learning",
        "a p i": "API",
        "data base": "database"
    }
    
    for wrong, right in corrections.items():
        text = text.replace(wrong, right)
    
    return text
```

### 6. Vision Analysis Issues

#### Issue: Poor Image Analysis Results

**Symptoms:**
- Inaccurate descriptions
- Missing important details
- Generic responses

**Solutions:**

1. **Use Specific Questions:**
```json
{
  "image_url": "https://example.com/image.jpg",
  "question": "Analyze this product image for an e-commerce listing. Describe the item, its key features, colors, and condition. Include details that would help customers make a purchase decision.",
  "model": "openai"
}
```

2. **Improve Image Quality:**
```python
# Ensure images are clear and well-lit
# Minimum recommended: 512x512 pixels
# Maximum effective: 2048x2048 pixels
```

3. **Use Appropriate Model:**
```json
{
  "model": "openai",        // General vision analysis
  "model": "openai-large",  // More detailed analysis
  "model": "claude"         // Good for complex reasoning
}
```

### 7. Performance Issues

#### Issue: Slow Response Times

**Symptoms:**
- Long generation times
- Timeouts
- High latency

**Solutions:**

1. **Optimize Request Size:**
```json
{
  "prompt": "Concise, clear prompt",  // Shorter prompts = faster
  "width": 1024,   // Smaller images = faster
  "height": 1024,
  "enhance": false // Skip enhancement for speed
}
```

2. **Use Sync Endpoints for Simple Requests:**
```bash
# ✅ Fast for simple requests
curl -X POST "/api/pollinations/text/generate/sync" \
  -d '{"prompt": "Hello world"}'

# ❌ Slower for simple requests (job queue overhead)
curl -X POST "/api/pollinations/text/generate" \
  -d '{"prompt": "Hello world"}'
```

3. **Implement Connection Pooling:**
```python
# Use session for multiple requests
async with aiohttp.ClientSession() as session:
    for prompt in prompts:
        await session.post(endpoint, json={"prompt": prompt})
```

4. **Batch Similar Requests:**
```python
# Start multiple jobs concurrently
jobs = await asyncio.gather(*[
    start_generation(prompt) for prompt in prompts
])

# Then wait for all results
results = await asyncio.gather(*[
    wait_for_job(job_id) for job_id in jobs
])
```

### 8. Storage and File Access Issues

#### Issue: Generated Content URLs Not Accessible

**Symptoms:**
- 403/404 errors when accessing S3 URLs
- Files not found
- Broken links

**Solutions:**

1. **Check S3 Configuration:**
```bash
# Verify S3 settings
docker-compose exec api python -c "
from app.services.s3_service import s3_service
print('Bucket:', s3_service.bucket_name)
print('Region:', s3_service.region)
print('Endpoint:', s3_service.endpoint_url)
"
```

2. **Test S3 Upload:**
```python
from app.services.s3_service import s3_service
import tempfile

# Test upload
with tempfile.NamedTemporaryFile() as tmp:
    tmp.write(b"test content")
    tmp.flush()
    
    result = await s3_service.upload_file_with_metadata(
        tmp.name, 
        "test/file.txt",
        public=True
    )
    print("Upload result:", result)
```

3. **Check File Permissions:**
```bash
# Files should be publicly readable
aws s3api get-object-acl --bucket your-bucket --key path/to/file.jpg
```

#### Issue: "S3 upload failed" Errors

**Solutions:**

1. **Verify Credentials:**
```bash
# Test AWS credentials
aws s3 ls s3://your-bucket-name/

# Or for DigitalOcean Spaces
aws s3 ls s3://your-space-name/ --endpoint-url=https://your-region.digitaloceanspaces.com
```

2. **Check Bucket Permissions:**
```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {"AWS": "arn:aws:iam::account:user/ouinhi-api"},
      "Action": ["s3:PutObject", "s3:GetObject"],
      "Resource": "arn:aws:s3:::your-bucket/*"
    }
  ]
}
```

3. **Enable CORS (if needed):**
```json
[
  {
    "AllowedHeaders": ["*"],
    "AllowedMethods": ["GET", "PUT", "POST"],
    "AllowedOrigins": ["*"],
    "ExposeHeaders": []
  }
]
```

## Debugging Tools

### Enable Debug Logging

```bash
# Add to .env
DEBUG=true
LOG_LEVEL=DEBUG

# Restart services
docker-compose restart api
```

### Monitor API Calls

```python
# Add request logging middleware
import logging
import time

logger = logging.getLogger("pollinations")

async def log_api_call(method, url, data=None):
    start_time = time.time()
    logger.info(f"API Call: {method} {url}")
    
    try:
        # Make actual API call
        result = await make_request(method, url, data)
        
        duration = time.time() - start_time
        logger.info(f"API Success: {method} {url} ({duration:.2f}s)")
        
        return result
        
    except Exception as e:
        duration = time.time() - start_time
        logger.error(f"API Error: {method} {url} ({duration:.2f}s): {e}")
        raise
```

### Test Individual Components

```python
# Test Pollinations service directly
from app.services.pollinations_service import pollinations_service

# Test model listing
models = await pollinations_service.list_image_models()
print("Available models:", models)

# Test simple generation
result = await pollinations_service.generate_text("Hello", model="openai")
print("Text generation:", result)
```

### Network Diagnostics

```bash
# Test connectivity to Pollinations
curl -I https://image.pollinations.ai/models
curl -I https://text.pollinations.ai/models

# Test from container
docker-compose exec api curl -I https://image.pollinations.ai/models

# Check DNS resolution
nslookup image.pollinations.ai
nslookup text.pollinations.ai
```

## Getting Help

### Logs to Collect

When reporting issues, include:

1. **Application Logs:**
```bash
docker-compose logs api --tail=100
```

2. **Environment Information:**
```bash
docker-compose exec api python -c "
import sys, os
print('Python:', sys.version)
print('API Key Set:', bool(os.environ.get('POLLINATIONS_API_KEY')))
print('S3 Bucket:', os.environ.get('S3_BUCKET_NAME'))
"
```

3. **Request/Response Details:**
```python
# Include exact request that failed
request_data = {
    "prompt": "your prompt",
    "model": "flux"
}

# Include error response
error_response = {
    "status_code": 500,
    "detail": "specific error message"
}
```

### Support Channels

- **Pollinations Documentation**: [docs.pollinations.ai](https://docs.pollinations.ai)
- **Pollinations GitHub**: [github.com/pollinations/pollinations](https://github.com/pollinations/pollinations)
- **Discord Community**: [discord.gg/pollinations](https://discord.gg/pollinations)

### Reporting Issues

When reporting issues, include:

1. **Environment**: Docker, local development, production
2. **Request Details**: Exact API call that failed
3. **Error Messages**: Complete error response
4. **Expected vs Actual**: What you expected vs what happened
5. **Logs**: Relevant application logs
6. **Configuration**: Sanitized environment variables

This should help you resolve most common issues with the Pollinations.AI integration.