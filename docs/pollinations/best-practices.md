# Pollinations.AI Best Practices

Guidelines for optimal performance, cost efficiency, and quality when using the Pollinations.AI integration.

## Authentication & API Keys

### Use Your API Key

Always set your `POLLINATIONS_API_KEY` environment variable:

```bash
POLLINATIONS_API_KEY=your_pollinations_api_key_here
```

**Benefits:**
- **No Rate Limits**: Eliminates 15-second cooldowns
- **Logo-Free Images**: Automatic `nologo=true` for clean images
- **Priority Processing**: Faster queue processing
- **Advanced Models**: Access to premium model tiers

### API Key Security

```bash
# ✅ Good: Environment variables
export POLLINATIONS_API_KEY=your_key_here

# ❌ Bad: Hardcoded in code
api_key = "poll_123abc..."  # Never do this
```

**Security checklist:**
- Never commit API keys to version control
- Use environment variables or secure secret management
- Rotate keys regularly
- Monitor usage for unusual activity

## Image Generation Optimization

### Prompt Engineering

**✅ Effective Prompts:**
```json
{
  "prompt": "A professional headshot of a confident businesswoman in modern office, natural lighting, high resolution, portrait photography style",
  "enhance": true
}
```

**❌ Ineffective Prompts:**
```json
{
  "prompt": "woman",
  "enhance": false
}
```

**Best practices:**
- Be specific and descriptive
- Include style references (photography, painting, digital art)
- Specify lighting conditions
- Use `enhance: true` for complex prompts
- Mention quality indicators (high resolution, professional, detailed)

### Dimension Guidelines

**Common Use Cases:**

| Use Case | Recommended Size | Aspect Ratio |
|----------|------------------|--------------|
| Social Media Posts | 1080x1080 | 1:1 |
| YouTube Thumbnails | 1280x720 | 16:9 |
| Website Headers | 1920x1080 | 16:9 |
| Mobile Wallpapers | 1080x1920 | 9:16 |
| Print (300 DPI) | 3000x2000+ | Varies |
| Blog Images | 1200x800 | 3:2 |

**Performance considerations:**
- Larger images take longer to generate
- Square images (1:1) often generate fastest
- Standard ratios (16:9, 4:3, 3:2) are well-optimized

### Model Selection

**Image Models:**

- **Flux** (default): Best general-purpose model
  - Fast generation times
  - High quality results
  - Good for most use cases

**When to use specific features:**

```json
{
  "model": "flux",
  "enhance": true,        // For complex or artistic prompts
  "safe": true,           // For public-facing content
  "transparent": true,    // For logos/graphics (PNG output)
  "seed": 42             // For reproducible results
}
```

## Text Generation Optimization

### Model Selection

**Text Models:**

- **OpenAI**: Best for general text, creative writing
- **Mistral**: Good for longer context, technical content
- **Claude**: Excellent for analysis, reasoning tasks

### Parameter Tuning

**Creative Content:**
```json
{
  "temperature": 0.8,      // Higher for creativity
  "top_p": 0.9,
  "presence_penalty": 0.1  // Avoid repetition
}
```

**Factual Content:**
```json
{
  "temperature": 0.3,      // Lower for accuracy
  "top_p": 0.8,
  "frequency_penalty": 0.2
}
```

**System Prompts:**
```json
{
  "system": "You are a professional copywriter specializing in engaging, conversion-focused content.",
  "prompt": "Write a product description for organic coffee beans"
}
```

### Prompt Design

**✅ Effective Structure:**
```
Role: You are an expert [domain] specialist
Task: [specific action]
Context: [relevant background]
Format: [desired output format]
Constraints: [limitations, style requirements]
```

**Example:**
```json
{
  "system": "You are a professional marketing copywriter with 10 years of experience in e-commerce.",
  "prompt": "Write a compelling product description for premium organic coffee beans. Target audience: health-conscious millennials. Format: 2-3 paragraphs with bullet points. Tone: conversational but professional. Include benefits and sensory details."
}
```

## Audio Processing Best Practices

### Voice Selection

**Voice Characteristics:**

| Voice | Best For | Tone |
|-------|----------|------|
| Alloy | General narration | Neutral, balanced |
| Echo | Professional content | Clear, authoritative |
| Fable | Storytelling | Expressive, engaging |
| Onyx | Serious content | Deep, authoritative |
| Nova | Casual content | Young, energetic |
| Shimmer | Soft content | Gentle, whispery |

### Text Preparation for TTS

**✅ TTS-Optimized Text:**
```text
Welcome to our podcast! Today, we're exploring artificial intelligence.

Here are three key points:
- First, A.I. is transforming industries
- Second, machine learning enables automation  
- Third, ethical considerations are crucial

Let's dive deeper into each topic.
```

**❌ Poor TTS Text:**
```text
Welcome 2 our podcast!!! Today we're exploring AI/ML & stuff...

Key points:
1) AI = good
2) ML = automation
3) Ethics = important ???

Let's go deeper...
```

**Optimization tips:**
- Write out abbreviations (AI → Artificial Intelligence)
- Use proper punctuation for natural pauses
- Avoid excessive special characters
- Include pronunciation guides for complex terms
- Keep sentences reasonably short (< 30 words)

### Audio Quality

**For best results:**
- Keep text under 1000 characters per request
- Use proper punctuation for natural speech rhythm
- Test different voices for your content type
- Consider breaking long content into segments

## Performance Optimization

### Async vs Sync Operations

**Use Async (Job Queue) For:**
- Long-running operations (complex images, long text)
- Batch processing
- Non-interactive workflows
- When you can poll for results

**Use Sync For:**
- Short, simple requests
- Interactive applications
- Real-time responses needed
- Quick prototyping

### Batch Processing

**✅ Efficient Batch Processing:**
```python
# Start all jobs concurrently
jobs = []
for prompt in prompts:
    response = await session.post(endpoint, json={"prompt": prompt})
    jobs.append(response.json()["job_id"])

# Then wait for all to complete
results = []
for job_id in jobs:
    result = await wait_for_completion(job_id)
    results.append(result)
```

**❌ Inefficient Sequential Processing:**
```python
# Don't do this - slow and inefficient
results = []
for prompt in prompts:
    response = await session.post(endpoint, json={"prompt": prompt})
    job_id = response.json()["job_id"]
    result = await wait_for_completion(job_id)  # Blocks each iteration
    results.append(result)
```

### Caching Strategies

**Model Lists Caching:**
```python
# Models don't change frequently - cache for 1 hour
cached_models = await redis.get("pollinations:models:image")
if not cached_models:
    models = await pollinations.list_image_models()
    await redis.setex("pollinations:models:image", 3600, json.dumps(models))
    return models
return json.loads(cached_models)
```

**Result Caching:**
```python
# Cache generated content URLs (they don't change)
cache_key = f"pollinations:result:{job_id}"
cached_result = await redis.get(cache_key)
if cached_result:
    return json.loads(cached_result)
```

## Cost Optimization

### Request Efficiency

**Minimize API Calls:**
- Use sync endpoints for simple, fast operations
- Batch similar requests when possible
- Cache model lists and other static data
- Use appropriate timeouts to avoid hanging requests

**Smart Polling:**
```python
# ✅ Exponential backoff for polling
async def wait_with_backoff(job_id, max_wait=300):
    delay = 1
    max_delay = 30
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        status = await check_job_status(job_id)
        if status["status"] in ["completed", "failed"]:
            return status
        
        await asyncio.sleep(delay)
        delay = min(delay * 1.5, max_delay)  # Exponential backoff
    
    raise TimeoutError("Job timed out")
```

### Resource Management

**Connection Pooling:**
```python
# ✅ Reuse HTTP connections
async with aiohttp.ClientSession() as session:
    # Make multiple requests with same session
    for i in range(100):
        await session.post(endpoint, json=data)
```

**Memory Management:**
```python
# ✅ Stream large results
async def download_large_content(url):
    async with session.get(url) as response:
        with open("output.jpg", "wb") as f:
            async for chunk in response.content.iter_chunked(8192):
                f.write(chunk)
```

## Error Handling & Reliability

### Robust Error Handling

```python
async def generate_with_retry(prompt, max_retries=3):
    """Generate with exponential backoff retry"""
    
    for attempt in range(max_retries):
        try:
            response = await client.post(endpoint, json={"prompt": prompt})
            
            if response.status == 200:
                return await response.json()
            elif response.status == 429:  # Rate limited
                wait_time = 2 ** attempt
                await asyncio.sleep(wait_time)
                continue
            else:
                response.raise_for_status()
                
        except aiohttp.ClientError as e:
            if attempt == max_retries - 1:
                raise
            
            wait_time = 2 ** attempt
            await asyncio.sleep(wait_time)
    
    raise Exception(f"Failed after {max_retries} attempts")
```

### Timeout Management

```python
# ✅ Set appropriate timeouts
timeout = aiohttp.ClientTimeout(
    total=300,      # 5 minutes total
    connect=30,     # 30 seconds to connect
    sock_read=60    # 60 seconds between reads
)

async with aiohttp.ClientSession(timeout=timeout) as session:
    # Make requests...
```

### Graceful Degradation

```python
async def generate_with_fallback(prompt):
    """Try Pollinations, fall back to local generation"""
    try:
        return await pollinations_generate(prompt)
    except Exception as e:
        logger.warning(f"Pollinations failed: {e}, using fallback")
        return await local_generate(prompt)
```

## Content Quality Guidelines

### Image Generation

**Quality Indicators:**
- Use descriptive, specific prompts
- Include style and lighting references
- Specify desired quality level
- Use enhancement for complex scenes

**Consistency:**
```json
{
  "prompt": "Product photo of wireless headphones, white background, professional lighting, e-commerce style",
  "seed": 12345,  // Same seed for consistent style
  "enhance": true
}
```

### Text Generation

**Quality Checklist:**
- Clear, specific instructions
- Appropriate model selection
- Proper parameter tuning
- Context-aware prompts
- Output format specification

**Consistency:**
```json
{
  "system": "You are a brand copywriter for TechCorp. Write in a professional, friendly tone that appeals to young professionals.",
  "prompt": "Write a product description for our new smartphone case",
  "temperature": 0.7,
  "seed": 42
}
```

## Monitoring & Analytics

### Usage Tracking

```python
# Track API usage and performance
@track_usage
async def generate_image(prompt, **kwargs):
    start_time = time.time()
    try:
        result = await pollinations_service.generate_image(prompt, **kwargs)
        
        # Log success metrics
        logger.info("image_generation_success", {
            "prompt_length": len(prompt),
            "generation_time": time.time() - start_time,
            "model": kwargs.get("model", "flux"),
            "dimensions": f"{kwargs.get('width', 1024)}x{kwargs.get('height', 1024)}"
        })
        
        return result
        
    except Exception as e:
        # Log error metrics
        logger.error("image_generation_failed", {
            "error": str(e),
            "prompt_length": len(prompt),
            "elapsed_time": time.time() - start_time
        })
        raise
```

### Performance Monitoring

**Key Metrics to Track:**
- Request latency (by operation type)
- Success/failure rates
- Queue times for async operations
- Resource usage (memory, CPU)
- Cost per operation
- User satisfaction scores

### Health Checks

```python
async def health_check():
    """Verify Pollinations integration health"""
    try:
        # Test basic connectivity
        models = await pollinations_service.list_image_models()
        
        # Test simple generation
        result = await pollinations_service.generate_text(
            "Health check test",
            temperature=0.1
        )
        
        return {
            "status": "healthy",
            "models_available": len(models),
            "response_time": "< 2s"
        }
        
    except Exception as e:
        return {
            "status": "unhealthy", 
            "error": str(e)
        }
```

## Security Best Practices

### Input Validation

```python
def validate_image_request(request):
    """Validate image generation request"""
    
    # Prompt validation
    if len(request.prompt) > 1000:
        raise ValueError("Prompt too long")
    
    if contains_inappropriate_content(request.prompt):
        raise ValueError("Inappropriate content detected")
    
    # Dimension validation
    if request.width * request.height > 4194304:  # 2048x2048 max
        raise ValueError("Image dimensions too large")
    
    # Model validation
    allowed_models = ["flux", "stable-diffusion"]
    if request.model not in allowed_models:
        raise ValueError("Invalid model specified")
```

### Content Filtering

```python
async def generate_safe_content(prompt, content_type="image"):
    """Generate content with safety checks"""
    
    # Pre-generation filtering
    if not is_safe_prompt(prompt):
        raise ValueError("Prompt contains unsafe content")
    
    # Add safety parameters
    if content_type == "image":
        kwargs = {"safe": True, "enhance": True}
    else:
        kwargs = {}
    
    result = await generate_content(prompt, **kwargs)
    
    # Post-generation filtering (if needed)
    if content_type == "image":
        if not is_safe_image(result["content_url"]):
            # Delete from S3 and return error
            await delete_content(result["content_url"])
            raise ValueError("Generated content failed safety check")
    
    return result
```

### Rate Limiting (Application Level)

```python
from datetime import datetime, timedelta
import asyncio

class RateLimiter:
    def __init__(self, max_requests=100, window_minutes=60):
        self.max_requests = max_requests
        self.window = timedelta(minutes=window_minutes)
        self.requests = {}
    
    async def check_rate_limit(self, user_id):
        now = datetime.now()
        user_requests = self.requests.get(user_id, [])
        
        # Remove old requests
        user_requests = [req_time for req_time in user_requests 
                        if now - req_time < self.window]
        
        if len(user_requests) >= self.max_requests:
            raise Exception("Rate limit exceeded")
        
        user_requests.append(now)
        self.requests[user_id] = user_requests
```

By following these best practices, you'll achieve optimal performance, quality, and reliability when using the Pollinations.AI integration in your Ouinhi API.