# Pollinations.AI Integration

The Ouinhi API includes comprehensive integration with [Pollinations.AI](https://pollinations.ai), providing access to powerful AI-driven content generation capabilities including image generation, text generation, vision analysis, and text-to-speech.

## Overview

Pollinations.AI is the world's most accessible open GenAI platform, offering text, image, and audio APIs with no signup required. Our integration provides:

- **Image Generation**: High-quality images from text descriptions
- **Text Generation**: Advanced language models for content creation
- **Vision Analysis**: AI-powered image understanding and description
- **Text-to-Speech**: Natural-sounding voice synthesis
- **Speech-to-Text**: Audio transcription capabilities

## Quick Start

### 1. Environment Setup

Add your Pollinations API key to your environment file:

```bash
# Optional but recommended for premium features
POLLINATIONS_API_KEY=your_pollinations_api_key_here
```

**Benefits of API Key:**
- Higher rate limits (no 15-second cooldowns)
- Automatic logo removal on generated images
- Priority processing queue
- Access to advanced model tiers

### 2. Start the API

```bash
# Using Docker Compose
docker-compose up --build

# Or for development
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 3. Test the Integration

```bash
# Generate an image
curl -X POST "http://localhost:8000/api/pollinations/image/generate" \
  -H "X-API-Key: your_ouinhi_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over the ocean",
    "width": 1920,
    "height": 1080,
    "model": "flux"
  }'

# Response: {"job_id": "abc-123-def-456"}

# Check job status
curl "http://localhost:8000/api/pollinations/image/generate/abc-123-def-456" \
  -H "X-API-Key: your_ouinhi_api_key"
```

## Architecture

### Job Queue Integration

All Pollinations operations are integrated with the Ouinhi job queue system:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   API Request   │───▶│   Job Queue      │───▶│  Pollinations   │
│                 │    │   (Redis)        │    │     API         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        │
┌─────────────────┐    ┌──────────────────┐              │
│   S3 Storage    │◀───│   Job Result     │◀─────────────┘
│   (Generated    │    │   Processing     │
│    Content)     │    │                  │
└─────────────────┘    └──────────────────┘
```

### Key Components

- **Service Layer**: `app/services/pollinations_service.py` - Core API client
- **Route Handlers**: `app/routes/{image,ai,audio}/pollinations.py` - API endpoints  
- **Models**: Pydantic models in `app/models.py` for request/response validation
- **Job Types**: New job types for async processing
- **Authentication**: Bearer token support for premium features

## Features & Capabilities

### Image Generation

Generate high-quality images from text descriptions with advanced controls:

- **Multiple Models**: Flux (default), and other advanced models
- **Customizable Dimensions**: From 64x64 to 2048x2048 pixels
- **Enhancement**: Automatic prompt enhancement using LLM
- **Safety Filters**: Built-in NSFW content filtering
- **Transparent Backgrounds**: PNG generation with transparency
- **Image-to-Image**: Transform existing images with new prompts
- **Reproducible Results**: Seed-based generation for consistency

### Text Generation

Advanced language model capabilities with multiple interfaces:

- **Simple Text Generation**: Single prompt → text response
- **Chat Completions**: Full conversation handling with message history
- **Multiple Models**: OpenAI, Mistral, and specialized models
- **Parameter Control**: Temperature, top-p, presence/frequency penalties
- **System Prompts**: Guide AI behavior and response style
- **JSON Mode**: Structured output formatting
- **Function Calling**: Enable AI to call external tools

### Vision Analysis

AI-powered image understanding and analysis:

- **Image URL Analysis**: Analyze images from web URLs  
- **Upload Analysis**: Analyze locally uploaded image files
- **Custom Questions**: Ask specific questions about image content
- **Multiple Models**: OpenAI Vision, Claude, and specialized vision models
- **Detailed Descriptions**: Comprehensive image analysis and description

### Audio Processing

High-quality speech synthesis and transcription:

- **Text-to-Speech**: Natural-sounding voice generation
- **Multiple Voices**: 6+ premium voices (alloy, echo, fable, onyx, nova, shimmer)
- **Speech-to-Text**: Audio file transcription (WAV, MP3)
- **High Quality Output**: MP3 format audio generation
- **Custom Instructions**: Guided transcription with context

## API Reference

### Authentication

All endpoints require authentication via `X-API-Key` header:

```bash
curl -H "X-API-Key: your_ouinhi_api_key" \
  "http://localhost:8000/api/pollinations/..."
```

### Base URLs

- **Production**: `https://your-domain.com/api/pollinations`
- **Development**: `http://localhost:8000/api/pollinations`

### Response Format

All async operations return a job ID for status tracking:

```json
{
  "job_id": "abc-123-def-456"
}
```

Check job status:

```json
{
  "job_id": "abc-123-def-456",
  "status": "completed",  // pending, processing, completed, failed
  "result": {
    "content_url": "https://your-s3-bucket.com/path/to/generated/content.jpg",
    "content_type": "image/jpeg",
    "file_size": 245760,
    "generation_time": 3.2,
    "model_used": "flux"
  },
  "error": null
}
```

## Directory Structure

```
docs/pollinations/
├── README.md              # This overview file
├── api-reference.md       # Complete API documentation
├── examples.md           # Usage examples and recipes
├── troubleshooting.md    # Common issues and solutions
└── best-practices.md     # Optimization tips and guidelines
```

## Next Steps

- **[API Reference](api-reference.md)** - Complete endpoint documentation
- **[Examples](examples.md)** - Code examples and use cases
- **[Best Practices](best-practices.md)** - Optimization and usage tips
- **[Troubleshooting](troubleshooting.md)** - Common issues and solutions

## Support

- **Documentation**: [Pollinations.AI Docs](https://docs.pollinations.ai)
- **Issues**: [GitHub Issues](https://github.com/pollinations/pollinations/issues)
- **Community**: [Discord](https://discord.gg/pollinations)