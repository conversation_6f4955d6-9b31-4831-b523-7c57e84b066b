# Pollinations.AI API Reference

Complete reference for all Pollinations.AI endpoints integrated into the Ouinhi API.

## Base URL

- **Production**: `https://your-domain.com/api/pollinations`
- **Development**: `http://localhost:8000/api/pollinations`

## Authentication

All endpoints require authentication via `X-API-Key` header:

```bash
curl -H "X-API-Key: your_ouinhi_api_key" \
  "http://localhost:8000/api/pollinations/..."
```

## Image Generation

### Generate Image

Generate an image from a text description.

**Endpoint**: `POST /api/pollinations/image/generate`

**Request Body**:
```json
{
  "prompt": "A beautiful sunset over the ocean",
  "model": "flux",
  "width": 1024,
  "height": 1024,
  "seed": 42,
  "enhance": true,
  "nologo": true,
  "safe": false,
  "transparent": false,
  "image_url": "https://example.com/input.jpg",
  "referrer": "my-app"
}
```

**Parameters**:
- `prompt` (string, required): Text description of the image (1-1000 chars)
- `model` (string): Model to use. Default: "flux"
- `width` (integer): Image width in pixels (64-2048). Default: 1024
- `height` (integer): Image height in pixels (64-2048). Default: 1024
- `seed` (integer): Seed for reproducible results
- `enhance` (boolean): Enhance prompt using LLM. Default: false
- `nologo` (boolean): Disable Pollinations logo. Default: false (auto-enabled with API key)
- `safe` (boolean): Strict NSFW filtering. Default: false
- `transparent` (boolean): Generate with transparent background. Default: false
- `image_url` (string): Input image URL for image-to-image generation
- `referrer` (string): Referrer for authentication

**Response**:
```json
{
  "job_id": "abc-123-def-456"
}
```

### Get Image Generation Status

Check the status of an image generation job.

**Endpoint**: `GET /api/pollinations/image/generate/{job_id}`

**Response**:
```json
{
  "job_id": "abc-123-def-456",
  "status": "completed",
  "result": {
    "content_url": "https://s3-bucket.com/pollinations/2024/01/15/image.jpg",
    "content_type": "image/jpeg",
    "file_size": 245760,
    "generation_time": 3.2,
    "model_used": "flux",
    "prompt": "A beautiful sunset over the ocean",
    "dimensions": "1024x1024"
  },
  "error": null
}
```

### List Image Models

Get available image generation models.

**Endpoint**: `GET /api/pollinations/models/image`

**Response**:
```json
{
  "models": ["flux", "stable-diffusion", "dalle"]
}
```

## Vision Analysis

### Analyze Image

Analyze an image using AI vision capabilities.

**Endpoint**: `POST /api/pollinations/vision/analyze`

**Request Body**:
```json
{
  "image_url": "https://example.com/image.jpg",
  "question": "What's in this image?",
  "model": "openai"
}
```

**Parameters**:
- `image_url` (string): URL to the image to analyze
- `question` (string): Question about the image. Default: "What's in this image?"
- `model` (string): Vision model to use. Default: "openai"

**Response**:
```json
{
  "job_id": "def-456-ghi-789"
}
```

### Analyze Uploaded Image

Analyze an uploaded image file.

**Endpoint**: `POST /api/pollinations/vision/analyze-upload`

**Request**: Multipart form data
- `file` (file, required): Image file to analyze
- `question` (string): Question about the image. Default: "What's in this image?"
- `model` (string): Vision model to use. Default: "openai"

**Response**:
```json
{
  "job_id": "ghi-789-jkl-012"
}
```

### Get Vision Analysis Status

Check the status of a vision analysis job.

**Endpoint**: `GET /api/pollinations/vision/analyze/{job_id}`

**Response**:
```json
{
  "job_id": "def-456-ghi-789",
  "status": "completed",
  "result": {
    "text": "This image shows a beautiful sunset over the ocean with orange and pink clouds reflected in the calm water.",
    "model_used": "openai",
    "generation_time": 1.8,
    "question": "What's in this image?",
    "image_url": "https://example.com/image.jpg"
  },
  "error": null
}
```

## Text Generation

### Generate Text

Generate text from a prompt using the simple text API.

**Endpoint**: `POST /api/pollinations/text/generate`

**Request Body**:
```json
{
  "prompt": "Write a creative story about AI",
  "model": "openai",
  "seed": 42,
  "temperature": 0.8,
  "top_p": 0.9,
  "presence_penalty": 0.1,
  "frequency_penalty": 0.1,
  "system": "You are a creative writer",
  "json_mode": false,
  "referrer": "my-app"
}
```

**Parameters**:
- `prompt` (string, required): Text prompt for generation (1-2000 chars)
- `model` (string): Model to use. Default: "openai"
- `seed` (integer): Seed for reproducible results
- `temperature` (float): Randomness control (0.0-3.0)
- `top_p` (float): Nucleus sampling (0.0-1.0)
- `presence_penalty` (float): Presence penalty (-2.0 to 2.0)
- `frequency_penalty` (float): Frequency penalty (-2.0 to 2.0)
- `system` (string): System prompt (max 500 chars)
- `json_mode` (boolean): Return JSON formatted response
- `referrer` (string): Referrer for authentication

**Response**:
```json
{
  "job_id": "jkl-012-mno-345"
}
```

### Generate Text (Sync)

Generate text immediately without job queue.

**Endpoint**: `POST /api/pollinations/text/generate/sync`

**Request Body**: Same as async version

**Response**:
```json
{
  "text": "Generated text content here...",
  "model_used": "openai",
  "generation_time": 2.1,
  "prompt": "Write a creative story about AI",
  "character_count": 156
}
```

### Chat Completions

Create a chat completion with full conversation support.

**Endpoint**: `POST /api/pollinations/chat/completions`

**Request Body**:
```json
{
  "messages": [
    {
      "role": "system",
      "content": "You are a helpful assistant."
    },
    {
      "role": "user", 
      "content": "What is the capital of France?"
    }
  ],
  "model": "openai",
  "temperature": 0.7,
  "stream": false,
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "Get current weather"
      }
    }
  ]
}
```

**Parameters**:
- `messages` (array, required): Array of message objects
- `model` (string): Model to use. Default: "openai"
- `temperature` (float): Randomness control (0.0-3.0)
- `stream` (boolean): Stream response. Default: false
- `json_mode` (boolean): Force JSON output
- `tools` (array): Available tools for function calling
- `tool_choice` (string|object): Tool choice strategy

**Response**:
```json
{
  "job_id": "mno-345-pqr-678"
}
```

### Chat Completions (Sync)

Create a chat completion immediately.

**Endpoint**: `POST /api/pollinations/chat/completions/sync`

**Request Body**: Same as async version

**Response**: OpenAI-compatible chat completion response:
```json
{
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "The capital of France is Paris."
      }
    }
  ],
  "_metadata": {
    "generation_time": 1.5,
    "model_used": "openai",
    "message_count": 2
  }
}
```

### Get Text Generation Status

Check the status of a text generation job.

**Endpoint**: `GET /api/pollinations/text/generate/{job_id}`

**Response**:
```json
{
  "job_id": "jkl-012-mno-345",
  "status": "completed",
  "result": {
    "text": "Generated text content here...",
    "model_used": "openai", 
    "generation_time": 2.1,
    "prompt": "Write a creative story about AI",
    "character_count": 156
  },
  "error": null
}
```

### List Text Models

Get available text generation models and voices.

**Endpoint**: `GET /api/pollinations/models/text`

**Response**:
```json
{
  "models": {
    "openai": {
      "capabilities": ["text", "vision", "function_calling"],
      "max_tokens": 4096
    },
    "mistral": {
      "capabilities": ["text"],
      "max_tokens": 8192
    },
    "openai-audio": {
      "capabilities": ["text_to_speech", "speech_to_text"],
      "voices": ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
    }
  }
}
```

## Audio Processing

### Text-to-Speech

Convert text to speech audio.

**Endpoint**: `POST /api/pollinations/audio/tts`

**Request Body**:
```json
{
  "text": "Hello, this is a test of text to speech",
  "voice": "nova",
  "model": "openai-audio"
}
```

**Parameters**:
- `text` (string, required): Text to synthesize (1-1000 chars)
- `voice` (string): Voice to use. Default: "alloy"
- `model` (string): Must be "openai-audio". Default: "openai-audio"

**Response**:
```json
{
  "job_id": "pqr-678-stu-901"
}
```

### Text-to-Speech (Sync)

Generate speech audio immediately.

**Endpoint**: `POST /api/pollinations/audio/tts/sync`

**Request Body**: Same as async version

**Response**:
```json
{
  "content_url": "https://s3-bucket.com/pollinations/2024/01/15/tts-audio.mp3",
  "content_type": "audio/mpeg",
  "file_size": 48432,
  "generation_time": 1.2,
  "model_used": "openai-audio",
  "voice_used": "nova",
  "text": "Hello, this is a test of text to speech",
  "text_length": 40
}
```

### Speech-to-Text

Transcribe audio file to text.

**Endpoint**: `POST /api/pollinations/audio/transcribe`

**Request**: Multipart form data
- `file` (file, required): Audio file to transcribe (WAV, MP3)
- `question` (string): Transcription instruction. Default: "Transcribe this audio"

**Response**:
```json
{
  "job_id": "stu-901-vwx-234"
}
```

### Get Audio Processing Status

Check the status of an audio processing job.

**Endpoint**: `GET /api/pollinations/audio/tts/{job_id}` or `GET /api/pollinations/audio/transcribe/{job_id}`

**Response (TTS)**:
```json
{
  "job_id": "pqr-678-stu-901",
  "status": "completed",
  "result": {
    "content_url": "https://s3-bucket.com/pollinations/2024/01/15/tts-audio.mp3",
    "content_type": "audio/mpeg",
    "file_size": 48432,
    "generation_time": 1.2,
    "model_used": "openai-audio",
    "voice_used": "nova",
    "text": "Hello, this is a test of text to speech",
    "text_length": 40
  },
  "error": null
}
```

**Response (Transcription)**:
```json
{
  "job_id": "stu-901-vwx-234",
  "status": "completed",
  "result": {
    "transcription": "Hello, this is the transcribed text from the audio file.",
    "audio_format": "wav",
    "generation_time": 2.5,
    "file_name": "audio.wav",
    "file_size": 245760,
    "character_count": 63
  },
  "error": null
}
```

### List Available Voices

Get available TTS voices.

**Endpoint**: `GET /api/pollinations/voices`

**Response**:
```json
{
  "voices": [
    {
      "name": "alloy",
      "description": "Neutral, balanced voice"
    },
    {
      "name": "echo", 
      "description": "Male, clear voice"
    },
    {
      "name": "fable",
      "description": "Expressive, storytelling voice"
    },
    {
      "name": "onyx",
      "description": "Deep, authoritative voice"
    },
    {
      "name": "nova",
      "description": "Young, energetic voice"
    },
    {
      "name": "shimmer",
      "description": "Soft, whispery voice"
    }
  ],
  "model": "openai-audio",
  "total_count": 6
}
```

## Error Handling

### HTTP Status Codes

- `200` - Success
- `400` - Bad Request (validation error)
- `401` - Unauthorized (missing/invalid API key)
- `404` - Not Found (job not found)
- `422` - Unprocessable Entity (validation error)
- `500` - Internal Server Error

### Error Response Format

```json
{
  "detail": "Error description",
  "job_id": "abc-123-def-456",
  "status": "failed",
  "error": "Detailed error message"
}
```

### Common Errors

**Validation Errors**:
```json
{
  "detail": [
    {
      "type": "value_error.missing",
      "loc": ["body", "prompt"],
      "msg": "field required"
    }
  ]
}
```

**Job Processing Errors**:
```json
{
  "job_id": "abc-123-def-456",
  "status": "failed", 
  "error": "Image generation failed: Invalid prompt content"
}
```

## Rate Limits

### Anonymous Requests
- **Rate Limit**: 1 request per 15 seconds per IP
- **Concurrent**: 1 concurrent request

### Authenticated Requests (with API key)
- **Rate Limit**: No cooldown restrictions
- **Concurrent**: Multiple concurrent requests
- **Priority**: Higher queue priority

## OpenAPI Documentation

Interactive API documentation is available at:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

All Pollinations endpoints are automatically documented with request/response schemas, examples, and parameter descriptions.