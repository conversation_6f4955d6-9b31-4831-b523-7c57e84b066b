# Pollinations.AI Examples

Practical examples and code snippets for using the Pollinations.AI integration.

## Getting Started Examples

### Basic Image Generation

```bash
# Generate a simple image
curl -X POST "http://localhost:8000/api/pollinations/image/generate" \
  -H "X-API-Key: your_ouinhi_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A serene mountain landscape with a lake",
    "width": 1920,
    "height": 1080
  }'

# Response: {"job_id": "img_123"}

# Check status
curl "http://localhost:8000/api/pollinations/image/generate/img_123" \
  -H "X-API-Key: your_ouinhi_api_key"
```

### Quick Text Generation

```bash
# Generate text synchronously 
curl -X POST "http://localhost:8000/api/pollinations/text/generate/sync" \
  -H "X-API-Key: your_ouinhi_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Write a haiku about coding",
    "temperature": 0.8
  }'
```

### Text-to-Speech

```bash
# Generate speech audio
curl -X POST "http://localhost:8000/api/pollinations/audio/tts/sync" \
  -H "X-API-Key: your_ouinhi_api_key" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Welcome to the future of AI content generation",
    "voice": "nova"
  }'
```

## Python Examples

### Complete Image Generation Workflow

```python
import requests
import time
import json

class PollinationsClient:
    def __init__(self, base_url="http://localhost:8000", api_key="your_api_key"):
        self.base_url = base_url
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    def generate_image(self, prompt, **kwargs):
        """Generate an image and wait for completion"""
        # Start generation
        response = requests.post(
            f"{self.base_url}/api/pollinations/image/generate",
            headers=self.headers,
            json={"prompt": prompt, **kwargs}
        )
        response.raise_for_status()
        job_id = response.json()["job_id"]
        
        # Poll for completion
        while True:
            status_response = requests.get(
                f"{self.base_url}/api/pollinations/image/generate/{job_id}",
                headers=self.headers
            )
            status_response.raise_for_status()
            status_data = status_response.json()
            
            if status_data["status"] == "completed":
                return status_data["result"]
            elif status_data["status"] == "failed":
                raise Exception(f"Generation failed: {status_data['error']}")
            
            time.sleep(2)  # Wait 2 seconds before checking again

# Usage example
client = PollinationsClient(api_key="your_ouinhi_api_key")

result = client.generate_image(
    prompt="A futuristic city skyline at sunset",
    width=1920,
    height=1080,
    model="flux",
    enhance=True
)

print(f"Image generated: {result['content_url']}")
print(f"Size: {result['file_size']} bytes")
print(f"Generation time: {result['generation_time']} seconds")
```

### Batch Image Generation

```python
import asyncio
import aiohttp

async def generate_images_batch(prompts, api_key="your_api_key"):
    """Generate multiple images concurrently"""
    base_url = "http://localhost:8000"
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    async with aiohttp.ClientSession() as session:
        # Start all generations
        job_ids = []
        for prompt in prompts:
            async with session.post(
                f"{base_url}/api/pollinations/image/generate",
                headers=headers,
                json={"prompt": prompt}
            ) as response:
                data = await response.json()
                job_ids.append((prompt, data["job_id"]))
        
        # Wait for all to complete
        results = []
        for prompt, job_id in job_ids:
            while True:
                async with session.get(
                    f"{base_url}/api/pollinations/image/generate/{job_id}",
                    headers=headers
                ) as response:
                    data = await response.json()
                    
                    if data["status"] == "completed":
                        results.append({
                            "prompt": prompt,
                            "result": data["result"]
                        })
                        break
                    elif data["status"] == "failed":
                        results.append({
                            "prompt": prompt,
                            "error": data["error"]
                        })
                        break
                
                await asyncio.sleep(2)
        
        return results

# Usage
prompts = [
    "A cat wearing a space helmet",
    "A robot gardening flowers", 
    "A magical forest with glowing trees"
]

results = asyncio.run(generate_images_batch(prompts))
for item in results:
    if "error" in item:
        print(f"Failed: {item['prompt']} - {item['error']}")
    else:
        print(f"Success: {item['prompt']} - {item['result']['content_url']}")
```

### Advanced Chat Completion with Function Calling

```python
import requests

def chat_with_functions(api_key="your_api_key"):
    """Example of chat completion with function calling"""
    
    headers = {
        "X-API-Key": api_key,
        "Content-Type": "application/json"
    }
    
    # Define available functions
    tools = [
        {
            "type": "function",
            "function": {
                "name": "get_weather",
                "description": "Get current weather for a location",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "location": {
                            "type": "string",
                            "description": "City name"
                        }
                    },
                    "required": ["location"]
                }
            }
        }
    ]
    
    # Start chat completion
    response = requests.post(
        "http://localhost:8000/api/pollinations/chat/completions/sync",
        headers=headers,
        json={
            "messages": [
                {
                    "role": "user",
                    "content": "What's the weather like in Tokyo?"
                }
            ],
            "tools": tools,
            "model": "openai"
        }
    )
    
    result = response.json()
    choice = result["choices"][0]
    
    if choice["message"].get("tool_calls"):
        # AI wants to call a function
        tool_call = choice["message"]["tool_calls"][0]
        function_name = tool_call["function"]["name"]
        arguments = json.loads(tool_call["function"]["arguments"])
        
        print(f"AI wants to call: {function_name} with {arguments}")
        
        # Simulate function execution
        if function_name == "get_weather":
            weather_result = f"The weather in {arguments['location']} is sunny, 24°C"
            
            # Continue conversation with function result
            follow_up = requests.post(
                "http://localhost:8000/api/pollinations/chat/completions/sync",
                headers=headers,
                json={
                    "messages": [
                        {
                            "role": "user",
                            "content": "What's the weather like in Tokyo?"
                        },
                        choice["message"],  # Assistant's tool call
                        {
                            "role": "tool",
                            "tool_call_id": tool_call["id"],
                            "name": function_name,
                            "content": weather_result
                        }
                    ],
                    "tools": tools,
                    "model": "openai"
                }
            )
            
            final_result = follow_up.json()
            print(f"Final response: {final_result['choices'][0]['message']['content']}")
    else:
        # Direct response
        print(f"Response: {choice['message']['content']}")

# Usage
chat_with_functions()
```

### Vision Analysis with Image Upload

```python
import requests

def analyze_image_file(image_path, question="What's in this image?", api_key="your_api_key"):
    """Analyze a local image file"""
    
    headers = {"X-API-Key": api_key}
    
    # Upload and analyze image
    with open(image_path, 'rb') as image_file:
        files = {'file': image_file}
        data = {'question': question}
        
        response = requests.post(
            "http://localhost:8000/api/pollinations/vision/analyze-upload",
            headers=headers,
            files=files,
            data=data
        )
        response.raise_for_status()
        job_id = response.json()["job_id"]
    
    # Wait for analysis
    while True:
        status_response = requests.get(
            f"http://localhost:8000/api/pollinations/vision/analyze/{job_id}",
            headers=headers
        )
        status_data = status_response.json()
        
        if status_data["status"] == "completed":
            return status_data["result"]["text"]
        elif status_data["status"] == "failed":
            raise Exception(f"Analysis failed: {status_data['error']}")
        
        time.sleep(1)

# Usage
description = analyze_image_file(
    "path/to/your/image.jpg",
    question="Describe this image in detail, including colors, objects, and mood"
)
print(f"Analysis: {description}")
```

## JavaScript/Node.js Examples

### Image Generation with Axios

```javascript
const axios = require('axios');

class PollinationsAPI {
    constructor(baseURL = 'http://localhost:8000', apiKey = 'your_api_key') {
        this.client = axios.create({
            baseURL,
            headers: {
                'X-API-Key': apiKey,
                'Content-Type': 'application/json'
            }
        });
    }
    
    async generateImage(prompt, options = {}) {
        // Start generation
        const response = await this.client.post('/api/pollinations/image/generate', {
            prompt,
            ...options
        });
        
        const { job_id } = response.data;
        
        // Poll for completion
        while (true) {
            const statusResponse = await this.client.get(`/api/pollinations/image/generate/${job_id}`);
            const { status, result, error } = statusResponse.data;
            
            if (status === 'completed') {
                return result;
            } else if (status === 'failed') {
                throw new Error(`Generation failed: ${error}`);
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    async generateTextSync(prompt, options = {}) {
        const response = await this.client.post('/api/pollinations/text/generate/sync', {
            prompt,
            ...options
        });
        return response.data;
    }
    
    async textToSpeechSync(text, voice = 'nova') {
        const response = await this.client.post('/api/pollinations/audio/tts/sync', {
            text,
            voice
        });
        return response.data;
    }
}

// Usage example
async function main() {
    const api = new PollinationsAPI();
    
    try {
        // Generate image
        const imageResult = await api.generateImage('A cosmic nebula in purple and blue', {
            width: 1024,
            height: 1024,
            enhance: true
        });
        console.log('Image generated:', imageResult.content_url);
        
        // Generate text
        const textResult = await api.generateTextSync('Write a short poem about space exploration');
        console.log('Generated text:', textResult.text);
        
        // Generate speech
        const audioResult = await api.textToSpeechSync(textResult.text, 'nova');
        console.log('Audio generated:', audioResult.content_url);
        
    } catch (error) {
        console.error('Error:', error.message);
    }
}

main();
```

### React Component Example

```jsx
import React, { useState } from 'react';
import axios from 'axios';

const PollinationsDemo = () => {
    const [prompt, setPrompt] = useState('');
    const [loading, setLoading] = useState(false);
    const [result, setResult] = useState(null);
    const [error, setError] = useState(null);
    
    const apiClient = axios.create({
        baseURL: 'http://localhost:8000',
        headers: {
            'X-API-Key': 'your_api_key',
            'Content-Type': 'application/json'
        }
    });
    
    const generateImage = async () => {
        if (!prompt.trim()) return;
        
        setLoading(true);
        setError(null);
        setResult(null);
        
        try {
            // Start generation
            const response = await apiClient.post('/api/pollinations/image/generate', {
                prompt: prompt.trim(),
                width: 512,
                height: 512
            });
            
            const { job_id } = response.data;
            
            // Poll for completion
            const pollStatus = async () => {
                const statusResponse = await apiClient.get(`/api/pollinations/image/generate/${job_id}`);
                const { status, result: jobResult, error: jobError } = statusResponse.data;
                
                if (status === 'completed') {
                    setResult(jobResult);
                    setLoading(false);
                } else if (status === 'failed') {
                    setError(jobError);
                    setLoading(false);
                } else {
                    setTimeout(pollStatus, 2000);
                }
            };
            
            pollStatus();
            
        } catch (err) {
            setError(err.response?.data?.detail || err.message);
            setLoading(false);
        }
    };
    
    return (
        <div className="p-6">
            <h2 className="text-2xl font-bold mb-4">Pollinations Image Generator</h2>
            
            <div className="mb-4">
                <input
                    type="text"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Enter your image prompt..."
                    className="w-full p-2 border rounded"
                    disabled={loading}
                />
                <button
                    onClick={generateImage}
                    disabled={loading || !prompt.trim()}
                    className="mt-2 px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
                >
                    {loading ? 'Generating...' : 'Generate Image'}
                </button>
            </div>
            
            {error && (
                <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                    Error: {error}
                </div>
            )}
            
            {result && (
                <div className="mb-4">
                    <h3 className="text-lg font-semibold mb-2">Generated Image:</h3>
                    <img
                        src={result.content_url}
                        alt={prompt}
                        className="max-w-full h-auto border rounded"
                    />
                    <p className="text-sm text-gray-600 mt-2">
                        Generated in {result.generation_time.toFixed(1)}s using {result.model_used}
                    </p>
                </div>
            )}
        </div>
    );
};

export default PollinationsDemo;
```

## Advanced Use Cases

### Content Pipeline Automation

```python
import asyncio
from typing import List, Dict
import aiohttp

class ContentPipeline:
    """Automated content creation pipeline using Pollinations"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "http://localhost:8000"
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    async def create_blog_post_with_visuals(self, topic: str) -> Dict:
        """Generate a complete blog post with images and audio"""
        
        async with aiohttp.ClientSession() as session:
            # 1. Generate blog post content
            blog_response = await session.post(
                f"{self.base_url}/api/pollinations/text/generate/sync",
                headers=self.headers,
                json={
                    "prompt": f"Write a comprehensive blog post about {topic}. Include an introduction, 3 main sections, and a conclusion.",
                    "temperature": 0.7,
                    "system": "You are a professional content writer."
                }
            )
            blog_data = await blog_response.json()
            blog_text = blog_data["text"]
            
            # 2. Generate hero image
            hero_image_response = await session.post(
                f"{self.base_url}/api/pollinations/image/generate",
                headers=self.headers,
                json={
                    "prompt": f"Professional hero image for a blog post about {topic}, high quality, modern design",
                    "width": 1920,
                    "height": 1080,
                    "enhance": True
                }
            )
            hero_job_id = (await hero_image_response.json())["job_id"]
            
            # 3. Generate audio narration
            audio_response = await session.post(
                f"{self.base_url}/api/pollinations/audio/tts",
                headers=self.headers,
                json={
                    "text": blog_text[:1000] + "...",  # First 1000 chars for intro
                    "voice": "nova"
                }
            )
            audio_job_id = (await audio_response.json())["job_id"]
            
            # 4. Wait for async jobs to complete
            hero_image = await self._wait_for_job(session, f"image/generate/{hero_job_id}")
            audio_narration = await self._wait_for_job(session, f"audio/tts/{audio_job_id}")
            
            return {
                "topic": topic,
                "content": blog_text,
                "hero_image": hero_image["content_url"],
                "audio_narration": audio_narration["content_url"],
                "metadata": {
                    "word_count": len(blog_text.split()),
                    "generation_time": blog_data["generation_time"]
                }
            }
    
    async def _wait_for_job(self, session: aiohttp.ClientSession, endpoint: str):
        """Wait for a job to complete"""
        while True:
            async with session.get(
                f"{self.base_url}/api/pollinations/{endpoint}",
                headers=self.headers
            ) as response:
                data = await response.json()
                
                if data["status"] == "completed":
                    return data["result"]
                elif data["status"] == "failed":
                    raise Exception(f"Job failed: {data['error']}")
                
                await asyncio.sleep(2)

# Usage
async def main():
    pipeline = ContentPipeline("your_api_key")
    
    result = await pipeline.create_blog_post_with_visuals("Artificial Intelligence in Healthcare")
    
    print(f"Generated blog post about: {result['topic']}")
    print(f"Content: {result['content'][:200]}...")
    print(f"Hero image: {result['hero_image']}")
    print(f"Audio narration: {result['audio_narration']}")
    print(f"Word count: {result['metadata']['word_count']}")

asyncio.run(main())
```

### Bulk Processing with Error Handling

```python
import asyncio
import json
from pathlib import Path
import aiohttp

async def bulk_image_processing(prompts_file: str, output_dir: str, api_key: str):
    """Process multiple prompts with comprehensive error handling"""
    
    # Load prompts
    with open(prompts_file, 'r') as f:
        prompts = json.load(f)
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    results = []
    failed_prompts = []
    
    async with aiohttp.ClientSession() as session:
        headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
        
        # Process in batches to avoid overwhelming the API
        batch_size = 5
        for i in range(0, len(prompts), batch_size):
            batch = prompts[i:i + batch_size]
            
            print(f"Processing batch {i//batch_size + 1}/{(len(prompts)-1)//batch_size + 1}")
            
            # Start all jobs in batch
            jobs = []
            for prompt_data in batch:
                try:
                    async with session.post(
                        "http://localhost:8000/api/pollinations/image/generate",
                        headers=headers,
                        json=prompt_data
                    ) as response:
                        if response.status == 200:
                            job_data = await response.json()
                            jobs.append((prompt_data, job_data["job_id"]))
                        else:
                            error_text = await response.text()
                            failed_prompts.append({
                                "prompt": prompt_data,
                                "error": f"HTTP {response.status}: {error_text}"
                            })
                except Exception as e:
                    failed_prompts.append({
                        "prompt": prompt_data,
                        "error": str(e)
                    })
            
            # Wait for all jobs in batch
            for prompt_data, job_id in jobs:
                try:
                    result = await wait_for_completion(session, job_id, headers)
                    
                    # Save result
                    filename = f"image_{job_id}.json"
                    with open(output_path / filename, 'w') as f:
                        json.dump({
                            "prompt": prompt_data,
                            "result": result,
                            "job_id": job_id
                        }, f, indent=2)
                    
                    results.append({
                        "prompt": prompt_data["prompt"],
                        "image_url": result["content_url"],
                        "job_id": job_id
                    })
                    
                except Exception as e:
                    failed_prompts.append({
                        "prompt": prompt_data,
                        "job_id": job_id,
                        "error": str(e)
                    })
            
            # Small delay between batches
            await asyncio.sleep(1)
    
    # Save summary
    summary = {
        "total_prompts": len(prompts),
        "successful": len(results),
        "failed": len(failed_prompts),
        "results": results,
        "failures": failed_prompts
    }
    
    with open(output_path / "summary.json", 'w') as f:
        json.dump(summary, f, indent=2)
    
    print(f"Completed: {len(results)} successful, {len(failed_prompts)} failed")
    return summary

async def wait_for_completion(session, job_id, headers, timeout=300):
    """Wait for job completion with timeout"""
    start_time = asyncio.get_event_loop().time()
    
    while True:
        if asyncio.get_event_loop().time() - start_time > timeout:
            raise TimeoutError(f"Job {job_id} timed out after {timeout} seconds")
        
        async with session.get(
            f"http://localhost:8000/api/pollinations/image/generate/{job_id}",
            headers=headers
        ) as response:
            data = await response.json()
            
            if data["status"] == "completed":
                return data["result"]
            elif data["status"] == "failed":
                raise Exception(f"Job failed: {data['error']}")
            
            await asyncio.sleep(3)

# Usage
prompts = [
    {"prompt": "A sunset over mountains", "width": 1024, "height": 768},
    {"prompt": "A futuristic city", "width": 1920, "height": 1080},
    {"prompt": "A peaceful forest scene", "width": 1024, "height": 1024}
]

# Save prompts to file
with open("prompts.json", "w") as f:
    json.dump(prompts, f)

# Process
asyncio.run(bulk_image_processing("prompts.json", "output/", "your_api_key"))
```

## Testing and Validation

### API Endpoint Testing

```python
import requests
import pytest

class TestPollinationsAPI:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.headers = {"X-API-Key": "your_test_api_key"}
    
    def test_image_generation(self):
        """Test basic image generation"""
        response = requests.post(
            f"{self.base_url}/api/pollinations/image/generate",
            headers=self.headers,
            json={"prompt": "test image"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "job_id" in data
        
        # Check job status
        job_response = requests.get(
            f"{self.base_url}/api/pollinations/image/generate/{data['job_id']}",
            headers=self.headers
        )
        assert job_response.status_code == 200
    
    def test_text_generation_sync(self):
        """Test synchronous text generation"""
        response = requests.post(
            f"{self.base_url}/api/pollinations/text/generate/sync",
            headers=self.headers,
            json={"prompt": "Hello world"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "text" in data
        assert len(data["text"]) > 0
    
    def test_model_listing(self):
        """Test model listing endpoints"""
        # Test image models
        response = requests.get(
            f"{self.base_url}/api/pollinations/models/image",
            headers=self.headers
        )
        assert response.status_code == 200
        assert "models" in response.json()
        
        # Test text models
        response = requests.get(
            f"{self.base_url}/api/pollinations/models/text",
            headers=self.headers
        )
        assert response.status_code == 200
        
        # Test voices
        response = requests.get(
            f"{self.base_url}/api/pollinations/voices",
            headers=self.headers
        )
        assert response.status_code == 200
        assert "voices" in response.json()

# Run tests
if __name__ == "__main__":
    tester = TestPollinationsAPI()
    tester.test_image_generation()
    tester.test_text_generation_sync()
    tester.test_model_listing()
    print("All tests passed!")
```

These examples demonstrate the full range of capabilities available through the Pollinations.AI integration, from simple single requests to complex automated content pipelines.