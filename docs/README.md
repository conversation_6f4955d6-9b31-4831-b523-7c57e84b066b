# Media API Documentation

Welcome to the Media API documentation! This comprehensive guide covers all features including the new AI-powered video generation capabilities.

## 📚 **Documentation Index**

### 🤖 **AI Features (NEW)**
- [AI Script Generation](ai-script-generation.md) - Generate video scripts from topics using GPT-4o/Groq
- [AI Video Search](ai-video-search.md) - Find stock videos and generate search queries with AI
- [Topic-to-Video Pipeline](footage-to-video-pipeline.md) - Complete end-to-end video generation
- [Enhanced Caption Timing](enhanced-caption-timing.md) - Precise word-level timing with Whisper
- [OpenAI-Compatible LLMs](openai-compatible-llms.md) - Use any OpenAI-compatible LLM (Ollama, etc.)
- [**🎬 YouTube Shorts Generator**](yt-shorts/README.md) - **AI-powered YouTube Shorts creation** ⭐

### 🎵 **Audio Processing**
- [Text-to-Speech](audio/speech.md) - Multi-provider TTS with Kokoro and Edge TTS
- [Music Generation](audio/music.md) - AI music creation with MusicGen
- [Audio Transcription](audio/transcriptions.md) - Whisper-powered audio transcription

### 📄 **Document Processing**
- [Document Conversion](documents/README.md) - Convert PDFs, Word docs, Excel, PowerPoint to Markdown
- [Examples & Use Cases](documents/examples.md) - Practical examples and integration patterns
- [Troubleshooting](documents/troubleshooting.md) - Common issues and solutions

### 🖼️ **Image Processing**
- [Image Overlay](images/edit.md) - Multi-layer image composition
- [Image-to-Video](videos/generations.md) - Convert images to videos with effects

### 🎬 **Video Processing**
- [Video Overlay](videos/edit.md) - Overlay videos on images
- [Video Concatenation](videos/concatenate.md) - Join multiple videos with transitions
- [Video Merge](videos/merge.md) - Merge videos with background audio in one operation
- [Video Captions](videos/add_captions.md) - Add modern TikTok-style captions
- [Video Audio](videos/add_audio.md) - Add background music and narration
- [Text Overlay](videos/text_overlay.md) - Add styled text to videos
- [LTX Video Generation](videos/ltx_generations.md) - Generate videos from text prompts or images using LTX-Video
- [**🎯 YouTube Shorts**](yt-shorts/README.md) - **Advanced AI-powered short video generation** ⭐

### 🔧 **Advanced Features**
- [FFmpeg Compose](ffmpeg/README.md) - Advanced video processing with custom FFmpeg commands
- [S3 Storage](s3/upload.md) - Cloud storage integration
- [Media Downloads](media/download.md) - Download and convert media
- [Code Execution](code/execute_python.md) - Run Python code for custom processing

## 🌟 **New AI-Powered Features**

The Media API now includes powerful AI capabilities that enable complete automation of video content creation:

### **🎯 Topic-to-Video Generation**
Transform any topic into a complete video with:
- AI script generation optimized for viral content
- Automatic background video sourcing from Pexels
- High-quality text-to-speech narration
- Modern caption styling with animations
- Professional video composition and rendering

### **🤖 Intelligent Automation**
- **Dual AI Providers**: OpenAI GPT-4o and Groq Mixtral-8x7b support
- **Smart Video Matching**: AI finds visually relevant background footage
- **Precise Timing**: Word-level caption synchronization
- **Quality Optimization**: Automatic resolution and format selection

### **📈 Production Ready**
- **Async Job Processing**: Handle multiple video generations simultaneously
- **S3 Storage**: Scalable cloud storage for all generated content
- **Error Recovery**: Robust fallback mechanisms
- **API-First Design**: RESTful endpoints for easy integration

## 🔗 **Quick Links**

- **Interactive API Docs**: `http://localhost:8000/docs` (when running locally)
- **Authentication**: All endpoints require `X-API-Key` header
- **Base URL**: `http://localhost:8000` (development) or your deployed URL
- **Status Polling**: All operations are async - poll job status endpoints for results

## 💡 **Common Use Cases**

1. **🎬 YouTube Shorts Generation**: AI-powered short video creation from long-form content
2. **📱 Social Media Automation**: Create TikTok, Instagram Reels, and YouTube Shorts
3. **🎯 Content Repurposing**: Transform long videos into engaging short clips
4. **🔊 Speaker-Focused Content**: Dynamic face tracking and audio-visual correlation
5. **📈 Viral Content Creation**: AI-powered highlight detection for maximum engagement
6. **🎪 Educational Shorts**: Convert educational content into digestible short videos
7. **🎵 Podcast Highlights**: Transform audio content into visual shorts

## 🛠️ **Development**

```bash
# Start the development server
docker-compose up --build

# Or run locally
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 📞 **Getting Started**

1. **Set up Environment Variables**:
   ```bash
   API_KEY=your_secret_api_key_here
   OPENAI_API_KEY=your_openai_key                 # For AI script generation
   OPENAI_BASE_URL=https://api.openai.com/v1      # Optional: For OpenAI-compatible LLMs
   OPENAI_MODEL=gpt-4o                           # Optional: Custom model name
   GROQ_API_KEY=your_groq_key                    # Optional alternative to OpenAI
   GROQ_BASE_URL=http://localhost:8080/v1        # Optional: For Groq-compatible LLMs (NOT needed for official Groq)
   GROQ_MODEL=mixtral-8x7b-32768                 # Optional: Custom Groq model
   PEXELS_API_KEY=your_pexels_key                # For stock video search
   S3_ACCESS_KEY=your_s3_access_key
   S3_SECRET_KEY=your_s3_secret_key
   S3_BUCKET_NAME=your_s3_bucket_name
   ```

2. **Start the API**:
   ```bash
   docker-compose up --build
   ```

3. **Make your first request**:
   ```bash
   # Generate a YouTube Short
   curl -X POST "http://localhost:8000/v1/yt-shorts/" \
     -H "X-API-Key: your_api_key" \
     -H "Content-Type: application/json" \
     -d '{"video_url": "https://www.youtube.com/watch?v=example", "max_duration": 60}'
   
   # Or create a topic-based video
   curl -X POST "http://localhost:8000/v1/ai/footage-to-video" \
     -H "X-API-Key: your_api_key" \
     -H "Content-Type: application/json" \
     -d '{"topic": "amazing ocean facts"}'
   ```

## 📋 **Common Response Formats**

### Job Creation Response
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### Job Status Response
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "result": {
    "final_video_url": "https://s3.../video.mp4",
    "script_generated": "Amazing ocean facts you didn't know...",
    "processing_time": 180.5
  },
  "error": null
}
```

---

*Last updated: January 2025 - Version 2.0 with AI Features* 