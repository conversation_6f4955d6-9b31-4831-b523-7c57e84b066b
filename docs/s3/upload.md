# S3 File Upload

The S3 upload endpoint allows you to upload files to your configured S3 bucket.

## Upload File

Upload a file to S3.

### Endpoint

```
POST /v1/s3/upload
```

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |
| Content-Type | Yes | multipart/form-data |

### Request Body

This endpoint accepts `multipart/form-data`.

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| file | file | Yes | The file to upload. |
| file_name | string | No | An optional file name. If not provided, the original file name will be used. |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

### Example

#### Request

```bash
cURL -X POST \
  https://localhost:8000/v1/s3/upload \
  -H 'Content-Type: multipart/form-data' \
  -H 'X-API-Key: your-api-key' \
  -F 'file=@/path/to/your/file.png' \
  -F 'file_name=custom_name.png'
```

#### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

## Get Job Status

Check the status of an upload job.

### Endpoint

```
GET /v1/s3/upload/{job_id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| job_id | string | Yes | ID of the job to get status for |

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "file_url": "https://your-bucket.s3.your-region.amazonaws.com/custom_name.png"
  },
  "error": null
}
```
