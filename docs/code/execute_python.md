# Python Code Execution

The Python code execution endpoint allows you to execute Python code in a sandboxed environment.

## Execute Python Code

Execute Python code.

### Endpoint

```
POST /v1/code/execute/python
```

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |
| Content-Type | Yes | application/json |

### Request Body

```json
{
  "code": "print('Hello, World!')"
}
```

#### Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| code | string | Yes | The Python code to execute. |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

### Example

#### Request

```bash
cURL -X POST \
  https://localhost:8000/v1/code/execute/python \
  -H 'Content-Type: application/json' \
  -H 'X-API-Key: your-api-key' \
  -d '{
    "code": "import sys; print(f'Python version: {sys.version}')"
  }'
```

#### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000"
}
```

## Get Job Status

Check the status of a code execution job.

### Endpoint

```
GET /v1/code/execute/python/{job_id}
```

### Path Parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| job_id | string | Yes | ID of the job to get status for |

### Headers

| Name | Required | Description |
|------|----------|-------------|
| X-API-Key | Yes | Your API key for authentication |

### Response

```json
{
  "job_id": "j-123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "result": {
    "stdout": "Python version: 3.12.2 (main, Feb  6 2024, 20:19:44) [GCC 11.4.0]",
    "stderr": "",
    "exit_code": 0
  },
  "error": null
}
```

```